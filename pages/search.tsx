import {GetServerSideProps} from 'next';
import {DynamicPage} from '@core/types';
import {trim} from '@core/helpers';
import {initDynamicPageParams} from '@core/helpers/server';
import CatalogPage from '@components/pages/store/Catalog';

export const getServerSideProps: GetServerSideProps = async ctx => {
    let {props, notFound, redirect} = await initDynamicPageParams(ctx);
    props.search = trim((ctx.query.query ?? '').toString());

    return {
        props,
        notFound,
        redirect
    };
};

const SearchPage: DynamicPage<typeof getServerSideProps> = props => {
    const {search, ...rest} = props;

    return <CatalogPage key={search} search={search} {...rest} />;
};

SearchPage.layout = 'default';

export default SearchPage;

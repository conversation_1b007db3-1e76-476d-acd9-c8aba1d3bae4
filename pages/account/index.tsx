import {GetServerSideProps} from 'next';
import {initDynamicPageParams} from '@core/helpers/server';
import {DynamicPage} from '@core/types';
import Account from '@components/pages/account';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    return {
        props,
        notFound,
        redirect
    };
};

const AccountPage: DynamicPage<typeof getServerSideProps> = props => {
    return <Account {...props} />;
};

AccountPage.layout = 'default';

export default AccountPage;

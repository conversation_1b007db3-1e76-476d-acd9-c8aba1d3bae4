import {GetServerSideProps} from 'next';
import {erpClient, initDynamicPageParams} from '@core/helpers/server';
import {DynamicPage} from '@core/types';
import MyCollection from '@components/pages/account/MyCollections/Collection';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    if (!!props.session && props.session) {
        const collections = await erpClient.post('customers/collections', {
            customerId: props.session.user?.id
        });
        const collectionId = ctx.params?.id;
        const collection = (collections ?? []).find(
            (collection: any) => collection.id === collectionId
        );

        if (typeof collection === 'undefined') {
            return {
                props,
                notFound: true,
                redirect
            };
        }

        const result = await erpClient.post('customers/collection-products', {
            customerId: props.session.user?.id,
            collectionId: collection.id,
            limit: 32
        });

        props.collections = collections;
        props.collection = collection;
        props.products = result.data;
        props.total = result.total;
        props.hasNextPage = result.hasNextPage;
    }

    return {
        props,
        notFound,
        redirect
    };
};

const MyCollectionPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <MyCollection key={props.collection?.id} {...props} />;
};

MyCollectionPage.layout = 'default';

export default MyCollectionPage;

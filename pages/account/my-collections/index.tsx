import {GetServerSideProps} from 'next';
import {erpClient, initDynamicPageParams} from '@core/helpers/server';
import {DynamicPage} from '@core/types';
import MyCollections from '@components/pages/account/MyCollections';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    if (!!props.session && props.session) {
        props.collections = await erpClient.post('customers/collections', {
            customerId: props.session.user?.id
        });
    }

    return {
        props,
        notFound,
        redirect
    };
};

const MyCollectionsPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <MyCollections {...props} />;
};

MyCollectionsPage.layout = 'default';

export default MyCollectionsPage;

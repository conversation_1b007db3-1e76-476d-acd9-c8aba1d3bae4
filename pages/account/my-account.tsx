import {GetServerSideProps} from 'next';
import {erpClient, initDynamicPageParams} from '@core/helpers/server';
import {DynamicPage} from '@core/types';
import storeConfig from '~/store.config';
import MyAccount from '@components/pages/account/MyAccount';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    // Get locale.
    const locale = ctx.locale ?? storeConfig.defaultLocale;

    // Get countries.
    props.countries = await erpClient.post('common/countries', {locale});

    return {
        props,
        notFound,
        redirect
    };
};

const MyAccountPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <MyAccount {...props} />;
};

MyAccountPage.layout = 'default';

export default MyAccountPage;

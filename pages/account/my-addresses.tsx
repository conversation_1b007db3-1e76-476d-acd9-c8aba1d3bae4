import {GetServerSideProps} from 'next';
import {erpClient, initDynamicPageParams} from '@core/helpers/server';
import {DynamicPage} from '@core/types';
import storeConfig from '~/store.config';
import MyAddresses from '@components/pages/account/MyAddresses';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    if (!!props.session && props.session) {
        // Get locale.
        const locale = ctx.locale ?? storeConfig.defaultLocale;

        // Get countries.
        props.countries = await erpClient.post('common/countries', {locale});
        const defaultCountry = props.countries.find(
            (country: any) => !!country.isDefault
        );

        // Get initial states.
        props.initialStates = !!defaultCountry
            ? await erpClient.post('common/states', {
                  locale,
                  countryId: defaultCountry.id
              })
            : [];

        // Get initial cities.
        props.initialCities =
            !!defaultCountry &&
            (!Array.isArray(props.initialStates) ||
                props.initialStates.length < 1)
                ? await erpClient.post('common/cities', {
                      locale,
                      countryId: defaultCountry.id,
                      ...(Array.isArray(props.initialStates) &&
                      props.initialStates.length > 0
                          ? {state: props.initialStates[0]}
                          : {})
                  })
                : [];

        // Get Contacts.
        props.contacts = await erpClient.post('customers/contacts', {
            customerId: props.session.user?.id
        });
    }

    return {
        props,
        notFound,
        redirect
    };
};

const MyAddressesPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <MyAddresses {...props} />;
};

MyAddressesPage.layout = 'default';

export default MyAddressesPage;

import {GetServerSideProps} from 'next';
import {erpClient, initDynamicPageParams} from '@core/helpers/server';
import {DynamicPage} from '@core/types';
import MyOrders from '@components/pages/account/MyOrders';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    if (!!props.session && props.session) {
        // Get orders.
        props.orders = await erpClient.post('customers/orders', {
            customerId: props.session.user?.id
        });

        // Payment methods.
        props.paymentMethods = await erpClient.post('checkout/payment-methods');
    }

    return {
        props,
        notFound,
        redirect
    };
};

const MyOrdersPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <MyOrders {...props} />;
};

MyOrdersPage.layout = 'default';

export default MyOrdersPage;

import {GetStaticPropsContext} from 'next';
import {erpClient, loadTranslations} from '@core/helpers/server';
import storeConfig from '~/store.config';
import {UiImage, UiLink} from '@core/components/ui';
import {useTrans} from '@core/hooks';

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const locale = ctx.locale ?? storeConfig.defaultLocale;
    const props: Record<string, any> = {
        storeInfo: await erpClient.post('common/info', {
            locale
        }),
        ...(await loadTranslations(locale))
    };

    return {
        props,
        revalidate: 60 * 60
    };
};

const ContactPage = (props: any) => {
    const t = useTrans();

    const {company} = props.storeInfo;

    return (
        <>
            <header className="shadow-small sticky top-0 z-50 h-header w-full border-b bg-white">
                <div className="flex h-full items-stretch justify-between px-4">
                    <div className="flex items-center">
                        <UiLink href="/" className="h-logo cursor-pointer">
                            <UiImage
                                src="/site-logo.png"
                                alt={storeConfig.title}
                                width={parseFloat(
                                    storeConfig.theme.logoWidth.replace(
                                        'px',
                                        ''
                                    )
                                )}
                                height={parseFloat(
                                    storeConfig.theme.logoHeight.replace(
                                        'px',
                                        ''
                                    )
                                )}
                                priority
                            />
                        </UiLink>
                    </div>
                </div>
            </header>

            <div className="container py-8">
                <div className="mb-8 text-3xl font-semibold">
                    {t('Contact Information')}
                </div>
                <ul className="grid gap-4 text-lg [&_span:first-child]:font-semibold">
                    <li>
                        <span>{t('Company')}: </span>
                        {company?.name}
                    </li>
                    <li>
                        <span>{t('Address')}: </span>
                        {company?.address?.address}
                    </li>
                    <li>
                        <span>{t('Phone')}: </span>
                        {company?.phone}
                    </li>
                    <li>
                        <span>{t('Email')}: </span>
                        {company?.email}
                    </li>
                    <li>
                        <span>{t('Tax Department')}: </span>
                        {company?.taxDepartment}
                    </li>
                    <li>
                        <span>{t('Tax Number')}: </span>
                        {company?.tin}
                    </li>
                </ul>
            </div>
        </>
    );
};

ContactPage.layout = 'empty';

export default ContactPage;

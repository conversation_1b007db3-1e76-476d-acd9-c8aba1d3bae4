import {GetServerSideProps} from 'next';
import {initDynamicPageParams} from '@core/helpers/server';
import {DynamicPage} from '@core/types';
import MyOrders from '@components/pages/common/MyOrders';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    return {
        props,
        notFound,
        redirect
    };
};

const MyOrdersPage: DynamicPage<typeof getServerSideProps> = props => {
    return <MyOrders />;
};

MyOrdersPage.layout = 'default';

export default MyOrdersPage;

import {GetStaticPropsContext} from 'next';
import {useTrans} from '@core/hooks';
import {erpClient, loadTranslations} from '@core/helpers/server';
import storeConfig from '~/store.config';

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const locale = ctx.locale ?? storeConfig.defaultLocale;
    const props = {
        storeInfo: await erpClient.post('common/info', {
            locale
        }),
        ...(await loadTranslations(locale))
    };

    return {props};
};

const NotFoundPage = () => {
    const t = useTrans();

    return (
        <div className="container flex min-h-screen flex-1 flex-col items-center justify-center py-12 xl:px-12 xl:py-24">
            <div className="flex h-40 w-40 items-center justify-center rounded-lg border border-dashed border-gray-500 text-gray-500">
                <p className="text-6xl">404</p>
            </div>

            <h1 className="pt-8 text-center text-2xl font-semibold">
                {t('Sorry, We can not find the page you are looking for.')}
            </h1>

            <p className="px-10 pt-2 text-center text-muted xl:w-8/12">
                {t(
                    'Please change your search criteria and try again. If still not finding anything relevant, please visit the Home page and try out some of our bestsellers!'
                )}
            </p>
        </div>
    );
};

NotFoundPage.layout = 'empty';

export default NotFoundPage;

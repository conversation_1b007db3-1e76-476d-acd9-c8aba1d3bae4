import {GetServerSideProps} from 'next';
import {DynamicPage} from '@core/types';
import {initDynamicPageParams} from '@core/helpers/server';
import MyLedger from '@components/pages/common/MyLedger';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    return {
        props,
        notFound,
        redirect
    };
};

const MyLedgerPage: DynamicPage<typeof getServerSideProps> = props => {
    return <MyLedger />;
};

MyLedgerPage.layout = 'default';

export default MyLedgerPage;

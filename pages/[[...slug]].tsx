import {memo} from 'react';
import {GetServerSidePropsContext} from 'next';
import {isDev} from '@core/helpers';
import type {DynamicPage} from '@core/types';
import {initDynamicPageParams} from '@core/helpers/server';
import CatalogPage from '@components/pages/store/Catalog';
import ProductPage from '@core/pages/store/Product';
import {useRouter} from 'next/router';

export const getServerSideProps = async (ctx: GetServerSidePropsContext) => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    if (ctx.resolvedUrl === '/') {
        return {
            props,
            notFound,
            redirect: {
                destination: '/home',
                permanent: false
            }
        };
    }

    return {
        props,
        notFound,
        redirect
    };
};

const DynamicPage: DynamicPage<typeof getServerSideProps> = memo(
    ({pageType, ...rest}) => {
        const router = useRouter();

        if (pageType === 'catalog') {
            return <CatalogPage {...rest} />;
        } else if (pageType === 'product') {
            // @ts-ignore
            return <ProductPage key={router.asPath} {...rest} />;
        }

        return null;
    }
);

if (isDev) {
    DynamicPage.displayName = 'DynamicPage';
}

DynamicPage.layout = 'default';

export default DynamicPage;

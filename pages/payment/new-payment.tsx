import {GetServerSideProps} from 'next';
import {DynamicPage} from '@core/types';
import {initDynamicPageParams} from '@core/helpers/server';
import NewPayment from '@components/pages/common/Payment/NewPayment';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    return {
        props,
        notFound,
        redirect
    };
};

const NewPaymentPage: DynamicPage<typeof getServerSideProps> = () => {
    return <NewPayment />;
};

NewPaymentPage.layout = 'default';

export default NewPaymentPage;

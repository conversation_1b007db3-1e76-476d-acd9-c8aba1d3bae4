import {GetServerSideProps} from 'next';
import {DynamicPage} from '@core/types';
import {initDynamicPageParams} from '@core/helpers/server';
import Payment from '@components/pages/common/Payment/home';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    return {
        props,
        notFound,
        redirect
    };
};

const PaymentPage: DynamicPage<typeof getServerSideProps> = () => {
    return <Payment />;
};

PaymentPage.layout = 'default';

export default PaymentPage;

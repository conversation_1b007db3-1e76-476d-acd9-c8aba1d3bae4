import {GetServerSideProps} from 'next';
import {DynamicPage} from '@core/types';
import {initDynamicPageParams} from '@core/helpers/server';
import ThankYou from '@components/pages/common/Payment/ThankYou';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    return {
        props,
        notFound,
        redirect
    };
};

const ThankYouPage: DynamicPage<typeof getServerSideProps> = () => {
    return <ThankYou />;
};

ThankYouPage.layout = 'default';

export default ThankYouPage;

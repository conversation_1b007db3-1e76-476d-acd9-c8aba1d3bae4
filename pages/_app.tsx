import '@core/assets/css/main.css';
import {FC, Fragment, memo, PropsWithChildren, useEffect, useMemo} from 'react';
import dynamic from 'next/dynamic';
import {appWithTranslation} from 'next-i18next';
import {SessionProvider} from 'next-auth/react';
import type {AppProps} from '@core/types';
import {Toaster} from 'sonner';
import {isDev} from '@core/helpers';
import {useViewportSize} from '@core/hooks';
import {
    CartProvider,
    LayoutProvider,
    MobileProvider,
    StoreProvider,
    UiProvider
} from '@core/context';
import UiDialogs from '@core/context/UiContext/Dialogs';
import Head from '@components/common/Head';
import Font from '@components/common/Font';

const AuthLayout = dynamic(() => import('@components/layouts/Auth'));
import DefaultLayout from '@components/layouts/Default';
import EmptyLayout from '@components/layouts/Empty';
import {Cookies} from 'react-cookie-consent';

// Prepare layouts
const layouts: Record<string, any> = {
    auth: AuthLayout,
    default: DefaultLayout,
    empty: EmptyLayout
};

const Analytics = dynamic(() => import('@components/common/Analytics'), {
    ssr: false
});

const ScrollToTop = dynamic(() => import('@components/common/ScrollToTop'), {
    ssr: false
});

let CookieConsent: any = Fragment;
if (Cookies.get('enterstore-cookie-consent') === undefined) {
    CookieConsent = dynamic(() => import('@components/common/CookieConsent'), {
        ssr: false
    });
}

const App: FC<AppProps> = memo(({Component, pageProps}) => {
    // Get storeInfo.
    const storeInfo = useMemo(() => pageProps.storeInfo, [pageProps.storeInfo]);

    // Get session.
    const session = useMemo(() => pageProps.session, [pageProps.session]);

    // Get active navigation item.
    const navigationItem = useMemo(
        () => pageProps.navigationItem,
        [pageProps.navigationItem]
    );

    // Get page type.
    const pageType = useMemo(
        () => pageProps.pageType ?? 'other',
        [pageProps.pageType]
    );

    // Get slug.
    const slug = useMemo(() => pageProps.slug, [pageProps.slug]);

    // Get cart
    const cart = useMemo(() => pageProps.cart, [pageProps.cart]);

    // Determine the layout.
    let Layout: FC<PropsWithChildren<{pageProps: any}>> = DefaultLayout;
    if (typeof Component.layout === 'string') {
        Layout = layouts[Component.layout] ?? DefaultLayout;
    }

    const {width} = useViewportSize();

    // Hide loading.
    useEffect(() => {
        document.body.classList?.remove('loading');

        if (pageType !== 'product' && width < 1280) {
            document.getElementsByTagName('html')[0].style.minHeight = '100%';
            document.getElementsByTagName('html')[0].style.height = 'auto';
            document.getElementsByTagName('html')[0].style.overflow = 'auto';
            document.getElementsByTagName('body')[0].style.minHeight = '100%';
            document.getElementsByTagName('body')[0].style.height = 'auto';
            document.getElementsByTagName('body')[0].style.overflow = 'auto';
            document.getElementById('__next')!.style.minHeight = '100%';
            document.getElementById('__next')!.style.height = 'auto';
            document.getElementById('__next')!.style.overflow = 'auto';
        }

        return () => {
            document.getElementsByTagName('html')[0].style.minHeight = '';
            document.getElementsByTagName('html')[0].style.height = '';
            document.getElementsByTagName('html')[0].style.overflow = '';
            document.getElementsByTagName('body')[0].style.minHeight = '';
            document.getElementsByTagName('body')[0].style.height = '';
            document.getElementsByTagName('body')[0].style.overflow = '';
            document.getElementById('__next')!.style.minHeight = '';
            document.getElementById('__next')!.style.height = '';
            document.getElementById('__next')!.style.overflow = '';
        };
    }, [pageType, width]);

    return (
        <>
            <Head />
            <Font />

            <SessionProvider session={session}>
                <UiProvider>
                    <StoreProvider
                        info={storeInfo}
                        navigationItem={navigationItem}
                        pageType={pageType}
                        slug={slug}
                    >
                        <CartProvider cart={cart}>
                            <LayoutProvider>
                                <MobileProvider>
                                    <Layout pageProps={pageProps}>
                                        <Component {...pageProps} />
                                        <ScrollToTop />
                                    </Layout>
                                    <UiDialogs />
                                </MobileProvider>
                            </LayoutProvider>
                        </CartProvider>
                    </StoreProvider>
                </UiProvider>
                <Analytics />
            </SessionProvider>

            <Toaster
                position="top-right"
                toastOptions={{duration: 3000, closeButton: true}}
                className="[&>li]:!shadow-sm"
                offset={20}
            />
        </>
    );
});

if (isDev) {
    App.displayName = 'App';
}

export default appWithTranslation(App);

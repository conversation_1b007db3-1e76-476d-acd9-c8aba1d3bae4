import {GetServerSideProps} from 'next';
import {DynamicPage} from '@core/types';
import {initDynamicPageParams} from '@core/helpers/server';

import Categories from '@components/pages/mobile/Categories';

export const getServerSideProps: GetServerSideProps = async ctx => {
    let {props, notFound, redirect} = await initDynamicPageParams(ctx);

    const params = ctx.params;
    if (
        typeof params !== 'undefined' &&
        Array.isArray(params.id) &&
        params.id.length > 0
    ) {
        props.currentId = params.id[0];
    }

    return {
        props,
        notFound,
        redirect
    };
};

const CategoriesPage: DynamicPage<typeof getServerSideProps> = props => {
    return <Categories {...props} />;
};

CategoriesPage.layout = 'empty';

export default CategoriesPage;

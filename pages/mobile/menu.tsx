import {GetServerSideProps} from 'next';
import {DynamicPage} from '@core/types';
import {initDynamicPageParams} from '@core/helpers/server';

import MobileMenu from '@components/pages/mobile/Menu';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    return {
        props,
        notFound,
        redirect
    };
};

const MobileMenuPage: DynamicPage<typeof getServerSideProps> = props => {
    return <MobileMenu {...props} />;
};

MobileMenuPage.layout = 'empty';

export default MobileMenuPage;

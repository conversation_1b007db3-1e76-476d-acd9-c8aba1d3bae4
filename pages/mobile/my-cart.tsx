import {GetServerSideProps} from 'next';
import {DynamicPage} from '@core/types';
import {initDynamicPageParams} from '@core/helpers/server';

import MobileMyCart from '@components/pages/mobile/MyCart';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    return {
        props,
        notFound,
        redirect
    };
};

const MobileMyCartPage: DynamicPage<typeof getServerSideProps> = props => {
    return <MobileMyCart {...props} />;
};

MobileMyCartPage.layout = 'empty';

export default MobileMyCartPage;

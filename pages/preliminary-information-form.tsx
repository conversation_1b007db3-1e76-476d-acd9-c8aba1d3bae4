import {GetStaticPropsContext} from 'next';
import {erpClient, loadTranslations} from '@core/helpers/server';
import storeConfig from '~/store.config';
import {UiImage, UiLink} from '@core/components/ui';

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const locale = ctx.locale ?? storeConfig.defaultLocale;
    const props: Record<string, any> = {
        storeInfo: await erpClient.post('common/info', {
            locale
        }),
        ...(await loadTranslations(locale))
    };

    props.preliminaryInformationForm = (
        await erpClient.post('common/text', {
            type: 'preliminary-information-form',
            locale
        })
    ).content;

    return {
        props,
        revalidate: 60 * 60
    };
};

const ServiceAgreementPage = (props: any) => {
    return (
        <>
            <header className="shadow-small sticky top-0 z-50 h-header w-full border-b bg-white">
                <div className="flex h-full items-stretch justify-between px-4">
                    <div className="flex items-center">
                        <UiLink href="/" className="h-logo cursor-pointer">
                            <UiImage
                                src="/site-logo.png"
                                alt={storeConfig.title}
                                width={parseFloat(
                                    storeConfig.theme.logoWidth.replace(
                                        'px',
                                        ''
                                    )
                                )}
                                height={parseFloat(
                                    storeConfig.theme.logoHeight.replace(
                                        'px',
                                        ''
                                    )
                                )}
                                priority
                            />
                        </UiLink>
                    </div>
                </div>
            </header>

            <div className="container py-4">
                <div
                    className="prose max-w-none"
                    dangerouslySetInnerHTML={{
                        __html: props.preliminaryInformationForm
                    }}
                />
            </div>
        </>
    );
};

ServiceAgreementPage.layout = 'empty';

export default ServiceAgreementPage;

import {GetServerSideProps} from 'next';
import {DynamicPage} from '@core/types';
import {initDynamicPageParams} from '@core/helpers/server';
import QuickOrder from '@components/pages/common/QuickOrder';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    return {
        props,
        notFound,
        redirect
    };
};

const QuickOrderPage: DynamicPage<typeof getServerSideProps> = () => {
    return <QuickOrder />;
};

QuickOrderPage.layout = 'default';

export default QuickOrderPage;

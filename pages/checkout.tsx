import {GetServerSideProps} from 'next';
import Cookies from 'cookies';
import {DynamicPage} from '@core/types';
import {erpClient, initDynamicPageParams} from '@core/helpers/server';
import Checkout from '@components/pages/store/Checkout';
import storeConfig from '~/store.config';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    const cookies = new Cookies(ctx.req, ctx.res);
    const cartId = cookies.get('cart-id');
    // @ts-ignore
    const customerId = props.session?.user.id;

    if (typeof cartId === 'string' && !!cartId) {
        let cart = null;

        try {
            cart = await erpClient.post('cart', {cartId, reInit: true});
        } catch (error: any) {}

        if (!!cart) {
            props.cart = cart;
        }
    } else if (typeof customerId === 'string') {
        let cart = null;

        try {
            cart = await erpClient.post('cart', {customerId, reInit: true});
        } catch (error: any) {}

        if (!!cart) {
            props.cart = cart;
        }
    }

    if (!props.cart) {
        return {
            props: {},
            notFound: false,
            redirect: {
                destination: '/cart',
                permanent: false
            }
        };
    }

    if (
        props.cart.items.filter(
            (item: any) =>
                item.selected &&
                !item.removed &&
                item.quantity > 0 &&
                item.productStockQuantity >= item.quantity
        ).length < 1 ||
        !(props.cart.subTotal > 0)
    ) {
        return {
            props: {},
            notFound: false,
            redirect: {
                destination: '/cart',
                permanent: false
            }
        };
    }

    // Get locale.
    const locale = ctx.locale ?? storeConfig.defaultLocale;

    // Get countries.
    props.countries = await erpClient.post('common/countries', {locale});
    const defaultCountry = (props.countries || []).find(
        (country: any) => !!country.isDefault
    );

    // Get initial states.
    props.initialStates = await erpClient.post('common/states', {
        locale,
        countryId: defaultCountry.id
    });

    // Get initial cities.
    props.initialCities = await erpClient.post('common/cities', {
        locale,
        countryId: defaultCountry.id,
        ...(Array.isArray(props.initialStates) && props.initialStates.length > 0
            ? {state: props.initialStates[0]}
            : {})
    });

    // Delivery options.
    props.deliveryOptions = await erpClient.post('checkout/delivery-options', {
        cartId
    });

    // Payment methods.
    props.paymentMethods = await erpClient.post('checkout/payment-methods', {
        cartId,
        erpUserId: props.session?.user?.erpUserId
    });

    // Contacts.
    if (!!props.session && props.session) {
        props.contacts = await erpClient.post('customers/contacts', {
            customerId: props.session.user?.id
        });
    }

    return {
        props,
        notFound,
        redirect
    };
};

const CheckoutPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <Checkout {...props} />;
};

CheckoutPage.layout = 'default';

export default CheckoutPage;

import {GetServerSideProps} from 'next';
import {erpClient, initDynamicPageParams} from '@core/helpers/server';
import {DynamicPage} from '@core/types';
import PreviousOrders from '@components/pages/common/PreviousOrders';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    props.orders = await erpClient.post('customers/orders', {
        customerId: props.session?.user?.id
    });

    props.paymentMethods = await erpClient.post('checkout/payment-methods');

    return {
        props,
        notFound,
        redirect
    };
};

const PreviousOrdersPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <PreviousOrders {...props} />;
};

PreviousOrdersPage.layout = 'default';

export default PreviousOrdersPage;

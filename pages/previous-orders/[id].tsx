import {GetServerSideProps} from 'next';
import {erpClient, initDynamicPageParams} from '@core/helpers/server';
import {DynamicPage} from '@core/types';
import Order from '@components/pages/common/PreviousOrders/Order';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    props.order = await erpClient.post('customers/order', {
        cartId: ctx.params?.id
    });

    props.deliveryOptions = await erpClient.post('checkout/delivery-options', {
        cartId: props.order.id
    });

    props.paymentMethods = await erpClient.post('checkout/payment-methods', {
        cartId: props.order.id
    });

    return {
        props,
        notFound,
        redirect
    };
};

const OrderPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <Order {...props} />;
};

OrderPage.layout = 'default';

export default OrderPage;

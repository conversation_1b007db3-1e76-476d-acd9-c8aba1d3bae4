import {GetServerSideProps} from 'next';
import {initDynamicPageParams} from '@core/helpers/server';
import {DynamicPage} from '@core/types';
import MyInvoices from '@components/pages/common/MyInvoices';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    return {
        props,
        notFound,
        redirect
    };
};

const MyInvoicesPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <MyInvoices {...props} />;
};

MyInvoicesPage.layout = 'default';

export default MyInvoicesPage;

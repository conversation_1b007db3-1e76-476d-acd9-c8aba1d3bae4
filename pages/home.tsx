import {GetServerSideProps} from 'next';
import {DynamicPage} from '@core/types';
import {initDynamicPageParams} from '@core/helpers/server';
import Home from '@components/pages/common/Home';
import NewPayment from '@components/pages/common/Payment/NewPayment';
import storeConfig from '~/store.config';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx);

    return {
        props,
        notFound,
        redirect
    };
};

const HomePage: DynamicPage<typeof getServerSideProps> = props => {
    return storeConfig.paymentOnly ? (
        <NewPayment />
    ) : (
        <Home productCatalogMap={[]} {...props} />
    );
};

HomePage.layout = 'default';

export default HomePage;

import {GetServerSideProps} from 'next';
import {initDynamicPageParams} from '@core/helpers/server';
import {DynamicPage} from '@core/types';
import Auth from '@components/pages/auth/Auth';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound} = await initDynamicPageParams(ctx, {
        isSecure: false
    });

    if (!!props.session) {
        return {
            props,
            notFound,
            redirect: {
                destination: '/home',
                permanent: false
            }
        };
    }

    return {
        props,
        notFound
    };
};

const AuthPage: DynamicPage<typeof getServerSideProps> = props => {
    return <Auth {...props} />;
};

AuthPage.layout = 'auth';

export default AuthPage;

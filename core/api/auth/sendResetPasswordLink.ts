import {endpoint, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient} = ctx;
    const {email, locale} = req.body;

    await validator
        .object()
        .shape({
            email: validator.string().email().required()
        })
        .isValid({
            email
        });

    try {
        await erpClient.post('customers/send-reset-password-link', {
            email,
            locale
        });
    } catch (error: any) {
        return res.status(422).json({
            code: error.code,
            message: error.message,
            field: error.field
        });
    }

    return res.status(200).json({});
});

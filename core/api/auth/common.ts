import NextAuth, {NextAuthOptions} from 'next-auth';
import credentialsProvider from 'next-auth/providers/credentials';
import {compare} from 'bcryptjs';
import {erpClient, redis} from '@core/helpers/server';
import Cookies from 'cookies';
import {trim} from '@core/helpers';

export const authOptions: NextAuthOptions = {
    secret: 'oRfzaDHsy8lCN0OW2iqzWrMYgVeP0JUT',
    session: {
        strategy: 'jwt'
    },
    callbacks: {
        jwt({token, user}) {
            if (typeof user === 'object') {
                token.id = user.id;
                token.email = user.email;
                // @ts-ignore
                token.erpUserId = user.erpUserId;
            }

            return token;
        },
        async session({session, token}): Promise<any> {
            if (typeof token === 'object' && token !== null) {
                const customerKey = `customer-${token.id}`;
                const cachedCustomer = await redis.get(customerKey);
                let customer: Record<string, any> = {};

                if (cachedCustomer !== null) {
                    customer = JSON.parse(cachedCustomer);
                } else {
                    customer = await erpClient.post('customers/customer', {
                        email: token.email
                    });

                    await redis.set(
                        customerKey,
                        JSON.stringify(customer),
                        'EX',
                        60 * 60 * 24
                    );
                }

                const u: Record<string, any> = {};
                u.id = customer.id;
                u.name = customer.name;
                u.firstName = customer.firstName;
                u.lastName = customer.lastName;
                u.email = customer.email;
                u.phoneNumber = customer.phoneNumber;
                u.phoneCode = customer.phoneCode;
                u.phoneCountryCode = customer.phoneCountryCode;
                u.erpUserId = token.erpUserId;

                session.user = u;
            }

            return session;
        }
    },
    events: {
        signOut: async ({token}) => {
            const customerKey = `customer-${token.id}`;

            try {
                await redis.del(customerKey);
            } catch (error) {}
        }
    },
    providers: [
        credentialsProvider({
            credentials: {
                email: {label: 'Email address', type: 'text', placeholder: ''},
                password: {label: 'Password', type: 'password'}
            },
            async authorize(credentials, req) {
                let result = null;
                try {
                    result = await erpClient.post('customers/customer', {
                        email: credentials?.email.toLowerCase()
                    });

                    const customerKey = `customer-${result.id}`;
                    await redis.set(
                        customerKey,
                        JSON.stringify(result),
                        'EX',
                        60 * 60 * 24
                    );

                    // Set existing cart customer.
                    const cookies = new Cookies(req as any, {} as any);
                    let cartId: string | undefined = trim(
                        cookies.get('cart-id') || ''
                    );
                    if (cartId === '') {
                        cartId = undefined;
                    }
                    if (!!cartId) {
                        try {
                            await erpClient.post('cart/set-customer', {
                                cartId,
                                customerId: result.id
                            });
                        } catch (error: any) {}
                    }
                } catch (error: any) {
                    if (error.code === 'email_is_invalid') {
                        throw new Error('Email is invalid');
                    }

                    throw new Error(
                        'Sign in information is incorrect. Please check your e-mail address and password and try again.'
                    );
                }

                if (!result.erpUserId) {
                    const erpUserId: string | undefined = trim(
                        // @ts-ignore
                        credentials?.erpUserId || ''
                    );

                    if (!!erpUserId) {
                        const erpUserPassword = await redis.get(
                            `user-passwords-${erpUserId}`
                        );

                        if (!!erpUserPassword) {
                            result.erpUserId = erpUserId;
                            result.erpUserPassword = erpUserPassword;
                        }
                    }
                }

                if (
                    !(await compare(
                        credentials?.password as string,
                        result.password ?? ''
                    ))
                ) {
                    if (!!result.erpUserPassword) {
                        // @ts-ignore
                        if (credentials?.changeCustomer) {
                            if (
                                credentials?.password !== result.password ??
                                ''
                            ) {
                                throw new Error(
                                    'Sign in information is incorrect. Please check your e-mail address and password and try again.'
                                );
                            }
                        } else {
                            if (
                                !(await compare(
                                    credentials?.password as string,
                                    result.erpUserPassword ?? ''
                                ))
                            ) {
                                throw new Error(
                                    'Sign in information is incorrect. Please check your e-mail address and password and try again.'
                                );
                            }
                        }
                    } else {
                        throw new Error(
                            'Sign in information is incorrect. Please check your e-mail address and password and try again.'
                        );
                    }
                }

                if (!!result.erpUserId && !!result.erpUserPassword) {
                    await redis.set(
                        `user-passwords-${result.erpUserId}`,
                        result.erpUserPassword
                    );
                }

                return {
                    id: result.id,
                    email: result.email,
                    erpUserId: result.erpUserId
                };
            }
        })
    ]
};

export default NextAuth(authOptions);

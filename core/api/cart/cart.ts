import Cookies from 'cookies';
import {trim} from '@core/helpers';
import {endpoint, erpClient} from '@core/helpers/server';
import {authOptions} from '../auth/common';
import {getServerSession} from 'next-auth';

export default endpoint.post(async (ctx, req, res) => {
    const cookies = new Cookies(req, res);
    const cartId = cookies.get('cart-id');

    if (typeof cartId === 'string' && trim(cartId) !== '') {
        let cart = null;

        try {
            cart = await erpClient.post('cart', {cartId});
        } catch (error: any) {}

        if (!!cart) {
            return res.json(cart);
        }
    }

    const session = await getServerSession(req, res, authOptions);
    if (typeof session === 'object' && session !== null) {
        let cart = null;

        try {
            cart = await erpClient.post('cart', {
                // @ts-ignore
                customerId: session.user.id
            });

            const date = new Date();

            cookies.set('cart-id', cart.id, {
                httpOnly: false,
                expires: new Date(date.setMonth(date.getMonth() + 1))
            });
        } catch (error: any) {}

        if (!!cart) {
            return res.json(cart);
        }
    }

    res.json({
        status: 'draft',
        step: 'information',
        subTotal: 0,
        discountTotal: 0,
        taxTotal: 0,
        deliveryTotal: 0,
        cashOnDeliveryServiceFee: 0,
        grandTotal: 0,
        itemCount: 0,
        productCount: 0,
        discounts: [],
        items: [],
        deliveryType: 'standard'
    });
});

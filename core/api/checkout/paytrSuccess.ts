import {endpoint} from '@core/helpers/server';
import {template} from '@core/helpers';
import storeConfig from '~/store.config';

export default endpoint.get(async (ctx, req, res) => {
    const {code} = req.query;

    res.removeHeader('X-DNS-Prefetch-Control');
    res.removeHeader('Strict-Transport-Security');
    res.removeHeader('X-XSS-Protection');
    res.removeHeader('X-Frame-Options');
    res.removeHeader('X-Content-Type-Options');
    res.removeHeader('Referrer-Policy');

    if (typeof code !== 'string' || code.length < 2) {
        return res.status(404).send('Not found!');
    }

    let html = '';
    try {
        html = template(
            `
<!DOCTYPE html>
<html lang='en'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <meta http-equiv='X-UA-Compatible' content='ie=edge'>
        <meta name='robots' content='noindex, nofollow'>
        <title>Payment Success</title>
        <style>
            html, body {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 100%;
                margin: 0;
            }

            .loader {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                display: block;
                margin: 15px auto;
                position: relative;
                color: {{color}};
                box-sizing: border-box;
                animation: animloader 1s linear infinite;
            }

            @keyframes animloader {
                0% {
                    box-shadow: 14px 0 0 -2px, 38px 0 0 -2px, -14px 0 0 -2px, -38px 0 0 -2px;
                }
                25% {
                    box-shadow: 14px 0 0 -2px, 38px 0 0 -2px, -14px 0 0 -2px, -38px 0 0 2px;
                }
                50% {
                    box-shadow: 14px 0 0 -2px, 38px 0 0 -2px, -14px 0 0 2px, -38px 0 0 -2px;
                }
                75% {
                    box-shadow: 14px 0 0 2px, 38px 0 0 -2px, -14px 0 0 -2px, -38px 0 0 -2px;
                }
                100% {
                    box-shadow: 14px 0 0 -2px, 38px 0 0 2px, -14px 0 0 -2px, -38px 0 0 -2px;
                }
            }
        </style>
    </head>
    <body>
        <span class='loader'></span>

        <script>
            window.onload = () => {
                try {
                    const payload = JSON.stringify({
                        status: 'success'
                    });
                    window.parent.postMessage(payload, '*');
                } catch (error) {
                }
            };
        </script>
    </body>
</html>
            `.trim(),
            {
                color: storeConfig.theme.colors.primary['600']
            }
        );
    } catch (error: any) {
        return res.status(400).send(error.message);
    }

    return res.send(html);
});

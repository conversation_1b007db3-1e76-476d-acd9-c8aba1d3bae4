import {randomUUID} from 'crypto';
import {endpoint, erpClient} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {customer} = ctx;
    const {amount} = req.body;

    if (typeof amount !== 'number') throw new Error('Amount must be a number');

    const result = await erpClient.post('checkout/installments', {
        customerId: customer?.id,
        amount
    });

    const installmentCounts = Array.from({length: 12}, (_, i) => i + 1);

    for (let i = 0; i < result.length; i++) {
        const {installments} = result[i];
        const updatedInstallments = installmentCounts.map(count => {
            const installment = installments.find(
                (installment: any) => installment.installmentCount === count
            );
            return installment
                ? {id: randomUUID(), isChecked: false, ...installment}
                : {
                      id: randomUUID(),
                      installmentAmount: 0,
                      installmentCount: count,
                      plusInstallmentCount: 0,
                      isChecked: false,
                      installmentRate: 0,
                      total: 0
                  };
        });
        result[i].installments = updatedInstallments;
    }

    return res.json(result);
});

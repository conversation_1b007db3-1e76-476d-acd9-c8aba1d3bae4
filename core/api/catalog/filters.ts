import {endpoint} from '@core/helpers/server';

type Payload = {
    categoryPaths: string[];
    groupIds: string[];
    brandIds: string[];
    set?: string;
    extraQuery: Record<string, any>;
    search?: string;
};

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient} = ctx;
    const {
        categoryPaths,
        groupIds,
        brandIds,
        set,
        extraQuery,
        search
    }: Payload = req.body;

    const result = await erpClient.post('catalog/filters', {
        categoryPaths,
        groupIds,
        brandIds,
        set,
        extraQuery,
        search
    });

    return res.json(result);
});

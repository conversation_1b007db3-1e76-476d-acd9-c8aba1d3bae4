import {endpoint} from '@core/helpers/server';
import storeConfig from '~/store.config';

type Sort = {
    field: string;
    direction: 'asc' | 'desc';
};

type Payload = {
    categoryPaths: string[];
    groupIds: string[];
    brandIds: string[];
    set?: string;
    selectedAttributes?: Record<string, any>;
    extraQuery: Record<string, any>;
    search?: string;
    skip?: number;
    limit?: number;
    sort?: Sort | Sort[];
    paginated: boolean;
    barcodes?: string[];
};

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {
        categoryPaths,
        groupIds,
        brandIds,
        set,
        selectedAttributes,
        extraQuery,
        search,
        skip,
        limit,
        sort,
        paginated,
        barcodes
    }: Payload = req.body;

    // Prepare sort.
    let sortQuery: Record<string, number> = {};
    if (Array.isArray(sort)) {
        for (const s of sort) {
            sortQuery[s.field] = s.direction === 'asc' ? 1 : -1;
        }
    } else if (typeof sort === 'object' && sort !== null) {
        sortQuery[sort.field] = sort.direction === 'asc' ? 1 : -1;
    } else {
        sortQuery = {updatedAt: -1};
    }

    const result = await erpClient.post('catalog/products', {
        categoryPaths,
        groupIds,
        brandIds,
        selectedAttributes,
        set,
        extraQuery,
        search,
        fields: storeConfig.catalog.productListItemFields,
        skip: skip ?? 0,
        limit:
            typeof limit === 'number'
                ? limit
                : storeConfig.catalog.productsPerPage || 48,
        sort: sortQuery,
        paginated,
        barcodes,
        ...(typeof customer !== 'undefined' ? {customerId: customer.id} : {})
    });

    if (paginated) {
        return res.send({
            products: result.products,
            hasNextPage: result.hasNextPage,
            totalProductCountText: result.totalProductCountText
        });
    }

    return res.json(result);
});

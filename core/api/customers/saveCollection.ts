import {endpoint, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {collectionId, name} = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    const customerId = customer.id;

    await validator
        .object()
        .shape({
            customerId: validator.string().required(),
            collectionId: validator.string().notRequired(),
            name: validator.string().required()
        })
        .isValid({
            customerId,
            collectionId,
            name
        });

    return res.json(
        await erpClient.post('customers/save-collection', {
            customerId,
            collectionId,
            name
        })
    );
}, true);

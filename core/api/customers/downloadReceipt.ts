import {endpoint} from '@core/helpers/server';

const FINANCE_API_URL = process.env.NEXT_PUBLIC_API_URL?.replace('/store', '/finance');

export default endpoint.post(async (ctx, req, res) => {
    const {entryId} = req.body;

    const response = await fetch(`${FINANCE_API_URL}/download-receipt`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({entryId})
    });
    const data = await response.json();

    return res.json({receiptUrl: data.url});
}, true);

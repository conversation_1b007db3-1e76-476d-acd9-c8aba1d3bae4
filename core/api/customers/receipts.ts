import {endpoint} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    const {skip = 0, limit = 10} = req.body;

    return res.json(
        await erpClient.post('customers/receipts', {
            customerId: customer.id,
            limit,
            skip
        })
    );
}, true);

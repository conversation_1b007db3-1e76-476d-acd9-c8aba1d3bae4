import {number, object, string} from 'yup';
import {endpoint} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient} = ctx;

    const customersSchema = object({
        erpUserId: string().required(),
        search: string().notRequired(),
        skip: number().notRequired().integer(),
        limit: number().notRequired().integer()
    });

    const payload = await customersSchema.validate(req.body);

    const result = await erpClient.post('customers/customers', {
        ...payload
    });

    return res.json(result);
}, true);

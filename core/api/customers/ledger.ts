import {endpoint} from '@core/helpers/server';

type Payload = {
    skip?: number;
    limit?: number;
    page?: number;
};

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {skip = 0, limit = 10} = req.body as Payload;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    return res.json(
        await erpClient.post('customers/ledger', {
            customerId: customer.id,
            skip,
            limit
        })
    );
}, true);

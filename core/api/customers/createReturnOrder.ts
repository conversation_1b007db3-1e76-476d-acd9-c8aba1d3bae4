import {endpoint, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {
        locale,
        shipmentTackingCode,
        productId,
        quantity,
        reasonId,
        description
    } = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    await validator
        .object()
        .shape({
            locale: validator.string().required(),
            shipmentTackingCode: validator.string().required(),
            productId: validator.string().required(),
            quantity: validator.number().required(),
            reasonId: validator.string().required(),
            description: validator.string().notRequired().max(1000)
        })
        .isValid({
            locale,
            shipmentTackingCode,
            productId,
            quantity,
            reasonId,
            description
        });

    return res.json(
        await erpClient.post('customers/create-return-order', {
            locale,
            shipmentTackingCode,
            productId,
            quantity,
            reasonId,
            description
        })
    );
}, true);

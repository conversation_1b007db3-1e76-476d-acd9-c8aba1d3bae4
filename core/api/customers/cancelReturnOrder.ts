import {endpoint, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {locale, orderId, productId} = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    await validator
        .object()
        .shape({
            locale: validator.string().required(),
            orderId: validator.string().required(),
            productId: validator.string().required()
        })
        .isValid({
            locale,
            orderId,
            productId
        });

    return res.json(
        await erpClient.post('customers/cancel-return-order', {
            locale,
            orderId,
            productId
        })
    );
}, true);

import {endpoint, erpClient} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient} = ctx;
    
    try {
        const exchangeRates = await erpClient.post('customers/exchange-rates', {});
        
        return res.json(exchangeRates);
    } catch (error: any) {
        // Fallback exchange rates if API fails
        const fallbackRates = {
            USD: 27.5,
            EUR: 30.2,
            GBP: 35.1,
            TL: 1
        };
        
        return res.json({
            rates: fallbackRates,
            baseCurrency: 'TL',
            lastUpdated: new Date().toISOString()
        });
    }
});

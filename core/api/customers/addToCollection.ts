import {endpoint} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {collectionId, productId, isBuyLater, isAlarm} = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    const customerId = customer.id;

    return res.json(
        await erpClient.post('customers/add-to-collection', {
            customerId,
            collectionId,
            productId,
            isBuyLater,
            isAlarm
        })
    );
}, true);

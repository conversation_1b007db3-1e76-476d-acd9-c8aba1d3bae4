import {endpoint, erpClient, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {customer} = ctx;

    const paymentSchema = validator.object({
        amount: validator.number().required(),
        installmentCount: validator.number().required(),
        cardBrand: validator.string(),
        cardNumber: validator.string().required(),
        cardHolder: validator.string().required(),
        expireYear: validator.number().required(),
        expireMonth: validator.number().required(),
        cvv: validator.string().required()
    });

    const payload = await paymentSchema.validate(req.body.payload);

    return res.json(
        await erpClient.post('customers/make-payment', {
            ...payload,
            customerId: customer?.id
        })
    );
});

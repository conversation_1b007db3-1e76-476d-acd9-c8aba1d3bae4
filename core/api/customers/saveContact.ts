import {endpoint, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {contact} = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    const customerId = customer.id;

    await validator
        .object()
        .shape({
            customerId: validator.string().required(),
            contact: validator
                .object()
                .shape({
                    id: validator.string().notRequired(),
                    type: validator.string().required(),
                    name: validator.string().required(),
                    relevantContact: validator
                        .object()
                        .shape({
                            name: validator.string().required(),
                            email: validator.string().notRequired(),
                            phoneNumber: validator.string().notRequired(),
                            phoneCode: validator.string().notRequired(),
                            phoneCountryCode: validator.string().notRequired()
                        })
                        .notRequired(),
                    address: validator
                        .object()
                        .shape({
                            street: validator.string().min(2).required(),
                            street2: validator.string().notRequired(),
                            city: validator.string().min(2).required(),
                            district: validator.string().min(2).required(),
                            subDistrict: validator.string().min(2).required(),
                            postalCode: validator.string().min(2).required(),
                            countryId: validator.string().min(2).required(),
                            countryName: validator.string().min(2).required()
                        })
                        .notRequired(),
                    invoiceType: validator.string().notRequired(),
                    companyName: validator.string().notRequired(),
                    taxIdentificationNumber: validator.string().notRequired(),
                    taxOffice: validator.string().notRequired(),
                    identityNumber: validator.string().notRequired(),
                    useDeliveryAddressAsBillingAddress: validator
                        .boolean()
                        .notRequired()
                })
                .required()
        })
        .isValid({
            customerId,
            contact
        });

    return res.json(
        await erpClient.post('customers/save-contact', {
            customerId,
            contact
        })
    );
}, true);

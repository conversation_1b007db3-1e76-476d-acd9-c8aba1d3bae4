import {endpoint} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {startDate, endDate, search, limit = 10, skip = 0} = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    return res.json(
        await erpClient.post('customers/invoices', {
            customerId: customer.id,
            startDate,
            endDate,
            search,
            limit,
            skip
        })
    );
}, true);

@layer components {
    .divider {
        @apply !border-secondary-300 !opacity-100;
    }

    .scroller::-webkit-scrollbar {
        @apply xl:w-[8px];
    }

    .scroller::-webkit-scrollbar-track {
        @apply xl:rounded xl:bg-gray-100 xl:transition;
    }

    .scroller::-webkit-scrollbar-track:hover {
        @apply xl:bg-gray-200;
    }

    .scroller::-webkit-scrollbar-thumb {
        @apply xl:rounded xl:bg-gray-400 xl:transition;
    }

    .scroller::-webkit-scrollbar-thumb:hover {
        @apply xl:bg-gray-600;
    }

    /* Single Product Slider */
    .thumb-swiper .swiper-slide-thumb-active {
        @apply rounded border !border-primary-600;
    }

    .thumb-swiper .swiper-slide {
        @apply rounded border border-transparent;
    }

    .thumb-swiper-wrapper .swiper-button-next,
    .thumb-swiper-wrapper .swiper-button-prev {
        @apply hidden h-10 w-10 bg-transparent text-white xl:flex;
        text-shadow: 0 0 3px rgb(221 224 228), 0 0 2px #000000;
    }

    .disable-slide-navigation {
        @apply !hidden;
    }

    /* Grouped Product Slider */
    .product-slider {
        --swiper-navigation-size: 16px;
    }

    .product-slider .swiper-wrapper {
        @apply items-stretch;
    }

    .product-slider .swiper-wrapper .swiper-slide {
        @apply h-[initial] px-1 pb-4;
    }

    .product-slider .swiper-button-next,
    .product-slider .swiper-button-prev {
        @apply top-[calc(50%-60px)] hidden h-10 w-10 origin-bottom scale-0 rounded-full bg-white bg-opacity-40 text-default opacity-0 transition hover:bg-primary-600 hover:text-white xl:flex;
    }

    .product-slider .swiper-button-next {
        @apply origin-left;
    }

    .product-slider .swiper-button-prev {
        @apply origin-right;
    }

    .product-slider:hover .swiper-button-next,
    .product-slider:hover .swiper-button-prev {
        @apply scale-100 opacity-100 shadow-lg;
    }

    /* Home page slider */
    .home-main-slider {
        --swiper-navigation-size: 18px;
    }

    .home-main-slider .swiper-button-next,
    .home-main-slider .swiper-button-prev {
        @apply button-primary z-[1] h-10 w-10 scale-100 rounded-md bg-white transition hover:border-primary-600 hover:bg-primary-600 hover:text-white md:-mt-8 md:h-12 md:w-12;
    }

    .home-main-slider .swiper-button-next,
    .home-main-slider .swiper-button-prev {
        @apply text-primary-600 hover:bg-primary-600;
    }

    .home-main-slider .swiper-button-next {
        @apply -right-6 hidden md:block;
    }

    .home-main-slider .swiper-button-prev {
        @apply -left-6 hidden md:block;
    }

    .prose .ql-align-center {
        @apply text-center;
    }

    .prose .ql-align-right {
        @apply text-right;
    }

    .shipping-status-road {
        @apply h-5 rounded-full bg-gray-200;
    }

    .shipping-status-road::before {
        @apply absolute left-0 top-1/2 z-[2] h-1 w-full -translate-y-1/2 content-[''];
        background: linear-gradient(
            90deg,
            rgb(156 163 175 / 0.6) 0%,
            rgb(156 163 175 / 0.6) 70%,
            rgb(229 231 235 / var(--tw-bg-opacity)) 70%,
            rgb(229 231 235 / var(--tw-bg-opacity)) 100%
        );
        background-size: 64px;
    }

    .card-container {
        @apply shadow-card rounded-lg bg-white;
    }

    .menu-card::-webkit-scrollbar {
        display: none;
    }

    .button-primary {
        @apply shadow-small rounded-md border bg-white;
    }

    .roll-out {
        @apply animate-[rollout_0.4s];
    }

    @keyframes rollout {
        from {
            transform: translateX(50%);
        }
        to {
            transform: none;
        }
    }

    .skeleton-card {
        @apply shadow-small relative isolate overflow-hidden rounded-md bg-secondary-300 before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_1s_infinite] before:border-t before:border-secondary-100/20 before:bg-gradient-to-r before:from-transparent before:via-secondary-100/70 before:to-transparent;
    }

    @keyframes shimmer {
        to {
            transform: translateX(100%);
        }
    }

    .featured-button {
        @apply button-primary flex items-center justify-center p-2 transition hover:border-primary-600 hover:bg-primary-600 hover:text-white;
    }

    .quick-look-product-action-btn {
        @apply shadow-small inline-flex w-fit items-center gap-2.5 rounded-md border p-2 transition hover:border-primary-600 hover:text-primary-600;
    }

    .notification-icon-success {
        @apply text-primary-600;
    }

    .color-variant-slider .swiper {
        @apply px-1;
    }

    .auth-page .form-input-group input:-webkit-autofill ~ label {
        @apply form-label absolute left-12 top-1.5 cursor-text select-none text-xs text-muted transition-all peer-placeholder-shown:top-3 peer-placeholder-shown:text-base peer-placeholder-shown:text-muted peer-focus:top-1.5 peer-focus:text-xs peer-focus:text-muted;
    }
}

.swiper-pagination-bullet {
    @apply inline-flex h-3.5 w-3.5 rounded-full bg-secondary-400;
}

.swiper-pagination-bullet-active {
    @apply bg-primary-600;
}

/* react-date-picker */
.react-datepicker__input-container input {
    @apply button-primary form-input w-full cursor-pointer py-1.5 pl-9 text-sm font-medium caret-transparent transition placeholder:text-gray-700 hover:bg-secondary-100 focus:border-primary-600 focus:!ring-primary-600;
}

.react-datepicker-popper {
    @apply button-primary z-40 w-72 p-2 text-sm;
}

.react-datepicker-left {
    @apply absolute left-0 right-auto top-11 transform-none !important;
}

.react-datepicker-right {
    @apply absolute left-auto right-0 top-11 transform-none !important;
}

.react-datepicker__portal {
    @apply card-container absolute right-0 top-12 z-10 w-72 transform-none text-sm;
}

.react-datepicker__month-container {
    @apply flex flex-col;
}

.react-datepicker__month {
    @apply flex flex-col;
}

.react-datepicker__current-month {
    @apply ml-2.5 text-lg font-semibold text-gray-700;
}

.react-datepicker__week {
    @apply flex justify-around;
}

.react-datepicker__day-names {
    @apply flex justify-around text-center text-xs font-medium text-gray-400;
}

.react-datepicker__day-name {
    @apply flex h-8 w-8 items-center justify-center rounded-full py-1;
}

.react-datepicker__navigation {
    @apply absolute top-2;
}

.react-datepicker__navigation--previous {
    @apply right-12 flex h-8 w-8 items-center justify-center rounded transition hover:bg-gray-200;
}

.react-datepicker__navigation--next {
    @apply right-4 flex h-8 w-8 items-center justify-center rounded transition hover:bg-gray-200;
}

.react-datepicker__day {
    @apply mb-1 flex h-8 w-8 cursor-pointer items-center justify-center rounded-md py-1 text-sm text-gray-700 transition hover:bg-primary-500 hover:text-white;
}

.react-datepicker__day--disabled {
    @apply cursor-not-allowed opacity-40 transition hover:opacity-50;
}

.react-datepicker__day--outside-month {
    @apply text-gray-300;
}

.react-datepicker__day--selected {
    @apply bg-primary-600 text-white;
}

.react-datepicker__day--in-range {
    @apply bg-primary-100;
}

.react-datepicker__day--in-selecting-range {
    @apply bg-primary-100;
}

.react-datepicker__day--selecting-range-start {
    @apply border-2 border-primary-600 bg-white;
}

.react-datepicker__day--selecting-range-end {
    @apply border-2 border-primary-600 bg-white;
}

.react-datepicker__day--selected {
    @apply bg-primary-600 text-white;
}

.react-datepicker__day--range-start {
    @apply bg-primary-600 text-white hover:bg-primary-500;
}

.react-datepicker__day--range-end {
    @apply bg-primary-600 text-white hover:bg-primary-500;
}

.react-datepicker__aria-live {
    @apply hidden;
}

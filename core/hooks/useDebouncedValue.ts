import {useEffect, useState, useRef} from 'react';

export default function useDebouncedValue<T = any>(
    value: T,
    wait: number,
    options = {leading: false}
) {
    const [_value, setValue] = useState(value);
    const mountedRef = useRef(false);
    const timeoutRef = useRef<number>(null);
    const cooldownRef = useRef(false);

    const cancel = () => window.clearTimeout(timeoutRef.current as number);

    useEffect(() => {
        if (mountedRef.current) {
            if (!cooldownRef.current && options.leading) {
                cooldownRef.current = true;
                setValue(value);
            } else {
                cancel();
                // @ts-ignore
                timeoutRef.current = window.setTimeout(() => {
                    cooldownRef.current = false;
                    setValue(value);
                }, wait);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value, options.leading]);

    useEffect(() => {
        mountedRef.current = true;
        return cancel;
    }, []);

    return [_value, cancel] as const;
}

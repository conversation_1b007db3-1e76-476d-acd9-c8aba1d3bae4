import {GetServerSidePropsContext, Redirect} from 'next';
import {Session as SessionBase, getServerSession} from 'next-auth';
import {authOptions} from '@core/api/auth/common';
import storeConfig from '~/store.config';
import {erpClient, loadTranslations} from '.';
import {
    Brand,
    Campaign,
    Customer,
    NavigationItem,
    Product,
    StoreInfo
} from '../../types';
import base64 from '../base64';
import parseURLSearchParams from '../parseURLSearchParams';
import {trim} from '../str';

type ParsedUrlQuery = Record<string, string | string[] | undefined>;

type Options = {
    isSecure?: boolean;
};

type Session = SessionBase & {
    user?: Customer;
};

type PageProps = {
    storeInfo: StoreInfo;
    session: Session | null;
    slug?: string;
    pageNumber?: number;
    navigationItem?: NavigationItem;
    [key: string]: any;
};

async function initDynamicPageParams(
    ctx: GetServerSidePropsContext<ParsedUrlQuery>,
    options: Options = {}
) {
    const locale = ctx.locale ?? storeConfig.defaultLocale;
    const {isSecure = true} = options;
    const session = (await getServerSession(
        ctx.req,
        ctx.res,
        authOptions
    )) as Session;
    const props: PageProps = {
        storeInfo: await erpClient.post('common/info', {
            locale,
            ...(session && {customerId: session.user?.id})
        }),
        session: JSON.parse(JSON.stringify(session)),
        ...(await loadTranslations(locale))
    };
    let redirect: Redirect | boolean = false;

    if (isSecure && !session) {
        redirect = {destination: '/auth', permanent: false};
    }

    const {slug, pageNumber} = processParams(ctx.params);

    if (typeof slug === 'object') {
        props.slug = slug;
    }

    if (typeof pageNumber === 'number') {
        props.pageNumber = pageNumber;
    }

    const navigation = props.storeInfo.navigation;
    const productCatalogs = navigation.filter(
        item =>
            item.depth === 0 &&
            item.type === 'product-catalog' &&
            !item.showInMainMenu
    );
    let productCatalogMap: any = {};
    if (Array.isArray(productCatalogs) && productCatalogs.length > 0) {
        for (const productCatalog of productCatalogs) {
            const result = await erpClient.post('catalog/products', {
                categoryPaths: productCatalog.productCategoryPaths,
                groupIds: productCatalog.productGroupIds,
                brandIds: productCatalog.productBrandIds,
                tagIds: productCatalog.productTagIds,
                advancedFilters: productCatalog.advancedFilters,
                fields: storeConfig.catalog.productListItemFields,
                set: productCatalog.productSet,
                skip: 0,
                limit: 12,
                sort: storeConfig.catalog.productSort
                    ? storeConfig.catalog.productSort
                    : {createdAt: -1},
                paginated: false
            });

            productCatalogMap[productCatalog.id] = {
                products: result.products ?? [],
                catalogName: productCatalog.name,
                detailPageLink: productCatalog.slug
            };
        }
        props.productCatalogMap = productCatalogMap;
    }

    let navigationItem = navigation.find(
        navigationItem => navigationItem.slug === slug
    );

    if (typeof navigationItem === 'object') {
        props.navigationItem = navigationItem;
    }

    if (slug !== undefined && navigationItem === undefined) {
        // Get product.
        try {
            let processedSlug = slug;
            let selectedAttributes = null;
            if (slug?.indexOf('-') !== -1) {
                try {
                    const parts = processedSlug!.split('-');
                    const lastPart = parts[parts.length - 1];
                    const decoded = base64.decode(lastPart);

                    if (decoded.length > 0 && decoded.includes('=')) {
                        const parsed = parseURLSearchParams(decoded);

                        if (Object.keys(parsed).length > 0 && !!parsed._es) {
                            delete parsed._es;
                            processedSlug = parts.slice(0, -1).join('-');
                            selectedAttributes = parsed;
                        }
                    }
                } catch (error) {}
            }

            let product: Product | null = null;
            let productCampaigns: Campaign[] | null = null;

            try {
                const result = await erpClient.post('catalog/product', {
                    slug: processedSlug,
                    ...(session && {customerId: session.user?.id})
                });

                product = result.product;
                productCampaigns = result.campaigns;
            } catch (error) {}

            if (!!product) {
                props.pageType = 'product';

                if (selectedAttributes !== null) {
                    props.selectedAttributes = selectedAttributes;
                }

                props.product = product;

                try {
                    const result = await erpClient.post('catalog/products', {
                        set: 'related-products',
                        productId: product.productId,
                        fields: storeConfig.catalog.productListItemFields,
                        skip: 0,
                        limit: 12,
                        sort: {updatedAt: -1},
                        paginated: false,
                        ...(session && {customerId: session.user?.id})
                    });

                    props.relatedProducts = result.products;
                } catch (error) {
                    props.relatedProducts = [];
                }

                props.campaigns = productCampaigns ?? [];

                return {props, notFound: false, redirect};
            }

            // Get brands.
            let brand: Brand | null = null;
            try {
                brand = await erpClient.post('catalog/brand', {
                    slug
                });
            } catch (error) {}

            if (!!brand) {
                props.pageType = 'catalog';
                props.brand = brand;

                const navItem: NavigationItem = {
                    id: brand.id,
                    type: 'product-catalog',
                    name: brand.name,
                    slug: brand.slug,
                    locale,
                    showInMainMenu: false,
                    seoTitle: brand.name,
                    seoDescription: '',
                    productCategoryPaths: [],
                    productGroupIds: [],
                    productBrandIds: [brand.id],
                    path: '',
                    href: `/${trim(brand.slug, '/')}`,
                    order: 99999
                };
                const existing = navigation.find(
                    navigationItem =>
                        navigationItem.slug === navItem.slug &&
                        (navigationItem.type === 'product-catalog' ||
                            navigationItem.type === 'page' ||
                            navigationItem.type === 'link')
                );
                if (typeof existing === 'undefined') {
                    navigation.push(navItem);
                    navigationItem = navItem;
                } else {
                    navigationItem = existing;
                }

                props.storeInfo.navigation = navigation;
                props.navigationItem = navigationItem;
                props.productCatalogMap = productCatalogMap;

                return {props, notFound: false, redirect};
            }

            if (brand === null && product === null) {
                return {props, notFound: true, redirect};
            }
        } catch (error) {}
    }

    if (navigationItem?.type === 'product-catalog') {
        props.pageType = 'catalog';
    }

    return {props, notFound: false, redirect};
}

function processParams(params: ParsedUrlQuery | undefined) {
    if (
        typeof params !== 'undefined' &&
        Array.isArray(params.slug) &&
        params.slug.length > 0
    ) {
        const slug = params.slug;

        if (slug[slug.length - 2] === 'page') {
            const page = parseInt(slug[slug.length - 1]);

            if (!isNaN(page)) {
                return {
                    slug: slug.slice(0, params.slug.length - 2).join('/'),
                    pageNumber: page
                };
            }
        }

        return {slug: slug.join('/')};
    }

    return {};
}

export default initDynamicPageParams;

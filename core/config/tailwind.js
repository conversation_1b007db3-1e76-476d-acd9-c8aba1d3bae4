const {theme} = require('../../store.config');

/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        './node_modules/@vechaiui/**/*.{js,ts,jsx,tsx}',
        './components/**/*.{js,ts,jsx,tsx}',
        './core/**/*.{js,ts,jsx,tsx}',
        './pages/**/*.{js,ts,jsx,tsx}',
        './assets/**/*.{js,ts,jsx,tsx}'
    ],
    darkMode: 'class',
    theme: {
        extend: {
            container: {
                center: true,
                padding: '1rem',
                screens: {xl: '1280px'}
            },
            colors: {
                ...theme.colors,
                secondary: {
                    100: '#f1f2f4',
                    200: '#edeeef',
                    300: '#dde0e4',
                    400: '#c9cccf'
                },
                discount: '#ff0005'
            },
            fontFamily: {
                sans: [
                    'var(--inter-font)',
                    theme.fontFamily.split(',').map(p => p.trim())
                ]
            },
            fontSize: {
                base: ['1rem', {lineHeight: theme.lineHeight}]
            },
            height: {
                header: theme.headerHeight,
                logo: theme.logoHeight,
                'mobile-header': theme.mobileHeaderHeight,
                'mobile-logo': theme.mobileLogoHeight,
                'mobile-tab-bar': theme.mobileTabBarHeight
            },
            padding: {
                'mobile-header': theme.mobileHeaderHeight,
                'mobile-tab-bar': theme.mobileTabBarHeight
            },
            textColor: {
                default: theme.textColor,
                base: theme.textColor,
                muted: theme.textMutedColor
            }
        }
    },
    future: {
        hoverOnlyWhenSupported: true
    },
    plugins: [
        require('@tailwindcss/typography'),
        require('@tailwindcss/forms'),
        require('@tailwindcss/aspect-ratio'),
        require('@vechaiui/core')({
            colors: ['primary', 'success', 'warning', 'danger']
        })
    ]
};

const colors = require('./colors');

module.exports = {
    colors: {
        ...colors,
        primary: colors.green,
        success: colors.green,
        warning: colors.amber,
        danger: colors.red
    },

    fontFamily:
        '-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji',
    lineHeight: '1.5rem',
    textColor: colors.gray[700],
    textMutedColor: colors.gray[500],

    // Layout
    headerHeight: '64px',
    logoWidth: '134px',
    logoHeight: '31px',
    accountHeaderHeight: '96px',

    // Mobile Layout.
    mobileHeaderHeight: '60px',
    mobileTabBarHeight: '60px',
    iosTabBarHeight: '68px',
    mobileLogoWidth: '118px',
    mobileLogoHeight: '27px'
};

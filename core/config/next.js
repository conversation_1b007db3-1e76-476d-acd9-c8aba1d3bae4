const {defaultLocale, locales, redirectTo} = require('../../store.config');

const withBundleAnalyzer = require('@next/bundle-analyzer')({
    enabled: process.env.ANALYZE === 'true'
});

// const withPWA = require('next-pwa')({
//     dest: 'public',
//     disable: process.env.NODE_ENV === 'development'
// });

/** @type {import('next').NextConfig} */
const nextConfig = {
    reactStrictMode: false,
    poweredByHeader: false,
    images: {
        domains: [
            'localhost',
            new URL(process.env.NEXT_PUBLIC_API_URL).host,
            'main.entererp.com'
        ],
        formats: ['image/avif', 'image/webp'],
        minimumCacheTTL: 60 * 60 * 24 * 30
    },
    i18n: {
        locales,
        defaultLocale,
        localeDetection: false
    },
    experimental: {
        largePageDataBytes: 1024 * 1024
    },
    typescript: {
        ignoreBuildErrors: process.env.NEXT_IGNORE_CHECKS === 'true'
    },
    eslint: {
        ignoreDuringBuilds: process.env.NEXT_IGNORE_CHECKS === 'true'
    }
};

module.exports = withBundleAnalyzer(nextConfig);

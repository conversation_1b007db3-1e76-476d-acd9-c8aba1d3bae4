import Campaign from './Campaign';
import type Product from './Product';

type ProductListItem = Pick<
    Product,
    | 'productId'
    | 'code'
    | 'name'
    | 'brandName'
    | 'brandSlug'
    | 'slug'
    | 'shortDescription'
    | 'rating'
    | 'reviewCount'
    | 'images'
    | 'salesPrice'
    | 'unDiscountedSalesPrice'
    | 'discount'
    | 'hasDiscount'
    | 'link'
    | 'isBestSellingProduct'
    | 'isDiscountedProduct'
    | 'isNewProduct'
    | 'isSuggestedProduct'
    | 'estimatedDeliveryDuration'
    | 'salesPriceAtCart'
    | 'isAdultProduct'
    | 'hasFreeShipping'
    | 'quantity'
    | 'isFake'
    | 'categoryName'
    | 'availableQuantity'
    | 'deliveryOptionIds'
    | 'deliveryAtSpecifiedDate'
    | 'deliveryAtSpecifiedTime'
    | 'weight'
    | 'width'
    | 'height'
    | 'depth'
    | 'unitName'
    | 'unitId'
    | 'isKitProduct'
    | 'discountedPrice'
    | 'campaigns'
> & {
    isFavorite?: boolean;
    colorVariantCount?: number;
    hasVariants?: boolean;
    campaigns?: Campaign[];
    totalWarehouseStockQuantity?: number;
    warehouseStocks?: {
        warehouseId: string;
        warehouseName: string;
        availableQuantity: number;
    }[];
    baseSalesPrice?: number;
    baseSalesPriceCurrency?: string;
};

export default ProductListItem;

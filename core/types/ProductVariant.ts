type ProductVariant = {
    productId: string;
    code: string;
    name: string;
    definition: string;
    slug: string;
    barcode?: string;
    shortDescription?: string;
    description?: string;
    rating: number;
    reviewCount: number;
    salesCount: number;
    favoritesCount: number;
    images?: string[];
    isSuggestedProduct?: boolean;
    isBestSellingProduct?: boolean;
    isNewProduct?: boolean;
    isDiscountedProduct?: boolean;
    unitId: string;
    unitName: string;
    deliveryOptionIds: string[];
    estimatedDeliveryDuration?: number;
    deliveryAtSpecifiedDate?: boolean;
    deliveryAtSpecifiedTime?: boolean;
    weight: number;
    height: number;
    width: number;
    depth: number;
    salesPrice: number;
    unDiscountedSalesPrice: number;
    discount: number;
    hasDiscount: boolean;
    quantity: number;
    attributes: Record<string, string>;
    colorAttributeCode?: string;
    colorAttributeValue?: string;
    warehouseStocks?: {
        warehouseId: string;
        warehouseName: string;
        availableQuantity: number;
    }[];
    totalWarehouseStockQuantity?: number;
};

export default ProductVariant;

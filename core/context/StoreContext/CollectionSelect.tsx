import {FC, memo, useCallback, useRef, useState} from 'react';
import {FormProvider, useForm} from 'react-hook-form';
import {cls, isDev, jsonRequest} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {Collection} from '@core/types';
import {UiAlert, UiButton, UiForm, UiInput} from '@core/components/ui';
import {PlusIcon} from '@core/icons/regular';
import {CheckCircleIcon} from '@core/icons/solid';

type CollectionSelectProps = {
    collections: Collection[];
    existingCollectionIds: string[];
    onSelect: (collectionIds: string[]) => void;
};

const CollectionSelect: FC<CollectionSelectProps> = memo(props => {
    const {
        collections: initialCollections,
        existingCollectionIds,
        onSelect
    } = props;
    const methods = useForm();
    const {
        register,
        formState: {errors},
        setValue
    } = methods;
    const t = useTrans();
    const [collections, setCollections] = useState(() => initialCollections);
    const [errorMessage, setErrorMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const inProgress = useRef(false);
    const [selected, setSelected] = useState<string[]>(() =>
        Array.isArray(existingCollectionIds) ? existingCollectionIds : []
    );

    const onNewCollectionAdd = useCallback(
        async (data: Record<string, any>) => {
            if (inProgress.current) return;

            inProgress.current = true;
            setIsLoading(true);

            try {
                const newCollection: Collection = await jsonRequest({
                    url: '/api/customers/save-collection',
                    method: 'POST',
                    data: {
                        name: data.name
                    }
                });

                setCollections([...collections, newCollection]);
                setSelected([...selected, newCollection.id]);

                setValue('name', '');
            } catch (error: any) {
                setErrorMessage(error.message);
            }

            setIsLoading(false);
            inProgress.current = false;
        },
        [collections, selected, setValue]
    );

    const onCollectionClick = useCallback(
        (collection: Collection) => {
            if (selected.includes(collection.id)) {
                setSelected(() => selected.filter(s => s !== collection.id));
            } else {
                setSelected([...selected, collection.id]);
            }
        },
        [selected]
    );
    const onCollectionsSelect = useCallback(() => {
        onSelect(selected);
    }, [selected, onSelect]);

    return (
        <div className="mt-4 flex flex-col px-4 pb-4 xl:mt-2 xl:px-6 xl:pb-6">
            <div className="space-y-2 xl:space-y-4">
                {collections.map(collection => (
                    <div
                        key={collection.id}
                        className={cls(
                            'group relative flex items-center rounded-lg border bg-white p-4 shadow-sm',
                            'cursor-pointer select-none text-sm transition focus:outline-none',
                            {
                                'border-primary-600 bg-primary-50 text-primary-600':
                                    selected.includes(collection.id)
                            }
                        )}
                        onClick={() => onCollectionClick(collection)}
                    >
                        <div className="flex items-center">
                            {selected.includes(collection.id) ? (
                                <CheckCircleIcon
                                    className="mr-4 h-6 w-6 text-primary-600"
                                    aria-hidden="true"
                                />
                            ) : (
                                <div className="mr-4 h-6 w-6 rounded-full border border-gray-300"></div>
                            )}
                        </div>
                        <div className="flex-1 overflow-hidden">
                            <div className="mb-1 truncate font-medium">
                                {t(collection!.name)}
                            </div>
                            <div className="text-muted">
                                {t('{count} product(s)', {
                                    count: collection!.itemCount
                                })}
                            </div>
                        </div>
                    </div>
                ))}

                <FormProvider {...methods}>
                    <UiForm onSubmit={methods.handleSubmit(onNewCollectionAdd)}>
                        {!!errorMessage && (
                            <UiAlert className="mb-4" color="danger">
                                {t(errorMessage)}
                            </UiAlert>
                        )}

                        {errors['name'] &&
                            errors['name'].type === 'required' && (
                                <UiAlert className="mb-4" color="danger">
                                    {t('Name is required')}
                                </UiAlert>
                            )}

                        <div className="flex w-full space-x-2">
                            <UiInput
                                className=""
                                placeholder={t('Create a new collection')}
                                size="lg"
                                {...register('name', {
                                    required: true
                                })}
                            />
                            <UiButton
                                variant="light"
                                size="lg"
                                leftIcon={<PlusIcon className="h-4 w-4" />}
                                loading={isLoading}
                                disabled={isLoading}
                                type="submit"
                            />
                        </div>
                    </UiForm>
                </FormProvider>

                <UiButton
                    className="mt-6 w-full"
                    variant="solid"
                    color="primary"
                    size="xl"
                    loading={isLoading}
                    disabled={isLoading}
                    onClick={onCollectionsSelect}
                >
                    {t('Save')}
                </UiButton>
            </div>
        </div>
    );
});

if (isDev) {
    CollectionSelect.displayName = 'CollectionSelect';
}

export default CollectionSelect;

import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {CollectionProduct} from '@core/types';
import {UiImage, UiPrice} from '@core/components/ui';

type CollectionProductSummaryProps = {
    locale: string;
    currency: {
        name: string;
        symbol: string;
        template: string;
    };
    item: CollectionProduct;
    onDetail: () => void;
};

const CollectionProductSummary: FC<CollectionProductSummaryProps> = memo(
    props => {
        const {locale, currency, item, onDetail} = props;

        return (
            <div className="mt-4 w-full border-t pt-4 text-sm">
                <div className="flex cursor-pointer" onClick={onDetail}>
                    <div className="h-18 w-12 flex-shrink-0">
                        <UiImage
                            className="h-full w-full rounded"
                            src={item.image}
                            alt={item.name}
                            width={60}
                            height={90}
                            fit="cover"
                            position="center"
                            quality={75}
                        />
                    </div>

                    <div className="ml-4 flex-1 flex-col">
                        <div className="w-full">
                            <h2 className="font-medium">{item.name}</h2>
                        </div>

                        <div className="pt-2">
                            <UiPrice
                                locale={locale}
                                currency={currency}
                                price={item.price}
                            />
                        </div>
                    </div>
                </div>
            </div>
        );
    }
);

if (isDev) {
    CollectionProductSummary.displayName = 'CollectionProductSummary';
}

export default CollectionProductSummary;

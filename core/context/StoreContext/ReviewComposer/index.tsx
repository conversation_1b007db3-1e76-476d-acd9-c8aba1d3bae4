import {
    FC,
    memo,
    Mouse<PERSON><PERSON><PERSON><PERSON><PERSON>,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {useRouter} from 'next/router';
import {FormProvider, useForm} from 'react-hook-form';
import {cleanHtml, cls, isDev, jsonRequest} from '@core/helpers';
import {useStore, useTrans, useUI} from '@core/hooks';
import {CollectionProduct} from '@core/types';
import {
    notification,
    UiAlert,
    UiButton,
    UiCheckbox,
    UiForm,
    UiImage,
    UiRating,
    UiTextarea
} from '@core/components/ui';
import Price from '@components/common/Price';
import storeConfig from '~/store.config';

import DocumentSheet from './DocumentSheet';
import CollectionProductSummary from '../CollectionProductSummary';

type ReviewComposerProps = {
    product: CollectionProduct;
    onCreate: () => void;
};

const ReviewComposer: FC<ReviewComposerProps> = memo(props => {
    const {product, onCreate} = props;
    const methods = useForm({
        defaultValues: {
            content: '',
            isAuthorNameShown: true,
            acceptedMembershipAgreement: true
        }
    });
    const {
        register,
        formState: {errors},
        watch
    } = methods;
    const {push: pushRoute, asPath: routePath} = useRouter();
    const {locale} = useStore();
    const t = useTrans();
    const {closeSideBar} = useUI();
    const [errorMessage, setErrorMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const inProgress = useRef(false);
    const [rating, setRating] = useState<number>();

    const contentText = watch('content');
    const remainingCharacterCount = useMemo(
        () => 2000 - contentText.length,
        [contentText]
    );

    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgress.current) return;

            if (typeof rating !== 'number' || !(rating >= 1 && rating <= 5)) {
                setErrorMessage(t('Rating is required'));

                return;
            }

            inProgress.current = true;
            setIsLoading(true);

            try {
                const content = cleanHtml(data.content ?? '');

                await jsonRequest({
                    url: '/api/customers/create-review',
                    method: 'POST',
                    data: {
                        locale,
                        productId: product.id,
                        rating,
                        content: content ?? '',
                        isAuthorNameShown: !!data.isAuthorNameShown,
                        acceptedMembershipAgreement:
                            !!data.acceptedMembershipAgreement
                    }
                });

                onCreate();

                closeSideBar();

                notification({
                    title: t('Review Submitted'),
                    description: t(
                        'Thank you for your review. Your review will be published after it has been approved.'
                    ),
                    status: 'success',
                    detailRenderer: closeNotification => (
                        <CollectionProductSummary
                            locale={locale}
                            currency={{
                                name: storeConfig.defaultCurrency.name,
                                symbol: storeConfig.defaultCurrency.symbol,
                                template: storeConfig.defaultCurrency.template
                            }}
                            item={product}
                            onDetail={() => {
                                closeNotification();
                                pushRoute('/account/my-reviews');
                            }}
                        />
                    )
                });
            } catch (error: any) {
                if (error.code === 'membership_agreement_not_accepted') {
                    setErrorMessage(
                        t(
                            'The membership agreement for the creating comment must be accepted.'
                        )
                    );
                } else {
                    setErrorMessage(error.message);
                }
            }

            setIsLoading(false);
            inProgress.current = false;
        },
        [
            closeSideBar,
            locale,
            notification,
            onCreate,
            product,
            pushRoute,
            rating,
            t
        ]
    );

    const [contractDocuments, setContractDocuments] = useState({
        termsOfMembershipText: '',
        clarificationText: '',
        commentPostingCriteriaText: ''
    });
    const [isDocumentSheetShown, setIsDocumentSheetShown] = useState(false);
    const [documentSheetTitle, setDocumentSheetTitle] = useState('');
    const [documentSheetContent, setDocumentSheetContent] = useState('');
    useEffect(() => {
        (async () => {
            try {
                const documents = await jsonRequest({
                    url: '/api/common/contract-documents',
                    method: 'POST',
                    data: {locale}
                });

                setContractDocuments(documents);
            } catch (error: any) {
                console.log(error);
            }
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    const onCommentPostingCriteriaTextClick = useCallback(() => {
        setDocumentSheetTitle(t('Comment Posting Criteria'));
        setDocumentSheetContent(contractDocuments.commentPostingCriteriaText);
        setIsDocumentSheetShown(true);
    }, [contractDocuments.commentPostingCriteriaText, t]);
    const onClarificationTextClick: MouseEventHandler<HTMLDivElement> =
        useCallback(
            e => {
                // @ts-ignore
                const id = e.target.id;

                if (id === 'clarificationText') {
                    e.preventDefault();

                    setDocumentSheetTitle(t('Clarification Text'));
                    setDocumentSheetContent(
                        contractDocuments.clarificationText
                    );
                    setIsDocumentSheetShown(true);
                }
            },
            [contractDocuments.clarificationText, t]
        );
    const onMembershipAgreementTextClick: MouseEventHandler<HTMLDivElement> =
        useCallback(
            e => {
                // @ts-ignore
                const id = e.target.id;

                if (id === 'termsOfMembershipText') {
                    e.preventDefault();

                    setDocumentSheetTitle(t('Membership Agreement'));
                    setDocumentSheetContent(
                        contractDocuments.termsOfMembershipText
                    );
                    setIsDocumentSheetShown(true);
                }
            },
            [contractDocuments.termsOfMembershipText, t]
        );

    return (
        <FormProvider {...methods}>
            <UiForm
                className="my-4 flex flex-col px-4 pb-4 xl:my-2 xl:px-6 xl:pb-6"
                onSubmit={methods.handleSubmit(onSubmit)}
            >
                {!!errorMessage && (
                    <UiAlert className="mb-6" color="danger">
                        {t(errorMessage)}
                    </UiAlert>
                )}

                {errors['acceptedMembershipAgreement'] &&
                    errors['acceptedMembershipAgreement'].type ===
                        'required' && (
                        <UiAlert className="mb-6" color="danger">
                            {t(
                                'The membership agreement for the creating comment must be accepted.'
                            )}
                        </UiAlert>
                    )}

                <div className="flex">
                    <div
                        className={cls(
                            'h-24 flex-shrink-0 overflow-hidden rounded',
                            {
                                'w-18':
                                    storeConfig.catalog.productImageShape ===
                                    'rectangle',
                                'w-24':
                                    storeConfig.catalog.productImageShape !==
                                    'rectangle'
                            }
                        )}
                    >
                        <UiImage
                            className="h-full w-full rounded"
                            src={product.image}
                            alt={product.name}
                            width={
                                storeConfig.catalog.productImageShape ===
                                'rectangle'
                                    ? 72
                                    : 96
                            }
                            height={96}
                            fit="cover"
                            position="center"
                            quality={75}
                        />
                    </div>

                    <div className="ml-4 flex flex-1 flex-col justify-between">
                        <div className="flex justify-between font-medium text-base text-default">
                            <h3 className="-mt-1">{product.name}</h3>

                            <Price
                                className="ml-4 hidden xl:block"
                                price={product.price}
                            />
                        </div>

                        <div className="h-6">
                            <UiRating
                                size="lg"
                                initialRating={rating}
                                onClick={value => {
                                    setRating(value);
                                    setErrorMessage('');
                                }}
                            />
                        </div>
                    </div>
                </div>

                <div className="mt-8">
                    <div className="flex justify-between text-sm">
                        <div className="font-medium">{t('Your comment')}</div>

                        <button
                            type="button"
                            className="cursor-pointer underline"
                            onClick={onCommentPostingCriteriaTextClick}
                        >
                            {t('Comment Posting Criteria')}
                        </button>
                    </div>

                    <UiForm.Control className="mt-2">
                        <UiTextarea
                            className="h-32"
                            maxLength={2000}
                            {...register('content', {
                                required: false,
                                maxLength: 2000
                            })}
                        />
                    </UiForm.Control>
                    <div className="mt-0.5 text-right text-xs text-muted">
                        {remainingCharacterCount}
                    </div>

                    <UiForm.Control className="mt-4">
                        <UiCheckbox
                            className="mt-1 self-start"
                            {...register('isAuthorNameShown', {
                                required: false
                            })}
                        >
                            <div
                                onClick={onClarificationTextClick}
                                dangerouslySetInnerHTML={{
                                    __html: t(
                                        'I allow my name and surname information to appear in the comments. <span id="clarificationText" class="text-primary-600">Click here</span> to access to the Clarification Text.'
                                    )
                                }}
                            />
                        </UiCheckbox>
                    </UiForm.Control>

                    <UiForm.Control className="mt-4">
                        <UiCheckbox
                            {...register('acceptedMembershipAgreement', {
                                required: true
                            })}
                        >
                            <div
                                onClick={onMembershipAgreementTextClick}
                                dangerouslySetInnerHTML={{
                                    __html: t(
                                        'I accept the <span id="termsOfMembershipText" class="text-primary-600">Membership Agreement</span> to add comments.'
                                    )
                                }}
                            />
                        </UiCheckbox>
                    </UiForm.Control>

                    <UiButton
                        className="mt-4 w-full xl:mt-6"
                        type="submit"
                        variant="solid"
                        size="lg"
                        color="primary"
                        loading={isLoading}
                    >
                        {t('Comment')}
                    </UiButton>
                </div>
            </UiForm>

            <DocumentSheet
                isShown={isDocumentSheetShown}
                title={documentSheetTitle}
                document={documentSheetContent}
                onClose={() => setIsDocumentSheetShown(false)}
            />
        </FormProvider>
    );
});

if (isDev) {
    ReviewComposer.displayName = 'ReviewComposer';
}

export default ReviewComposer;

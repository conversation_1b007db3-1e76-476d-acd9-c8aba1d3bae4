import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {UiTransition} from '@core/components/ui';
import {XIcon} from '@core/icons/solid';

type DocumentSheetProps = {
    isShown: boolean;
    title: string;
    document: string;
    onClose: () => void;
};

const DocumentSheet: FC<DocumentSheetProps> = memo(
    ({isShown, title, document, onClose}) => {
        return (
            <UiTransition.Root className="absolute inset-0" show={isShown}>
                <UiTransition.Child
                    className="absolute bottom-0 z-10 flex h-4/5 w-full flex-col overflow-hidden rounded-t-2xl bg-white shadow-xl"
                    enter="transform transition ease-in-out duration-500"
                    enterFrom="translate-y-full"
                    enterTo="translate-y-0"
                    leave="transform transition ease-in-out duration-500"
                    leaveFrom="translate-y-0"
                    leaveTo="translate-y-full"
                >
                    <div className="px-6 py-6">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center text-lg font-medium">
                                {title}
                            </div>
                            <div className="ml-3 flex h-8 items-center">
                                <button
                                    type="button"
                                    className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-muted outline-none transition hover:bg-gray-200 hover:text-muted"
                                    onClick={onClose}
                                >
                                    <XIcon
                                        className="h-5 w-5"
                                        aria-hidden="true"
                                    />
                                </button>
                            </div>
                        </div>
                    </div>

                    <div className="scroller flex-1 overflow-y-auto overflow-x-hidden">
                        <div className="w-full px-6 pb-6">
                            <div
                                className="prose"
                                dangerouslySetInnerHTML={{
                                    __html: document
                                }}
                            />
                        </div>
                    </div>
                </UiTransition.Child>

                <UiTransition.Child
                    className="absolute inset-0 bg-gray-900 bg-opacity-10"
                    enter="transition ease-in-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="transition ease-in-out duration-300"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                    onClick={onClose}
                />
            </UiTransition.Root>
        );
    }
);

if (isDev) {
    DocumentSheet.displayName = 'DocumentSheet';
}

export default DocumentSheet;

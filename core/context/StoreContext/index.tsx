import {
    createContext,
    FC,
    memo,
    PropsWithChildren,
    useCallback,
    useMemo,
    useRef,
    useState
} from 'react';
import {useRouter} from 'next/router';
import {useSession} from 'next-auth/react';
import {isDev, jsonRequest} from '@core/helpers';
import {useTrans, useUI} from '@core/hooks';
import {
    Collection,
    CollectionProduct,
    Customer,
    CustomerProductPrams,
    NavigationItem,
    StoreInfo
} from '@core/types';
import storeConfig from '~/store.config';
import {StoreContextType} from './types';
import CollectionProductSummary from './CollectionProductSummary';
import CollectionSelect from './CollectionSelect';
import ReviewComposer from './ReviewComposer';
import {notification} from '@core/components/ui';

export const StoreContext = createContext<StoreContextType>(null as any);

if (isDev) {
    StoreContext.displayName = 'StoreContext';
}

type StoreProviderProps = {
    info: StoreInfo;
    navigationItem?: NavigationItem;
    pageType: 'home' | 'catalog' | 'product' | 'page';
    slug?: string;
};

export const StoreProvider: FC<PropsWithChildren<StoreProviderProps>> = memo(
    props => {
        const {info, navigationItem, pageType = 'page', slug, children} = props;
        const {
            push: pushRoute,
            locale: selectedLocale,
            asPath: routePath
        } = useRouter();
        const {data: session} = useSession();
        const t = useTrans();
        const {openSideBar, closeSideBar} = useUI();
        const [updatedCustomer, setUpdatedCustomer] = useState<
            Customer | undefined
        >();

        // Add to favorites.
        const isFavoriteUpdateInProgress = useRef(false);
        const addToFavorites = useCallback(
            async (product: CollectionProduct) => {
                if (!!session) {
                    try {
                        if (isFavoriteUpdateInProgress.current) {
                            return false;
                        }

                        isFavoriteUpdateInProgress.current = true;

                        await jsonRequest({
                            url: '/api/customers/add-to-favorites',
                            method: 'POST',
                            data: {productId: product.id}
                        });

                        notification({
                            title: t('Added to Favourites'),
                            description: t(
                                'The product has been added to your favourite list.'
                            ),
                            status: 'success',
                            detailRenderer: closeNotification => (
                                <CollectionProductSummary
                                    locale={
                                        !!selectedLocale
                                            ? selectedLocale
                                            : storeConfig.defaultLocale
                                    }
                                    currency={{
                                        name: storeConfig.defaultCurrency.name,
                                        symbol: storeConfig.defaultCurrency
                                            .symbol,
                                        template:
                                            storeConfig.defaultCurrency.template
                                    }}
                                    item={product}
                                    onDetail={() => {
                                        closeNotification();
                                        pushRoute('/account/my-favorites');
                                    }}
                                />
                            )
                        });

                        isFavoriteUpdateInProgress.current = false;

                        return true;
                    } catch (error: any) {
                        console.log(error);
                        notification({
                            title: t('Error'),
                            description: t(
                                'An error occurred while adding the product to favourites!'
                            ),
                            status: 'error'
                        });

                        isFavoriteUpdateInProgress.current = false;

                        return false;
                    }
                } else {
                    await pushRoute(`/auth?redirect=${routePath}`);

                    return false;
                }
            },
            [routePath, pushRoute, session, selectedLocale, notification, t]
        );
        const removeFromFavorites = useCallback(
            async (product: CollectionProduct) => {
                if (!!session) {
                    try {
                        if (isFavoriteUpdateInProgress.current) {
                            return false;
                        }

                        isFavoriteUpdateInProgress.current = true;

                        await jsonRequest({
                            url: '/api/customers/remove-from-favorites',
                            method: 'POST',
                            data: {productId: product.id}
                        });

                        notification({
                            title: t('Removed from Favourites'),
                            description: t(
                                'The product has been removed from your favourite list.'
                            ),
                            status: 'success',
                            detailRenderer: closeNotification => (
                                <CollectionProductSummary
                                    locale={
                                        !!selectedLocale
                                            ? selectedLocale
                                            : storeConfig.defaultLocale
                                    }
                                    currency={{
                                        name: storeConfig.defaultCurrency.name,
                                        symbol: storeConfig.defaultCurrency
                                            .symbol,
                                        template:
                                            storeConfig.defaultCurrency.template
                                    }}
                                    item={product}
                                    onDetail={() => {
                                        closeNotification();
                                        pushRoute('/account/my-favorites');
                                    }}
                                />
                            )
                        });

                        isFavoriteUpdateInProgress.current = false;

                        return true;
                    } catch (error: any) {
                        console.log(error);
                        notification({
                            title: t('Error'),
                            description: error.message,
                            status: 'error'
                        });

                        isFavoriteUpdateInProgress.current = false;

                        return false;
                    }
                } else {
                    await pushRoute(`/auth?redirect=${routePath}`);

                    return false;
                }
            },
            [routePath, pushRoute, session, selectedLocale, notification, t]
        );

        // Add to collection.
        const isCollectionUpdateInProgress = useRef(false);
        const updateProductCollections = useCallback(
            async (product: CollectionProduct) => {
                if (!!session) {
                    let collections: Collection[] | null = null;
                    let customerProductParams: CustomerProductPrams | null =
                        null;

                    try {
                        collections = await jsonRequest({
                            url: '/api/customers/collections',
                            method: 'POST',
                            data: {}
                        });
                        customerProductParams = await jsonRequest({
                            url: '/api/customers/product-params-for-customer',
                            method: 'POST',
                            data: {
                                productId: product.id
                            }
                        });
                    } catch (error: any) {
                        return [];
                    }

                    const existingCollectionIds = (
                        customerProductParams!.collectionIds ?? []
                    ).filter(id => id !== 'is-alarm');

                    try {
                        const collectionIds = await new Promise(
                            async (resolve, reject) => {
                                openSideBar(
                                    t('Select Collection'),
                                    <CollectionSelect
                                        collections={(collections ?? []).filter(
                                            c => !c.isAlarm
                                        )}
                                        existingCollectionIds={
                                            existingCollectionIds
                                        }
                                        onSelect={async collectionIds => {
                                            closeSideBar();

                                            try {
                                                if (
                                                    isCollectionUpdateInProgress.current
                                                ) {
                                                    return reject(null);
                                                }

                                                isCollectionUpdateInProgress.current =
                                                    true;

                                                await jsonRequest({
                                                    url: '/api/customers/save-product-collections',
                                                    method: 'POST',
                                                    data: {
                                                        productId: product.id,
                                                        collectionIds
                                                    }
                                                });

                                                isCollectionUpdateInProgress.current =
                                                    false;

                                                notification({
                                                    title: t(
                                                        'Added to Collection'
                                                    ),
                                                    description: t(
                                                        'The product has been added to the collection.'
                                                    ),
                                                    status: 'success',
                                                    detailRenderer:
                                                        closeNotification => (
                                                            <CollectionProductSummary
                                                                locale={
                                                                    !!selectedLocale
                                                                        ? selectedLocale
                                                                        : storeConfig.defaultLocale
                                                                }
                                                                currency={{
                                                                    name: storeConfig
                                                                        .defaultCurrency
                                                                        .name,
                                                                    symbol: storeConfig
                                                                        .defaultCurrency
                                                                        .symbol,
                                                                    template:
                                                                        storeConfig
                                                                            .defaultCurrency
                                                                            .template
                                                                }}
                                                                item={product}
                                                                onDetail={() => {
                                                                    closeNotification();
                                                                    pushRoute(
                                                                        `/account/my-collections`
                                                                    );
                                                                }}
                                                            />
                                                        )
                                                });

                                                resolve(collectionIds);
                                            } catch (error: any) {
                                                console.log(error);

                                                isCollectionUpdateInProgress.current =
                                                    false;

                                                reject(null);
                                            }
                                        }}
                                    />,
                                    'normal',
                                    () => {
                                        reject(null);
                                    }
                                );
                            }
                        );

                        return Array.isArray(collectionIds)
                            ? collectionIds
                            : [];
                    } catch (error: any) {
                        isCollectionUpdateInProgress.current = false;

                        return existingCollectionIds;
                    }
                } else {
                    await pushRoute(`/auth?redirect=${routePath}`);

                    return [];
                }
            },
            [
                closeSideBar,
                notification,
                openSideBar,
                pushRoute,
                routePath,
                selectedLocale,
                session,
                t
            ]
        );
        const addToCollection = useCallback(
            async (
                collection: Partial<Collection>,
                product: CollectionProduct
            ) => {
                if (!!session) {
                    try {
                        if (isCollectionUpdateInProgress.current) {
                            return false;
                        }

                        isCollectionUpdateInProgress.current = true;

                        await jsonRequest({
                            url: '/api/customers/add-to-collection',
                            method: 'POST',
                            data: {
                                collectionId: collection.id,
                                productId: product.id,
                                isBuyLater: collection.isBuyLater,
                                isAlarm: collection.isAlarm
                            }
                        });

                        notification({
                            title: t('Added to Collection'),
                            description: t(
                                'The product has been added to the collection.'
                            ),
                            status: 'success',
                            detailRenderer: closeNotification => (
                                <CollectionProductSummary
                                    locale={
                                        !!selectedLocale
                                            ? selectedLocale
                                            : storeConfig.defaultLocale
                                    }
                                    currency={{
                                        name: storeConfig.defaultCurrency.name,
                                        symbol: storeConfig.defaultCurrency
                                            .symbol,
                                        template:
                                            storeConfig.defaultCurrency.template
                                    }}
                                    item={product}
                                    onDetail={() => {
                                        closeNotification();
                                        pushRoute(
                                            `/account/my-collections/${collection.id}`
                                        );
                                    }}
                                />
                            )
                        });

                        isCollectionUpdateInProgress.current = false;

                        return true;
                    } catch (error: any) {
                        console.log(error);
                        notification({
                            title: t('Error'),
                            description: t(
                                'An error occurred while adding the product to the collection!'
                            ),
                            status: 'error'
                        });
                        isCollectionUpdateInProgress.current = false;

                        return false;
                    }
                } else {
                    await pushRoute(`/auth?redirect=${routePath}`);

                    return false;
                }
            },
            [routePath, pushRoute, session, selectedLocale, notification, t]
        );
        const removeFromCollection = useCallback(
            async (
                collection: Partial<Collection>,
                product: CollectionProduct
            ) => {
                if (!!session) {
                    try {
                        if (isCollectionUpdateInProgress.current) {
                            return;
                        }

                        isCollectionUpdateInProgress.current = true;

                        await jsonRequest({
                            url: '/api/customers/remove-from-collection',
                            method: 'POST',
                            data: {
                                collectionId: collection.id,
                                productId: product.id,
                                isBuyLater: collection.isBuyLater,
                                isAlarm: collection.isAlarm
                            }
                        });

                        notification({
                            title: t('Removed from Collection'),
                            description: t(
                                'The product has been removed from the collection.'
                            ),
                            status: 'success',
                            detailRenderer: closeNotification => (
                                <CollectionProductSummary
                                    locale={
                                        !!selectedLocale
                                            ? selectedLocale
                                            : storeConfig.defaultLocale
                                    }
                                    currency={{
                                        name: storeConfig.defaultCurrency.name,
                                        symbol: storeConfig.defaultCurrency
                                            .symbol,
                                        template:
                                            storeConfig.defaultCurrency.template
                                    }}
                                    item={product}
                                    onDetail={() => {
                                        closeNotification();
                                        pushRoute('/account/my-favorites');
                                    }}
                                />
                            )
                        });

                        isCollectionUpdateInProgress.current = false;

                        return true;
                    } catch (error: any) {
                        console.log(error);
                        notification({
                            title: t('Error'),
                            description: error.message,
                            status: 'error'
                        });

                        isCollectionUpdateInProgress.current = false;

                        return false;
                    }
                } else {
                    await pushRoute(`/auth?redirect=${routePath}`);

                    return false;
                }
            },
            [routePath, pushRoute, session, selectedLocale, notification, t]
        );

        // Create Review.
        const isCreateReviewInProgress = useRef(false);
        const createReview = useCallback(
            async (product: CollectionProduct) => {
                if (!!session) {
                    try {
                        if (isCreateReviewInProgress.current) {
                            return;
                        }

                        isCreateReviewInProgress.current = true;

                        const checkResult = await jsonRequest({
                            url: '/api/customers/check-review-create',
                            method: 'POST',
                            data: {
                                productId: product.id
                            }
                        });

                        if (checkResult.status !== 'ok') {
                            if (checkResult.code === 'already_reviewed') {
                                notification({
                                    title: t('Error'),
                                    description: t(
                                        'You have already reviewed this product.'
                                    ),
                                    status: 'error'
                                });
                            } else if (
                                checkResult.code === 'must_be_purchased'
                            ) {
                                notification({
                                    title: t(
                                        'In order to be able to evaluate, you must have purchased the product beforehand.'
                                    ),
                                    description: t(
                                        'In order to provide you with more useful evaluations, we only include the experiences of our customers who purchased the relevant product.'
                                    ),
                                    status: 'error'
                                });
                            }

                            isCreateReviewInProgress.current = false;

                            return false;
                        }

                        const result = await new Promise(resolve =>
                            openSideBar(
                                t('What Do You Think About The Product?'),
                                <ReviewComposer
                                    product={product}
                                    onCreate={() => resolve(true)}
                                />,
                                'normal',
                                () => resolve(false)
                            )
                        );

                        isCreateReviewInProgress.current = false;

                        return result;
                    } catch (error: any) {
                        console.log(error);
                        notification({
                            title: t('Error'),
                            description: error.message,
                            status: 'error'
                        });

                        isCreateReviewInProgress.current = false;

                        return false;
                    }
                } else {
                    await pushRoute(`/auth?redirect=${routePath}`);

                    return false;
                }
            },
            [session, openSideBar, t, pushRoute, routePath]
        );

        const value: any = useMemo(
            () => ({
                paymentOnly: !!storeConfig.paymentOnly,
                title: storeConfig.title,
                description: storeConfig.description,
                locales: storeConfig.locales,
                defaultLocale: storeConfig.defaultLocale,
                locale: !!selectedLocale
                    ? selectedLocale
                    : storeConfig.defaultLocale,
                defaultCurrency: {
                    name: storeConfig.defaultCurrency.name,
                    symbol: storeConfig.defaultCurrency.symbol,
                    template: storeConfig.defaultCurrency.template
                },
                currency: {
                    name: storeConfig.defaultCurrency.name,
                    symbol: storeConfig.defaultCurrency.symbol,
                    template: storeConfig.defaultCurrency.template
                },
                ...(typeof info === 'object'
                    ? {navigation: info.navigation}
                    : {}),
                navigationItem,
                pageType,
                slug,
                ...(typeof info === 'object'
                    ? {
                          popularSearches: info.popularSearches,
                          popularCategories: info.popularCategories
                      }
                    : {}),
                updatedCustomer,

                setUpdatedCustomer,
                addToFavorites,
                removeFromFavorites,
                updateProductCollections,
                addToCollection,
                removeFromCollection,
                createReview
            }),
            [
                selectedLocale,
                info,
                navigationItem,
                pageType,
                slug,

                updatedCustomer,
                addToFavorites,
                removeFromFavorites,
                updateProductCollections,
                addToCollection,
                removeFromCollection,
                createReview
            ]
        );

        return (
            <StoreContext.Provider value={value}>
                {children}
            </StoreContext.Provider>
        );
    }
);

if (isDev) {
    StoreProvider.displayName = 'StoreProvider';
}

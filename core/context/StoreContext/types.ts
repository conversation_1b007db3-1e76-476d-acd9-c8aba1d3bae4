import {Dispatch, SetStateAction} from 'react';
import {
    Collection,
    CollectionProduct,
    Customer,
    NavigationItem
} from '@core/types';

export type StoreContextType = {
    paymentOnly: boolean;
    title: string;
    description: string;
    locales: string[];
    defaultLocale: string;
    locale: string;
    defaultCurrency: {
        name: string;
        symbol: string;
        template: string;
    };
    currency: {
        name: string;
        symbol: string;
        template: string;
    };
    navigation: NavigationItem[];
    navigationItem?: NavigationItem;
    pageType: 'home' | 'catalog' | 'product' | 'page';
    slug?: string;
    popularSearches: {
        id: string;
        title: string;
    }[];
    popularCategories: {
        id: string;
        title: string;
        href: string;
    }[];
    updatedCustomer?: Customer;
    setUpdatedCustomer: Dispatch<SetStateAction<Customer | undefined>>;
    addToFavorites: (product: CollectionProduct) => Promise<boolean>;
    removeFromFavorites: (product: CollectionProduct) => Promise<boolean>;
    updateProductCollections: (product: CollectionProduct) => Promise<string[]>;
    addToCollection: (
        collection: Partial<Collection>,
        product: CollectionProduct
    ) => Promise<boolean>;
    removeFromCollection: (
        collection: Partial<Collection>,
        product: CollectionProduct
    ) => Promise<boolean>;
    createReview: (product: CollectionProduct) => Promise<boolean>;
};

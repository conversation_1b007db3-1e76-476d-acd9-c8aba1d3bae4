import {
    createContext,
    Dispatch,
    FC,
    memo,
    ReactNode,
    SetStateAction,
    useEffect,
    useMemo,
    useState
} from 'react';
import {isDev} from '@core/helpers';
import {useViewportSize} from '@core/hooks';

type LayoutContextType = {
    isSideMenuBlocked: boolean;
    isSideMenuOpen: boolean;
    setIsSideMenuOpen: Dispatch<SetStateAction<boolean>>;
};

export const LayoutContext = createContext<LayoutContextType>(null as any);

export const LayoutProvider: FC<{children: ReactNode}> = memo(({children}) => {
    const [isSideMenuOpen, setIsSideMenuOpen] = useState(false);
    const [isSideMenuBlocked, setIsSideMenuBlocked] = useState(false);

    const {width: viewportWidth} = useViewportSize();

    const isMobileViewport = useMemo(
        () => viewportWidth > 0 && viewportWidth <= 1440,
        [viewportWidth]
    );

    useEffect(() => {
        if (isMobileViewport) {
            setIsSideMenuBlocked(true);
            setIsSideMenuOpen(false);
        } else {
            setIsSideMenuBlocked(false);
        }
    }, [isMobileViewport]);

    const value = useMemo(
        () => ({
            isSideMenuOpen,
            isSideMenuBlocked,
            setIsSideMenuOpen
        }),
        [isSideMenuOpen, isSideMenuBlocked, setIsSideMenuOpen]
    );

    return (
        <LayoutContext.Provider value={value}>
            {children}
        </LayoutContext.Provider>
    );
});

if (isDev) {
    LayoutProvider.displayName = 'LayoutProvider';
}

import {FC, memo} from 'react';
import storeConfig from '~/store.config';
import {cls, isDev} from '@core/helpers';
import {
    useCart,
    useIOSDevice,
    useMobile,
    useTrans,
    useStore
} from '@core/hooks';
import {UiLink} from '@core/components/ui';
import {
    BagIcon,
    StoreIcon,
    PaymentIcon,
    InboxIcon,
    TagsIcon,
    ChartTreeMapIcon
} from '@core/icons/solid';

const MobileTabBar: FC = memo(() => {
    const t = useTrans();
    const {paymentOnly} = useStore();
    const {itemCount: cartItemsCount} = useCart();
    const {activeTab} = useMobile();

    const {isIOSDevice} = useIOSDevice();

    return (
        <div
            className="fixed bottom-0 left-0 z-[48] block w-full select-none border-t bg-white shadow xl:hidden"
            style={{
                height: `${
                    isIOSDevice
                        ? storeConfig.theme.iosTabBarHeight
                        : storeConfig.theme.mobileTabBarHeight
                }`
            }}
        >
            <div className="container h-full">
                <div className="flex h-full items-start justify-between">
                    {!paymentOnly ? (
                        <UiLink
                            className="flex flex-1 flex-col items-center justify-between py-3"
                            href="/"
                        >
                            <StoreIcon
                                className={cls(
                                    'h-5 w-5',
                                    activeTab === 'store'
                                        ? 'text-primary-600'
                                        : 'text-gray-700'
                                )}
                            />
                            <div
                                className={cls(
                                    'h-[10px] pt-1.5 text-[10px] leading-[10px]',
                                    activeTab === 'store'
                                        ? 'text-primary-600'
                                        : 'text-gray-700'
                                )}
                            >
                                {t('Store')}
                            </div>
                        </UiLink>
                    ) : (
                        <UiLink
                            className="flex flex-1 flex-col items-center justify-between py-3"
                            href="/"
                        >
                            <PaymentIcon
                                className={cls(
                                    'h-5 w-5',
                                    activeTab === 'store'
                                        ? 'text-primary-600'
                                        : 'text-gray-700'
                                )}
                            />
                            <div
                                className={cls(
                                    'h-[10px] pt-1.5 text-[10px] leading-[10px]',
                                    activeTab === 'store'
                                        ? 'text-primary-600'
                                        : 'text-gray-700'
                                )}
                            >
                                {t('Payment')}
                            </div>
                        </UiLink>
                    )}

                    {!paymentOnly && (
                        <>
                            <UiLink
                                className="flex flex-1 flex-col items-center justify-between py-3"
                                href="/mobile/categories"
                            >
                                <TagsIcon
                                    className={cls(
                                        'h-5 w-5',
                                        activeTab === 'categories'
                                            ? 'text-primary-600'
                                            : 'text-gray-700'
                                    )}
                                />
                                <div
                                    className={cls(
                                        'h-[10px] pt-1.5 text-[10px] leading-[10px]',
                                        activeTab === 'categories'
                                            ? 'text-primary-600'
                                            : 'text-gray-700'
                                    )}
                                >
                                    {t('Categories')}
                                </div>
                            </UiLink>

                            <UiLink
                                className="flex flex-1 flex-col items-center justify-between py-3"
                                href="/mobile/my-cart"
                            >
                                <div className="relative flex flex-1 flex-col items-center justify-between">
                                    <BagIcon
                                        className={cls(
                                            'h-5 w-5',
                                            activeTab === 'my-cart'
                                                ? 'text-primary-600'
                                                : 'text-gray-700'
                                        )}
                                    />
                                    <div
                                        className={cls(
                                            'h-[10px] pt-1.5 text-[10px] leading-[10px]',
                                            activeTab === 'my-cart'
                                                ? 'text-primary-600'
                                                : 'text-gray-700'
                                        )}
                                    >
                                        {t('My Cart')}
                                    </div>

                                    {cartItemsCount > 0 && (
                                        <span
                                            className="absolute -top-1.5 right-0.5 flex items-center justify-center rounded-full bg-red-600 text-[9px] font-medium text-white"
                                            style={{
                                                paddingLeft: '2px',
                                                paddingRight: '2px',
                                                minWidth: '1rem',
                                                minHeight: '1rem'
                                            }}
                                        >
                                            {cartItemsCount}
                                        </span>
                                    )}
                                </div>
                            </UiLink>

                            <UiLink
                                className="flex flex-1 flex-col items-center justify-between py-3"
                                href="/quick-order"
                            >
                                <InboxIcon
                                    className={cls(
                                        'h-5 w-5',
                                        activeTab === 'quick-order'
                                            ? 'text-primary-600'
                                            : 'text-gray-700'
                                    )}
                                />
                                <div
                                    className={cls(
                                        'h-[10px] pt-1.5 text-[10px] leading-[10px]',
                                        activeTab === 'quick-order'
                                            ? 'text-primary-600'
                                            : 'text-gray-700'
                                    )}
                                >
                                    {t('Quick Order')}
                                </div>
                            </UiLink>
                        </>
                    )}

                    <UiLink
                        className="flex flex-1 flex-col items-center justify-between py-3"
                        href="/mobile/menu"
                    >
                        <ChartTreeMapIcon
                            className={cls(
                                'h-5 w-5',
                                activeTab === 'menu'
                                    ? 'text-primary-600'
                                    : 'text-gray-700'
                            )}
                        />
                        <div
                            className={cls(
                                'h-[10px] pt-1.5 text-[10px] leading-[10px]',
                                activeTab === 'menu'
                                    ? 'text-primary-600'
                                    : 'text-gray-700'
                            )}
                        >
                            {t('Menu')}
                        </div>
                    </UiLink>
                </div>
            </div>
        </div>
    );
});

if (isDev) {
    MobileTabBar.displayName = 'MobileTabBar';
}

export default MobileTabBar;

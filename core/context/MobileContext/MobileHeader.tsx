import {FC, memo, useCallback, useMemo} from 'react';
import {useRouter} from 'next/router';
import {isDev} from '@core/helpers';
import {useMobile, useStore} from '@core/hooks';
import {UiImage, UiLink} from '@core/components/ui';
import {ArrowLeftIcon, SearchIcon} from '@core/icons/solid';
import storeConfig from '~/store.config';

type MobileHeaderProps = {
    title?: string;
};

const MobileHeader: FC<MobileHeaderProps> = memo(({title}) => {
    const router = useRouter();
    const {paymentOnly} = useStore();
    const {mobileTitle, setIsMobileSearchShown} = useMobile();

    const onGoBack = useCallback(() => router.back(), [router]);

    const isHome = useMemo(() => router.asPath === '/home', [router.asPath]);

    return (
        <div className="fixed left-0 right-0 top-0 z-[48] flex h-mobile-header w-full items-center border-b bg-white xl:hidden">
            {isHome ? (
                <div className="container flex w-full items-center justify-between py-3">
                    <UiLink href="/" aria-label="Logo">
                        <UiImage
                            src="/site-logo.png"
                            alt={storeConfig.title}
                            width={parseFloat(
                                storeConfig.theme.mobileLogoWidth.replace(
                                    'px',
                                    ''
                                )
                            )}
                            height={parseFloat(
                                storeConfig.theme.mobileLogoHeight.replace(
                                    'px',
                                    ''
                                )
                            )}
                            priority
                        />
                    </UiLink>

                    {!paymentOnly && (
                        <button
                            className="button-primary flex h-8 w-8 items-center justify-center transition active:opacity-50"
                            onClick={() => setIsMobileSearchShown(true)}
                        >
                            <SearchIcon className="h-3.5 w-3.5 text-gray-700" />
                        </button>
                    )}
                </div>
            ) : (
                <div className="container flex items-center space-x-3">
                    <button
                        className="button-primary flex h-8 w-8 items-center justify-center transition active:opacity-50"
                        onClick={onGoBack}
                    >
                        <ArrowLeftIcon className="h-3.5 w-3.5 text-gray-700" />
                    </button>

                    <div className="min-w-0 flex-1">
                        <div className="w-full truncate font-semibold">
                            {!!title ? title : mobileTitle}
                        </div>
                    </div>

                    {!paymentOnly && (
                        <button
                            className="button-primary flex h-8 w-8 items-center justify-center transition active:opacity-50"
                            onClick={() => setIsMobileSearchShown(true)}
                        >
                            <SearchIcon className="h-3.5 w-3.5 text-gray-700" />
                        </button>
                    )}
                </div>
            )}
        </div>
    );
});

if (isDev) {
    MobileHeader.displayName = 'MobileHeader';
}

export default MobileHeader;

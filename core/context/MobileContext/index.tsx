import {createContext, FC, memo, useEffect, useMemo, useState} from 'react';
import {useRouter} from 'next/router';
import {cls, isDev} from '@core/helpers';
import {useCustomer, useViewportSize} from '@core/hooks';
import {MobileContextType} from './types';
import MobileHeader from './MobileHeader';
import MobileTabBar from './MobileTabBar';
import MobileSearch from './MobileSearch';
import {useSession} from 'next-auth/react';

export const MobileContext = createContext<MobileContextType>(null as any);

if (isDev) {
    MobileContext.displayName = 'MobileContext';
}

export const MobileProvider: FC<{children: React.ReactNode}> = memo(
    ({children}) => {
        const router = useRouter();
        const {width: viewportWidth} = useViewportSize();
        const [mobileTitle, setMobileTitle] = useState('');
        const [isMobileSearchShown, setIsMobileSearchShown] = useState(false);

        const [isMobile, setIsMobile] = useState(false);
        const isMobileViewport = useMemo(
            () => viewportWidth > 0 && viewportWidth < 1024,
            [viewportWidth]
        );
        useEffect(() => {
            if (isMobileViewport) {
                setIsMobile(true);
            }
        }, [isMobileViewport]);

        const activeTab = useMemo(() => {
            const path = router.asPath;

            if (path.includes('/mobile/categories')) {
                return 'categories';
            } else if (path.includes('/mobile/my-cart')) {
                return 'my-cart';
            } else if (path.includes('/quick-order')) {
                return 'quick-order';
            } else if (path.includes('/mobile/menu')) {
                return 'menu';
            }
            return 'store';
        }, [router.asPath]);

        const value: any = useMemo(
            () => ({
                mobileTitle,
                isMobileSearchShown,
                activeTab,
                isMobile,
                setMobileTitle,
                setIsMobileSearchShown
            }),
            [
                mobileTitle,
                isMobileSearchShown,
                activeTab,
                isMobile,
                setIsMobileSearchShown
            ]
        );

        const customer = useCustomer();

        const activateMobileProvider = useMemo(
            () => customer && router.asPath !== '/auth',
            [customer, router.asPath]
        );

        return (
            <MobileContext.Provider value={value}>
                <div
                    className={cls(
                        'relative h-full w-full overflow-hidden xl:py-0',
                        {
                            'pb-mobile-header pt-mobile-header':
                                activateMobileProvider
                        }
                    )}
                >
                    {activateMobileProvider && <MobileHeader />}

                    <div className="content-wrapper h-full w-full overflow-y-scroll">
                        {children}
                    </div>

                    {activateMobileProvider && <MobileTabBar />}
                </div>

                <MobileSearch />
            </MobileContext.Provider>
        );
    }
);

if (isDev) {
    MobileProvider.displayName = 'MobileProvider';
}

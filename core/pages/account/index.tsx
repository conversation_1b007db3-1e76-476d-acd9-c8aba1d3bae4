import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useCustomer, useTrans} from '@core/hooks';
import Seo from '@components/common/Seo';
import {
    BellIcon,
    CheckCircleIcon,
    BookmarkIcon,
    BagIcon,
    StarIcon,
    StoreIcon
} from '@core/icons/solid';
import {UiAvatar, UiLink} from '@core/components/ui';

interface AccountLinkProps {
    icon: JSX.Element;
    title: string;
    description: string;
    href: string;
}

const AccountLink: FC<AccountLinkProps> = ({
    description,
    href,
    icon,
    title
}) => {
    const t = useTrans();

    return (
        <UiLink
            href={href}
            className="card-container hover:shadow-small flex items-center gap-6 border-2 border-transparent p-6 transition hover:border-primary-600"
        >
            <div className="flex h-11 w-11 items-center justify-center">
                {icon}
            </div>
            <div className="flex-1 space-y-1">
                <p className="font-semibold">{t(title)}</p>
                <p className="text-sm">{t(description)}</p>
            </div>
        </UiLink>
    );
};

const Account: FC = memo(() => {
    const t = useTrans();

    const customer = useCustomer();

    return (
        <>
            <Seo title={t('Account')} />

            <div className="flex items-center space-x-5">
                <UiAvatar
                    className="bg-primary-600 text-white"
                    name={customer?.name}
                    size="3xl"
                />

                <div className="flex-1">
                    <div className="font-medium">{customer?.name}</div>
                    <div className="text-muted">{customer?.email}</div>
                </div>
            </div>

            <div className="mt-6 h-[1px] w-full bg-secondary-400"></div>

            <div className="mt-6 grid auto-rows-fr gap-4 lg:grid-cols-2">
                <AccountLink
                    title="Siparişlerim"
                    description="Son siparişlerin durumunu kontrol edin, iadeleri
                    yönetin ve benzer ürünleri keşfedin."
                    href="/account/my-orders"
                    icon={
                        <CheckCircleIcon className="h-6 w-6 text-primary-600" />
                    }
                />
                <AccountLink
                    title="Favorilerim"
                    description="Son favorilerinizi kontrol edin."
                    href="/account/my-favorites"
                    icon={<BellIcon className="h-6 w-6 text-primary-600" />}
                />
                <AccountLink
                    title="Koleksiyonlarım"
                    description="Kokleksiyonlarım özelliğiyle tüm ürünler her an elinizin altında."
                    href="/account/my-collections"
                    icon={<BookmarkIcon className="h-6 w-6 text-primary-600" />}
                />
                <AccountLink
                    title="Değerlendirmelerim"
                    description="Son değerlendirmelerinizi kontrol edin."
                    href="/account/my-reviews"
                    icon={<BagIcon className="h-6 w-6 text-primary-600" />}
                />
                <AccountLink
                    title="Adreslerim"
                    description="Teslimat ve fatura adreslerinizi yönetin."
                    href="/account/my-addresses"
                    icon={<StarIcon className="h-6 w-6 text-primary-600" />}
                />
                <AccountLink
                    title="Hesabım"
                    description="Hesap bilgilerinizi yönetin."
                    href="/account/my-account"
                    icon={<StoreIcon className="h-6 w-6 text-primary-600" />}
                />
            </div>
        </>
    );
});

if (isDev) {
    Account.displayName = 'Account';
}

export default Account;

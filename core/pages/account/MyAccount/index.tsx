import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import Seo from '@components/common/Seo';
import Information from './Information';
import Password from './Password';
import GoBack from '@components/common/GoBack';

type MyAccountProps = {
    countries: Record<string, any>[];
};

const MyAccount: FC<MyAccountProps> = memo(({countries}) => {
    const t = useTrans();

    return (
        <>
            <Seo title={t('My Account')} />

            <GoBack />

            <div className="grid gap-4 xl:mb-0 xl:grid-cols-2 xl:gap-16">
                <div className="card-container p-8">
                    <h2 className="my-4 text-xl font-medium xl:mt-0">
                        {t('Account Information')}
                    </h2>

                    <Information countries={countries} />
                </div>

                <div className="card-container p-8">
                    <h2 className="my-4 text-xl font-medium xl:mt-0">
                        {t('Change Password')}
                    </h2>

                    <Password />
                </div>
            </div>
        </>
    );
});

if (isDev) {
    MyAccount.displayName = 'MyAccount';
}

export default MyAccount;

import {FC, memo, useCallback, useRef, useState} from 'react';
import {FormProvider, useForm} from 'react-hook-form';
import {isDev, jsonRequest} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {Ui<PERSON>lert, UiButton, UiForm, notification} from '@core/components/ui';
import {EyeIcon, EyeSlashIcon} from '@core/icons/solid';

const Password: FC = memo(() => {
    const methods = useForm();
    const {
        register,
        formState: {errors},
        watch,
        reset
    } = methods;
    const t = useTrans();
    const password: string = watch('password', '');
    const [isPasswordShown, setIsPasswordShown] = useState(false);
    const confirmation: string = watch('confirmation', '');
    const [isConfirmationShown, setIsConfirmationShown] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const inProgress = useRef(false);

    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgress.current) return;

            inProgress.current = true;
            setIsLoading(true);

            try {
                if (data.password !== data.confirmation) {
                    // noinspection ExceptionCaughtLocallyJS
                    throw new Error(
                        t(
                            'Password and password confirmation must be the same!'
                        )
                    );
                }
                await jsonRequest({
                    url: '/api/customers/change-password',
                    method: 'POST',
                    data: {
                        password: data.password
                    }
                });

                notification({
                    title: t('Password Changed'),
                    description: t(
                        'Your password has been successfully changed.'
                    ),
                    status: 'success'
                });
                setErrorMessage('');
                reset();
            } catch (error: any) {
                setErrorMessage(error.message);
            }

            setIsLoading(false);
            inProgress.current = false;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [reset]
    );

    return (
        <FormProvider {...methods}>
            <UiForm onSubmit={methods.handleSubmit(onSubmit)}>
                {!!errorMessage && (
                    <UiAlert className="mb-8" color="danger">
                        {t(errorMessage)}
                    </UiAlert>
                )}

                <UiForm.Field
                    label={t('Password')}
                    rightElement={
                        !!password &&
                        password.length > 0 && (
                            <div
                                className="cursor-pointer"
                                onClick={() =>
                                    setIsPasswordShown(!isPasswordShown)
                                }
                            >
                                {isPasswordShown && (
                                    <EyeSlashIcon className="h-5 w-5 text-muted" />
                                )}
                                {!isPasswordShown && (
                                    <EyeIcon className="h-5 w-5 text-muted" />
                                )}
                            </div>
                        )
                    }
                    type={isPasswordShown ? 'text' : 'password'}
                    error={
                        errors.password && errors.password.type === 'required'
                            ? t('Password is required')
                            : undefined
                    }
                    {...register('password', {required: true})}
                />

                <UiForm.Field
                    className="mt-2 xl:mt-4"
                    label={t('Password confirmation')}
                    rightElement={
                        !!confirmation &&
                        confirmation.length > 0 && (
                            <div
                                className="cursor-pointer"
                                onClick={() =>
                                    setIsConfirmationShown(!isConfirmationShown)
                                }
                            >
                                {isConfirmationShown && (
                                    <EyeSlashIcon className="h-5 w-5 text-muted" />
                                )}
                                {!isConfirmationShown && (
                                    <EyeIcon className="h-5 w-5 text-muted" />
                                )}
                            </div>
                        )
                    }
                    type={isConfirmationShown ? 'text' : 'password'}
                    error={
                        errors.confirmation &&
                        errors.confirmation.type === 'required'
                            ? t('Password confirmation is required')
                            : undefined
                    }
                    {...register('confirmation', {required: true})}
                />

                <UiButton
                    className="mt-2 w-full border-2 xl:mt-6"
                    color="primary"
                    size="xl"
                    loading={isLoading}
                    disabled={isLoading}
                >
                    {t('Change Password')}
                </UiButton>
            </UiForm>
        </FormProvider>
    );
});

if (isDev) {
    Password.displayName = 'Password';
}

export default Password;

import {FC, memo, useCallback, useEffect, useRef, useState} from 'react';
import {isDev, jsonRequest, randomId} from '@core/helpers';
import {useIntersection, useStore, useTrans} from '@core/hooks';
import {ProductListItem} from '@core/types';
import {HeartIcon} from '@core/icons/regular';
import ProductCard from '@components/common/ProductCard';
import Seo from '@components/common/Seo';
import GoBack from '@components/common/GoBack';

type MyFavoritesProps = {
    products: ProductListItem[];
    total: number;
    hasNextPage: boolean;
};

const MyFavorites: FC<MyFavoritesProps> = memo(props => {
    const t = useTrans();
    const {
        products: initialProducts,
        total: initialTotal,
        hasNextPage: initialHasNextPage
    } = props;
    const [ref, observer] = useIntersection();
    const {removeFromFavorites} = useStore();
    const [products, setProducts] = useState<ProductListItem[]>(
        () => initialProducts
    );
    const [total, setTotal] = useState(() => initialTotal);
    const [hasNextPage, setHasNextPage] = useState(() => initialHasNextPage);
    const [isLoading, setIsLoading] = useState(false);
    const limit = useRef(32);
    const skip = useRef(0);
    const inProgress = useRef(false);
    const loadMoreRequested = useRef(false);

    // Load more.
    useEffect(() => {
        if (observer?.isIntersecting) {
            if (inProgress.current) {
                loadMoreRequested.current = true;

                return;
            }

            inProgress.current = true;
            setIsLoading(true);
            skip.current += limit.current;

            setProducts(currentProducts =>
                currentProducts.concat(
                    [...Array(limit.current)].map(
                        () =>
                            ({
                                productId: randomId(16),
                                isFake: true
                            } as any)
                    )
                )
            );

            const load = async () => {
                const result = await jsonRequest({
                    url: '/api/customers/favorite-products',
                    method: 'POST',
                    data: {
                        skip: skip.current,
                        limit: limit.current
                    }
                });

                setProducts(currentProducts => {
                    currentProducts = currentProducts.filter(
                        // @ts-ignore
                        currentProduct => !currentProduct.isFake
                    );

                    return currentProducts.concat(result.data);
                });
                setTotal(result.total);
                setHasNextPage(result.hasNextPage);
                setIsLoading(false);
                inProgress.current = false;

                if (loadMoreRequested.current) {
                    loadMoreRequested.current = false;

                    if (result.hasNextPage) {
                        await load();
                    }
                }
            };

            load();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [observer]);

    // On Remove.
    const onRemove = useCallback(
        async (product: ProductListItem) => {
            if (inProgress.current) {
                return;
            }

            inProgress.current = true;
            setIsLoading(true);

            try {
                await removeFromFavorites({
                    id: product.productId,
                    name: product.name,
                    image:
                        Array.isArray(product.images) &&
                        product.images.length > 0
                            ? product.images[0]
                            : '/no-image.png',
                    price: product.salesPrice
                });
                setProducts(currentProducts => {
                    currentProducts = currentProducts.filter(
                        currentProduct =>
                            currentProduct.productId !== product.productId
                    );

                    return currentProducts;
                });

                const result = await jsonRequest({
                    url: '/api/customers/favorite-products',
                    method: 'POST',
                    data: {
                        skip: 0,
                        limit: 1
                    }
                });
                setTotal(result.total);
            } catch (error: any) {}

            inProgress.current = false;
            setIsLoading(false);
        },
        [removeFromFavorites]
    );

    return (
        <>
            <Seo title={t('My Favorites')} />

            <GoBack />

            {total > 0 && (
                <div className="card-container mt-4 grid grid-cols-2 gap-2 p-4 xl:mt-0 xl:grid-cols-5 xl:gap-4">
                    {products.map(product => {
                        return (
                            <ProductCard
                                key={product.productId}
                                product={product}
                                isFavoriteShown={false}
                                isUnDiscountedPriceShown={false}
                                onRemove={onRemove}
                                // @ts-ignore
                                isFake={product.isFake}
                            />
                        );
                    })}
                </div>
            )}

            {total > 0 && !isLoading && hasNextPage && <span ref={ref}></span>}

            {total < 1 && !isLoading && (
                <div className="card-container flex flex-1 flex-col items-center justify-center py-12 xl:px-12 xl:py-24">
                    <div className="flex h-24 w-24 items-center justify-center rounded-md border border-dashed border-gray-500 text-muted ">
                        <HeartIcon className="h-8 w-8" />
                    </div>

                    <h2 className="pt-8 text-center text-2xl font-semibold">
                        {t('No favorites found!')}
                    </h2>

                    <p className="px-10 pt-2 text-center text-muted">
                        {t('There are no favorites in your account.')}
                    </p>
                </div>
            )}
        </>
    );
});

if (isDev) {
    MyFavorites.displayName = 'MyFavorites';
}

export default MyFavorites;

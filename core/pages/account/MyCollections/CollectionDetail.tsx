import {FC, memo, useCallback, useEffect, useRef, useState} from 'react';
import {FormProvider, useForm} from 'react-hook-form';
import {isDev, jsonRequest} from '@core/helpers';
import {useTrans, useUI} from '@core/hooks';
import {Collection} from '@core/types';
import {UiAlert, UiButton, UiForm, notification} from '@core/components/ui';

type CollectionDetailProps = {
    collection?: Collection;
    onSave: (collection: Collection) => void;
};

const CollectionDetail: FC<CollectionDetailProps> = memo(props => {
    const {collection, onSave} = props;
    const methods = useForm();
    const {
        register,
        formState: {errors},
        setValue
    } = methods;
    const t = useTrans();
    const {closeSideBar} = useUI();
    const [errorMessage, setErrorMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const inProgress = useRef(false);

    useEffect(() => {
        if (typeof collection === 'undefined') return;

        if (typeof collection.name !== 'undefined')
            setValue('name', collection.name);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [collection]);

    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgress.current) return;

            inProgress.current = true;
            setIsLoading(true);

            try {
                const newCollection: Collection = await jsonRequest({
                    url: '/api/customers/save-collection',
                    method: 'POST',
                    data: {
                        name: data.name,
                        ...(typeof collection !== 'undefined'
                            ? {collectionId: collection.id}
                            : {})
                    }
                });

                onSave(newCollection);
                notification({
                    title: t('Collection Saved'),
                    description: t(
                        'The collection has been successfully saved.'
                    ),
                    status: 'success'
                });
                closeSideBar();
            } catch (error: any) {
                setErrorMessage(error.message);
                setIsLoading(false);
            }

            inProgress.current = false;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [collection]
    );

    return (
        <div className="mt-4 flex flex-col px-4 pb-4 xl:mt-2 xl:px-6 xl:pb-6">
            <FormProvider {...methods}>
                <UiForm onSubmit={methods.handleSubmit(onSubmit)}>
                    {!!errorMessage && (
                        <UiAlert className="mb-8" color="danger">
                            {t(errorMessage)}
                        </UiAlert>
                    )}

                    <UiForm.Field
                        label={t('Collection name')}
                        error={
                            errors['name'] && errors['name'].type === 'required'
                                ? t('Name is required')
                                : undefined
                        }
                        {...register('name', {
                            required: true
                        })}
                    />

                    <UiButton
                        className="mt-4 w-full xl:mt-6"
                        variant="solid"
                        color="primary"
                        size="xl"
                        loading={isLoading}
                        disabled={isLoading}
                    >
                        {typeof collection === 'undefined'
                            ? t('Save Collection')
                            : t('Create Collection')}
                    </UiButton>
                </UiForm>
            </FormProvider>
        </div>
    );
});

if (isDev) {
    CollectionDetail.displayName = 'CollectionDetail';
}

export default CollectionDetail;

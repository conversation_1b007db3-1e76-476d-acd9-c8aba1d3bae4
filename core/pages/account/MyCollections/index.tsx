import {FC, memo, useCallback, useMemo, useState} from 'react';
import {clone, isDev, jsonRequest} from '@core/helpers';
import {useTrans, useUI} from '@core/hooks';
import {Collection} from '@core/types';
import {UiButton, UiLink} from '@core/components/ui';
import {
    BellIcon,
    BookmarkIcon,
    ClockIcon,
    PencilIcon,
    PlusIcon,
    TrashIcon
} from '@core/icons/regular';
import Seo from '@components/common/Seo';
import storeConfig from '~/store.config';
import CollectionDetail from './CollectionDetail';
import GoBack from '@components/common/GoBack';

type MyCollectionsProps = {
    collections: Collection[];
};

const MyCollections: FC<MyCollectionsProps> = memo(props => {
    const {collections: initialCollections} = props;
    const t = useTrans();
    const {openSideBar, confirm} = useUI();
    const [collections, setCollections] = useState(() => initialCollections);

    const buyLaterCollection = useMemo(
        () => collections.find(collection => !!collection.isBuyLater),
        [collections]
    );
    const alarmCollection = useMemo(
        () => collections.find(collection => !!collection.isAlarm),
        [collections]
    );
    const myCollections = useMemo(
        () =>
            collections.filter(
                collection => !collection.isBuyLater && !collection.isAlarm
            ),
        [collections]
    );

    const onCollectionSaved = useCallback(
        (collection: Collection) => {
            const newCollections = clone(collections);
            const index = newCollections.findIndex(c => c.id === collection.id);

            if (index !== -1) {
                newCollections[index] = collection;
            } else {
                newCollections.push(collection);
            }

            setCollections(newCollections);
        },
        [collections]
    );
    const onNewCollection = useCallback(() => {
        openSideBar(
            t('New Collection'),
            <CollectionDetail onSave={onCollectionSaved} />
        );
    }, [t, openSideBar, onCollectionSaved]);
    const onUpdateCollection = useCallback(
        (collection: Collection) => {
            openSideBar(
                t('Update Collection'),
                <CollectionDetail
                    collection={collection}
                    onSave={onCollectionSaved}
                />
            );
        },
        [t, openSideBar, onCollectionSaved]
    );
    const onDeleteCollection = useCallback(
        (collection: Collection) => {
            confirm(
                t('Delete Collection'),
                t(
                    'You are about to delete the collection. This action will not be reversible.'
                ),
                t('Delete Collection'),
                async () => {
                    try {
                        await jsonRequest({
                            url: '/api/customers/remove-collection',
                            method: 'POST',
                            data: {collectionId: collection.id}
                        });

                        setCollections(
                            collections.filter(c => c.id !== collection.id)
                        );
                    } catch (error: any) {
                        console.log(error);
                    }
                }
            );
        },
        [t, collections, confirm]
    );

    return (
        <>
            <Seo title={t('My Collections')} />

            <GoBack />

            <div className="card-container my-4 p-8">
                <div className="mb-4 flex items-center justify-between">
                    <h2 className="font-medium xl:text-lg">
                        {storeConfig.title}
                    </h2>
                    <UiButton
                        variant="solid"
                        color="primary"
                        leftIcon={<PlusIcon className="mr-2 h-4 w-4" />}
                        onClick={onNewCollection}
                    >
                        {t('Create Collection')}
                    </UiButton>
                </div>

                <div className="grid grid-cols-1 gap-2 xl:grid-cols-4 xl:gap-4">
                    <UiLink
                        className="group flex cursor-pointer select-none items-center rounded-md border bg-white p-4 text-sm shadow-sm outline outline-1 outline-transparent transition hover:border-primary-600 hover:outline-primary-600 focus:border-secondary-300 focus:outline-none"
                        href={`/account/my-collections/${
                            buyLaterCollection!.id
                        }`}
                    >
                        <div className="mr-4 flex items-center">
                            <ClockIcon className="h-8 w-8 text-muted" />
                        </div>

                        <div className="flex-1 overflow-hidden">
                            <div className="mb-1 truncate font-medium">
                                {t(buyLaterCollection!.name)}
                            </div>
                            <div className="text-muted">
                                {t('{count} product(s)', {
                                    count: buyLaterCollection!.itemCount
                                })}
                            </div>
                        </div>
                    </UiLink>

                    <UiLink
                        className="group flex cursor-pointer select-none items-center rounded-md border bg-white p-4 text-sm shadow-sm outline outline-1 outline-transparent transition hover:border-primary-600 hover:outline-primary-600 focus:border-secondary-300 focus:outline-none"
                        href={`/account/my-collections/${alarmCollection!.id}`}
                    >
                        <div className="mr-4 flex items-center">
                            <BellIcon className="h-8 w-8 text-muted" />
                        </div>

                        <div className="flex-1 overflow-hidden">
                            <div className="mb-1 truncate font-medium">
                                {t(alarmCollection!.name)}
                            </div>
                            <div className="text-muted">
                                {t('{count} product(s)', {
                                    count: alarmCollection!.itemCount
                                })}
                            </div>
                        </div>
                    </UiLink>
                </div>

                {myCollections.length > 0 && (
                    <>
                        <h2 className="mb-3 mt-12 hidden text-lg font-medium xl:block">
                            {t('My Collections')}
                        </h2>
                        <div className="grid grid-cols-1 gap-2 xl:grid-cols-3 xl:gap-4">
                            {myCollections.map(collection => (
                                <UiLink
                                    key={collection.id}
                                    className="group flex cursor-pointer select-none items-center rounded-md border bg-white p-4 text-sm shadow-sm transition hover:border-primary-600 hover:bg-primary-50 hover:text-primary-600 focus:outline-none"
                                    href={`/account/my-collections/${collection.id}`}
                                >
                                    <div className="mr-4 flex items-center">
                                        <BookmarkIcon className="h-8 w-8 text-muted transition group-hover:text-primary-600" />
                                    </div>

                                    <div className="flex-1 overflow-hidden">
                                        <div className="mb-1 truncate font-medium">
                                            {t(collection!.name)}
                                        </div>
                                        <div className="text-muted">
                                            {t('{count} product(s)', {
                                                count: collection!.itemCount
                                            })}
                                        </div>
                                    </div>

                                    <div className="ml-4 flex h-full items-center space-x-1">
                                        <button
                                            className="group/inner cursor-pointer rounded-full p-1.5 transition hover:bg-warning-600"
                                            onClick={e => {
                                                e.preventDefault();
                                                e.stopPropagation();

                                                onUpdateCollection(collection);
                                            }}
                                        >
                                            <PencilIcon className="h-4 w-4 transition group-hover/inner:text-white" />
                                        </button>

                                        <button
                                            className="group/inner cursor-pointer rounded-full p-1.5 transition hover:bg-danger-600"
                                            onClick={e => {
                                                e.preventDefault();
                                                e.stopPropagation();
                                                onDeleteCollection(collection);
                                            }}
                                        >
                                            <TrashIcon className="h-4 w-4 transition group-hover/inner:text-white" />
                                        </button>
                                    </div>
                                </UiLink>
                            ))}
                        </div>
                    </>
                )}
            </div>
        </>
    );
});

if (isDev) {
    MyCollections.displayName = 'MyCollections';
}

export default MyCollections;

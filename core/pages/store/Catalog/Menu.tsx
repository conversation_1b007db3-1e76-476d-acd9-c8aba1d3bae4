import {useCallback, useMemo, useState} from 'react';
import {useRouter} from 'next/router';
import {UiButton} from '@core/components/ui';
import {useStore, useTrans} from '@core/hooks';
import {NavigationItem} from '@core/types';
import {ArrowLeftIcon} from '@core/icons/solid';
import {cls} from '@core/helpers';
import {ChevronDownIcon} from '@core/icons/solid';

const Menu = () => {
    const [currentId, setCurrentId] = useState<string | undefined>();

    const {navigation} = useStore();

    const router = useRouter();

    const t = useTrans();

    const currentItem = useMemo(
        () =>
            navigation.find(navigationItem => navigationItem.id === currentId),
        [currentId, navigation]
    );

    const navigationItems = useMemo(() => {
        if (currentItem === undefined) {
            return navigation.filter(
                navigationItem =>
                    navigationItem.showInMainMenu &&
                    navigationItem.slug.split('/').length == 1
            );
        }

        return navigation.filter(
            navigationItem =>
                navigationItem.showInMainMenu &&
                navigationItem.slug.includes(currentItem.slug) &&
                navigationItem.id !== currentItem.id &&
                navigationItem.slug.split('/').length ==
                    currentItem.slug.split('/').length + 1
        );
    }, [navigation, currentItem]);

    const hasSubItems = useCallback(
        (item: NavigationItem) =>
            navigation.some(
                navigationItem =>
                    navigationItem.showInMainMenu &&
                    navigationItem.slug.includes(item.slug) &&
                    navigationItem.slug !== item.slug
            ),
        [navigation]
    );

    const prevNavItemSelector = useCallback(
        (item: NavigationItem) => {
            const foundedItem = navigation.find(navigationItem => {
                const navigationSlugs = item.slug.split('/');
                navigationSlugs.pop();
                return navigationItem.slug == navigationSlugs.join('/');
            });
            setCurrentId(foundedItem?.id);
        },
        [navigation]
    );

    return (
        <aside className="menu-card sticky top-20 hidden h-screen w-1/5 overflow-y-auto pb-24 xl:block">
            <p className="mb-2 text-xl font-medium">{t('Menu')}</p>
            <div className="card-container flex flex-col items-start gap-0.5 overflow-hidden p-2">
                {currentItem && currentItem.type == 'product-catalog' && (
                    <div className="mb-1.5 w-full border-b pb-2">
                        <button
                            className="flex w-full cursor-pointer items-center gap-3 rounded-md bg-secondary-200 p-2 text-left text-sm transition hover:bg-secondary-100"
                            onClick={() => prevNavItemSelector(currentItem)}
                        >
                            <div className="inline-flex h-5 w-5 items-center justify-center rounded-md border bg-white">
                                <ArrowLeftIcon className="h-3 w-3 fill-primary-600" />
                            </div>
                            <p className="flex-1 font-medium">
                                {currentItem.name}
                            </p>
                        </button>
                    </div>
                )}

                {navigationItems.map(navigationItem => (
                    <button
                        className={cls(
                            'roll-out flex w-full cursor-pointer items-center gap-4 rounded-md p-2 text-left text-sm transition hover:bg-secondary-100',
                            router.asPath.includes(navigationItem.slug) &&
                                'bg-secondary-200'
                        )}
                        key={navigationItem.id}
                        onClick={() => {
                            hasSubItems(navigationItem)
                                ? setCurrentId(navigationItem.id)
                                : router.push(navigationItem.href);
                        }}
                    >
                        {navigationItem.svgIcon && (
                            <div
                                className="inline-flex h-4 w-4 items-center"
                                dangerouslySetInnerHTML={{
                                    __html: navigationItem.svgIcon
                                }}
                            />
                        )}
                        <p className="flex-1 font-medium">
                            {navigationItem.name}
                        </p>
                        <ChevronDownIcon className="h-3 w-3 -rotate-90 fill-primary-600 stroke-primary-600 stroke-[24px]" />
                    </button>
                ))}

                {currentItem && currentItem.type == 'product-catalog' && (
                    <div className="mt-1.5 w-full border-t">
                        <UiButton
                            variant="solid"
                            color="primary"
                            className="mt-2 w-full focus:!bg-primary-600 active:!bg-primary-600"
                            onClick={() => router.push(currentItem.href)}
                        >
                            {t('Show All Products')}
                        </UiButton>
                    </div>
                )}
            </div>
        </aside>
    );
};

export default Menu;

import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import useCatalog from '../useCatalog';
import AllFilters from './AllFilters';
import PartialFilters from './PartialFilters';
import AppliedFilters from './AppliedFilters';
import Sort from './Sort';
import {UiCheckbox} from '@core/components/ui';
import {useTrans} from '@core/hooks';

const howManyFilterCategoryToShow = 4;

const Filters: FC = memo(() => {
    const {
        filters,
        appliedFilters,
        isLoading,
        inStockOnly,
        handleInStockChange
    } = useCatalog();

    const t = useTrans();

    return (
        <div className="mb-4 hidden flex-col border-b pb-4 xl:flex">
            <div className="flex items-center justify-end">
                {!isLoading ? (
                    Array.isArray(filters) &&
                    filters.length > 0 && (
                        <div className="h-8 w-full xl:flex xl:items-center xl:justify-between xl:gap-2.5">
                            <div className="flex gap-2.5">
                                {filters.map((filter, index) => {
                                    if (index >= howManyFilterCategoryToShow)
                                        return;
                                    return (
                                        <PartialFilters
                                            key={filter.label + filter.field}
                                            filter={filter}
                                        />
                                    );
                                })}
                                <AllFilters />
                            </div>
                        </div>
                    )
                ) : (
                    <div className="skeleton-card h-8 w-full"></div>
                )}
                {!isLoading ? (
                    <div className="hidden w-fit items-center justify-end gap-4 xl:flex">
                        <label className="flex w-24 items-center gap-1 text-sm">
                            <UiCheckbox
                                name="inStockOnly"
                                checked={inStockOnly}
                                onChange={handleInStockChange}
                            />
                            {t('In Stock')}
                        </label>
                        <Sort />
                    </div>
                ) : (
                    <div className="skeleton-card h-8 w-fit"></div>
                )}
            </div>

            {appliedFilters.length > 0 && <AppliedFilters />}
        </div>
    );
});

if (isDev) {
    Filters.displayName = 'Filters';
}

export default Filters;

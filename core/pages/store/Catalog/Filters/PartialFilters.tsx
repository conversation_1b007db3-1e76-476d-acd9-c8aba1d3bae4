import {UiTransition, UiInput, UiCheckbox} from '@core/components/ui';
import {toLower, trim, cls, isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {ChevronDownIcon} from '@core/icons/solid';
import {Filter} from '@core/types';
import {Popover} from '@headlessui/react';
import {FC, memo, useState, useMemo, Fragment} from 'react';
import useCatalog from '../useCatalog';
import PriceFilter from './PriceFilters';

type FilterProps = {
    filter: Filter;
};

const PartialFilters: FC<FilterProps> = memo(({filter}) => {
    const t = useTrans();
    const [search, setSearch] = useState('');
    const {appliedFilters, addFilter, removeFilter} = useCatalog();

    const filteredItems = useMemo(
        () =>
            filter.items.filter(
                item =>
                    toLower(item.label).indexOf(toLower(trim(search))) !== -1
            ),
        [filter.items, search]
    );

    return (
        <Popover className="relative">
            {({open}) => (
                <>
                    <Popover.Button
                        className={cls(
                            'button-primary group inline-flex items-center px-3.5 py-1.5 text-sm font-medium transition hover:bg-secondary-100',
                            {
                                'border-primary-600 ring-1 ring-primary-600':
                                    open
                            }
                        )}
                    >
                        <span>{t(filter.label)}</span>
                        <ChevronDownIcon
                            className={cls(
                                'ml-2.5 h-3.5 w-3.5 stroke-black stroke-[20px] transition group-hover:text-opacity-80',
                                {'-rotate-180': open}
                            )}
                        />
                    </Popover.Button>
                    <UiTransition
                        show={open}
                        as={Fragment}
                        enter="transition ease-out duration-200"
                        enterFrom="opacity-0 translate-y-1"
                        enterTo="opacity-100 translate-y-0"
                        leave="transition ease-in duration-150"
                        leaveFrom="opacity-100 translate-y-0"
                        leaveTo="opacity-0 translate-y-1"
                    >
                        <Popover.Panel className="absolute left-0 z-20 mt-3 min-w-[240px] max-w-md transform">
                            <div className="button-primary overflow-hidden">
                                {filter.items.length > 10 && (
                                    <div className="p-3">
                                        <UiInput
                                            className="border bg-white shadow-sm transition focus:bg-white"
                                            size="md"
                                            placeholder={t(
                                                `{artifact} search`,
                                                {
                                                    artifact: t(filter.label)
                                                }
                                            )}
                                            onChange={e =>
                                                setSearch(e.target.value)
                                            }
                                        />
                                    </div>
                                )}

                                <div className="scroller max-h-96 space-y-5 overflow-auto p-4">
                                    {filter.type === 'price' ? (
                                        <PriceFilter filter={filter} />
                                    ) : (
                                        filteredItems.map(item => (
                                            <div
                                                key={item.value}
                                                className="group flex items-center"
                                            >
                                                <UiCheckbox
                                                    size="lg"
                                                    className="rounded-full"
                                                    checked={
                                                        appliedFilters.findIndex(
                                                            appliedFilter =>
                                                                appliedFilter.field ===
                                                                    filter.field &&
                                                                appliedFilter
                                                                    .selected
                                                                    .value ===
                                                                    item.value
                                                        ) !== -1
                                                    }
                                                    onChange={e =>
                                                        // @ts-ignore
                                                        e.target.checked
                                                            ? addFilter({
                                                                  type: filter.type,
                                                                  label: filter.label,
                                                                  field: filter.field,
                                                                  isColorAttribute:
                                                                      filter.isColorAttribute,
                                                                  selected: item
                                                              })
                                                            : removeFilter({
                                                                  type: filter.type,
                                                                  label: filter.label,
                                                                  field: filter.field,
                                                                  isColorAttribute:
                                                                      filter.isColorAttribute,
                                                                  selected: item
                                                              })
                                                    }
                                                >
                                                    {filter.isColorAttribute ? (
                                                        <div className="flex items-center">
                                                            <div
                                                                className="relative mr-1.5 h-4 w-4 overflow-hidden rounded-lg"
                                                                style={{
                                                                    backgroundColor:
                                                                        item.color
                                                                }}
                                                            >
                                                                {!item.color && (
                                                                    <svg
                                                                        className="absolute inset-0 h-full w-full stroke-2 text-red-600"
                                                                        viewBox="0 0 100 100"
                                                                        preserveAspectRatio="none"
                                                                        stroke="currentColor"
                                                                    >
                                                                        <line
                                                                            x1={
                                                                                0
                                                                            }
                                                                            y1={
                                                                                100
                                                                            }
                                                                            x2={
                                                                                100
                                                                            }
                                                                            y2={
                                                                                0
                                                                            }
                                                                            vectorEffect="non-scaling-stroke"
                                                                        />
                                                                    </svg>
                                                                )}
                                                            </div>

                                                            <span className="select-none transition group-hover:text-muted">
                                                                {item.label}
                                                            </span>
                                                        </div>
                                                    ) : (
                                                        <span className="select-none transition group-hover:text-muted">
                                                            {item.label}
                                                        </span>
                                                    )}
                                                </UiCheckbox>
                                            </div>
                                        ))
                                    )}
                                </div>
                            </div>
                        </Popover.Panel>
                    </UiTransition>
                </>
            )}
        </Popover>
    );
});

if (isDev) {
    PartialFilters.displayName = 'PartialFilters';
}

export default PartialFilters;

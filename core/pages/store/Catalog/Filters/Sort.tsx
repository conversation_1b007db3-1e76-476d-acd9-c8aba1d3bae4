import {FC, Fragment, memo, useEffect, useRef, useState} from 'react';
import {useStore, useTrans} from '@core/hooks';
import {UiCheckbox, UiListBox, UiTransition} from '@core/components/ui';
import {cls, isDev} from '@core/helpers';
import useCatalog from '../useCatalog';

const Sort: FC = memo(() => {
    const t = useTrans();
    const {navigationItem} = useStore();
    const {sortOptions, sort, updateSort} = useCatalog();

    const [selectedSort, setSelectedSort] = useState<string>(
        typeof sort === 'object'
            ? `${sort.field}|${sort.direction}`
            : sortOptions[0].value
    );

    const isInitial = useRef(true);

    useEffect(() => {
        if (isInitial.current) {
            isInitial.current = false;
            return;
        }

        const [field, direction] = selectedSort.split('|');

        updateSort({field, direction: direction === 'desc' ? 'desc' : 'asc'});
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedSort]);

    return (
        <div className="ml-auto flex items-center justify-between">
            {!!navigationItem && !navigationItem.productSet && (
                <div>
                    <UiListBox
                        value={selectedSort}
                        onChange={setSelectedSort}
                        as="div"
                        className="relative space-y-3"
                    >
                        {({open}) => (
                            <>
                                <UiListBox.Button
                                    title={t('Sort')}
                                    className={cls(
                                        'button-primary inline-flex w-12 items-center justify-center px-3.5 py-1.5 text-sm font-medium transition hover:bg-secondary-100',
                                        {
                                            'border-primary-600 ring-1 ring-primary-600':
                                                open
                                        }
                                    )}
                                >
                                    <svg
                                        viewBox="0 0 16 16"
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-4 w-4"
                                    >
                                        <path
                                            fillRule="evenodd"
                                            clipRule="evenodd"
                                            d="M4.293 1.293a.997.997 0 0 1 1.414 0l3 3a1 1 0 0 1-1.414 1.414L6 4.414V9a1 1 0 1 1-2 0V4.414L2.707 5.707a1 1 0 0 1-1.414-1.414l3-3ZM10 7a1 1 0 0 1 2 0v4.585l1.293-1.292a.999.999 0 1 1 1.414 1.414l-3 3a.997.997 0 0 1-1.414 0l-3-3a.997.997 0 0 1 0-1.414.999.999 0 0 1 1.414 0L10 11.585V7Z"
                                        ></path>
                                    </svg>
                                </UiListBox.Button>

                                <UiTransition
                                    show={open}
                                    as={Fragment}
                                    enter="transition ease-out duration-200"
                                    enterFrom="opacity-0 translate-y-1"
                                    enterTo="opacity-100 translate-y-0"
                                    leave="transition ease-in duration-150"
                                    leaveFrom="opacity-100 translate-y-0"
                                    leaveTo="opacity-0 translate-y-1"
                                >
                                    <UiListBox.Options
                                        static
                                        className="button-primary absolute right-0 z-20 mt-3 max-h-64 w-full origin-top-left overflow-auto px-1.5 py-1.5 outline-none"
                                        style={{minWidth: '180px'}}
                                    >
                                        {sortOptions.map(option => (
                                            <UiListBox.Option
                                                className="group relative"
                                                key={option.value}
                                                value={option.value}
                                            >
                                                {({
                                                    active,
                                                    selected,
                                                    disabled
                                                }) => (
                                                    <button
                                                        disabled={disabled}
                                                        aria-disabled={disabled}
                                                        className="flex w-full flex-shrink-0 cursor-pointer select-none items-center border-0 px-3 py-2 text-left focus:outline-none"
                                                    >
                                                        <span className="block flex-1 truncate transition group-hover:text-muted">
                                                            {t(option.label)}
                                                        </span>
                                                        <UiCheckbox
                                                            size="lg"
                                                            onChange={() => {}}
                                                            className={cls(
                                                                'cursor-pointer rounded-full transition',
                                                                {
                                                                    'group-hover:bg-secondary-100':
                                                                        !selected
                                                                }
                                                            )}
                                                            checked={selected}
                                                        />
                                                    </button>
                                                )}
                                            </UiListBox.Option>
                                        ))}
                                    </UiListBox.Options>
                                </UiTransition>
                            </>
                        )}
                    </UiListBox>
                </div>
            )}
        </div>
    );
});

if (isDev) {
    Sort.displayName = 'Sort';
}

export default Sort;

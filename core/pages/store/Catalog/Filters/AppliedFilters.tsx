import {FC, memo} from 'react';
import {useTrans} from '@core/hooks';
import {isDev} from '@core/helpers';
import {XIcon} from '@core/icons/solid';
import useCatalog from '../useCatalog';

const AppliedFilters: FC = memo(() => {
    const t = useTrans();
    const {appliedFilters, removeFilter, clearFilters} = useCatalog();

    return (
        <div className="mt-4 hidden w-full xl:block">
            {appliedFilters.length > 0 && (
                <div className="flex w-full flex-wrap justify-between gap-2.5">
                    {appliedFilters.map(appliedFilter => (
                        <div
                            onClick={() => removeFilter(appliedFilter)}
                            key={`${appliedFilter.field}-${appliedFilter.selected.value}`}
                            className="button-primary flex cursor-pointer select-none items-center border-2 border-primary-600 px-3.5 py-1.5 text-xs font-medium transition hover:bg-secondary-100"
                        >
                            <div className="flex items-center gap-1.5">
                                {appliedFilter.isColorAttribute ? (
                                    <div className="flex items-center">
                                        <div
                                            className="relative mr-1 h-3.5 w-3.5 overflow-hidden rounded-lg"
                                            style={{
                                                backgroundColor:
                                                    appliedFilter.selected.color
                                            }}
                                        >
                                            {!appliedFilter.selected.color && (
                                                <svg
                                                    className="absolute inset-0 h-full w-full stroke-2 text-red-600"
                                                    viewBox="0 0 100 100"
                                                    preserveAspectRatio="none"
                                                    stroke="currentColor"
                                                >
                                                    <line
                                                        x1={0}
                                                        y1={100}
                                                        x2={100}
                                                        y2={0}
                                                        vectorEffect="non-scaling-stroke"
                                                    />
                                                </svg>
                                            )}
                                        </div>

                                        {appliedFilter.selected.label}
                                    </div>
                                ) : (
                                    appliedFilter.selected.label
                                )}
                                <div className="rounded-full bg-gray-200 p-1">
                                    <XIcon className="h-2.5 w-2.5 stroke-black stroke-[20px]" />
                                </div>
                            </div>
                        </div>
                    ))}

                    <button
                        onClick={clearFilters}
                        className="ml-auto flex items-center rounded-md border-2 border-transparent bg-primary-600 px-3.5 py-1.5 text-xs font-medium text-white transition hover:bg-primary-500"
                    >
                        {t('Clear All Filters')}
                    </button>
                </div>
            )}
        </div>
    );
});

if (isDev) {
    AppliedFilters.displayName = 'AppliedFilters';
}

export default AppliedFilters;

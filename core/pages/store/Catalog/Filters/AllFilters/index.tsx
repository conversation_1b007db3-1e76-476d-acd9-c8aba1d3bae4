import {UiButton, UiDialog, UiTransition} from '@core/components/ui';
import {cls} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {XIcon} from '@core/icons/solid';
import {Fragment, useState} from 'react';
import useCatalog from '../../useCatalog';
import Filters from './Filters';

const AllFilters = () => {
    const [showDrawer, setShowDrawer] = useState(false);

    const {filters, clearFilters, appliedFilters} = useCatalog();

    const t = useTrans();

    return (
        <>
            <button
                onClick={() => setShowDrawer(true)}
                className={cls(
                    'button-primary group inline-flex items-center px-3.5 py-1.5 text-sm font-medium transition hover:bg-secondary-100',
                    {'border-primary-600 ring-1 ring-primary-600': showDrawer}
                )}
            >
                <span>{t('All Filters')}</span>
                {appliedFilters?.length > 0 ? (
                    <span
                        className="ml-2.5 inline-flex items-center justify-center rounded-full bg-primary-600 text-[10px] font-bold text-white"
                        style={{
                            minWidth: '1.2rem',
                            minHeight: '1.2rem'
                        }}
                    >
                        {appliedFilters.length}
                    </span>
                ) : (
                    <svg className="ml-2.5 h-5 w-5" viewBox="0 0 24 24">
                        <path d="M6 5h2v1h12v2H8v1H6V8H4V6h2V5zm12 8h2v-2h-2v-1h-2v1H4v2h12v1h2v-1zm-5.94 5H20v-2h-7.94v-1h-2v1H4v2h6.06v1h2v-1z" />
                    </svg>
                )}
            </button>

            <UiTransition.Root show={showDrawer} as={Fragment}>
                <UiDialog
                    as="div"
                    className="relative z-modal"
                    onClose={() => setShowDrawer(false)}
                >
                    <UiTransition.Child
                        as={Fragment}
                        enter="ease-in-out duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="ease-in-out duration-300"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                    >
                        <div className="fixed inset-0 bg-gray-900 bg-opacity-20 transition-opacity" />
                    </UiTransition.Child>

                    <div className="fixed inset-0 overflow-hidden">
                        <div className="absolute inset-0 overflow-hidden">
                            <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full">
                                <UiTransition.Child
                                    as={Fragment}
                                    enter="transform transition ease-in-out duration-500"
                                    enterFrom="translate-x-full"
                                    enterTo="translate-x-0"
                                    leave="transform transition ease-in-out duration-500"
                                    leaveFrom="translate-x-0"
                                    leaveTo="translate-x-full"
                                >
                                    <UiDialog.Panel className="pointer-events-auto w-screen xl:max-w-md">
                                        <div className="flex h-full flex-col overflow-y-auto bg-white shadow-xl">
                                            <div className="flex items-center justify-between border-b p-6">
                                                <UiDialog.Title className="flex select-none items-center text-lg font-medium">
                                                    {t('Filter')}
                                                </UiDialog.Title>
                                                <div className="ml-3 flex h-8 items-center">
                                                    <button
                                                        type="button"
                                                        className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-muted outline-none transition hover:bg-gray-200 hover:text-muted"
                                                        onClick={() =>
                                                            setShowDrawer(false)
                                                        }
                                                    >
                                                        <XIcon
                                                            className="h-5 w-5"
                                                            aria-hidden="true"
                                                        />
                                                    </button>
                                                </div>
                                            </div>

                                            <div className="flex flex-col px-6 pb-24">
                                                {filters.map(filter => {
                                                    return (
                                                        <Filters
                                                            key={
                                                                filter.label +
                                                                filter.field
                                                            }
                                                            filter={filter}
                                                        />
                                                    );
                                                })}
                                            </div>

                                            <div className="fixed bottom-0 w-[calc(100%-16px)] bg-white p-6">
                                                <UiButton
                                                    variant="solid"
                                                    disabled={
                                                        appliedFilters.length ===
                                                        0
                                                    }
                                                    size="lg"
                                                    color="primary"
                                                    onClick={() => {
                                                        clearFilters();
                                                    }}
                                                >
                                                    {t('Clear All Filters')}
                                                </UiButton>
                                            </div>
                                        </div>
                                    </UiDialog.Panel>
                                </UiTransition.Child>
                            </div>
                        </div>
                    </div>
                </UiDialog>
            </UiTransition.Root>
        </>
    );
};

export default AllFilters;

import {UiDisclosure, UiInput, UiCheckbox} from '@core/components/ui';
import {toLower, trim, cls} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {ChevronDownIcon} from '@core/icons/solid';
import {Filter} from '@core/types';
import {useState, useMemo} from 'react';
import useCatalog from '../../useCatalog';

type FilterProps = {
    filter: Filter;
};

const Filters = ({filter}: FilterProps) => {
    const [search, setSearch] = useState('');

    const {appliedFilters, addFilter, removeFilter} = useCatalog();

    const t = useTrans();

    const filteredItems = useMemo(
        () =>
            filter.items.filter(
                item =>
                    toLower(item.label).indexOf(toLower(trim(search))) !== -1
            ),
        [filter.items, search]
    );

    return (
        <UiDisclosure
            as="div"
            key={filter.label + filter.field}
            className="border-b py-4"
        >
            {({open}) => (
                <>
                    <h3 className="group -my-3 flow-root">
                        <UiDisclosure.Button className="flex w-full items-center justify-between py-3.5 text-sm">
                            <span className="font-semibold group-hover:underline">
                                {t(filter.label)}
                            </span>

                            <ChevronDownIcon
                                className={cls(
                                    'h-3.5 w-3.5 stroke-black stroke-[20px] text-muted transition',
                                    {'-rotate-180': open}
                                )}
                            />
                        </UiDisclosure.Button>
                    </h3>

                    <UiDisclosure.Panel className="pt-4">
                        {filter.items.length > 10 && (
                            <div className="pb-3">
                                <UiInput
                                    className="bg-white shadow-sm transition"
                                    size="md"
                                    placeholder={t(`{artifact} search`, {
                                        artifact: t(filter.label)
                                    })}
                                    onChange={e => setSearch(e.target.value)}
                                />
                            </div>
                        )}

                        <div className="scroller max-h-56 space-y-1 overflow-auto p-1">
                            {filteredItems.map(item => (
                                <div
                                    key={item.value}
                                    className="group flex items-center"
                                >
                                    <UiCheckbox
                                        size="lg"
                                        className="rounded-full"
                                        checked={
                                            appliedFilters.findIndex(
                                                appliedFilter =>
                                                    appliedFilter.field ===
                                                        filter.field &&
                                                    appliedFilter.selected
                                                        .value === item.value
                                            ) !== -1
                                        }
                                        onChange={e =>
                                            // @ts-ignore
                                            e.target.checked
                                                ? addFilter({
                                                      type: filter.type,
                                                      label: filter.label,
                                                      field: filter.field,
                                                      isColorAttribute:
                                                          filter.isColorAttribute,
                                                      selected: item
                                                  })
                                                : removeFilter({
                                                      type: filter.type,
                                                      label: filter.label,
                                                      field: filter.field,
                                                      isColorAttribute:
                                                          filter.isColorAttribute,
                                                      selected: item
                                                  })
                                        }
                                    >
                                        {filter.isColorAttribute ? (
                                            <div className="flex items-center">
                                                <div
                                                    className="relative mr-1.5 h-4 w-4 overflow-hidden rounded-lg"
                                                    style={{
                                                        backgroundColor:
                                                            item.color
                                                    }}
                                                >
                                                    {!item.color && (
                                                        <svg
                                                            className="absolute inset-0 h-full w-full stroke-2 text-red-600"
                                                            viewBox="0 0 100 100"
                                                            preserveAspectRatio="none"
                                                            stroke="currentColor"
                                                        >
                                                            <line
                                                                x1={0}
                                                                y1={100}
                                                                x2={100}
                                                                y2={0}
                                                                vectorEffect="non-scaling-stroke"
                                                            />
                                                        </svg>
                                                    )}
                                                </div>

                                                <span className="select-none text-sm transition duration-100 ease-in-out group-hover:text-muted">
                                                    {item.label}
                                                </span>
                                            </div>
                                        ) : (
                                            <span className="select-none text-sm transition duration-100 ease-in-out group-hover:text-muted">
                                                {item.label}
                                            </span>
                                        )}
                                    </UiCheckbox>
                                </div>
                            ))}
                        </div>
                    </UiDisclosure.Panel>
                </>
            )}
        </UiDisclosure>
    );
};

export default Filters;

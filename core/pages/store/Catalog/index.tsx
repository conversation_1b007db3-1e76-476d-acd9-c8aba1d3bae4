import {memo} from 'react';
import {isDev} from '@core/helpers';
import {Page} from '@core/types';
import {CatalogProvider} from './context';

import Meta from './Meta';
import Products from './Products';
import Menu from './Menu';

type CatalogPageProps = {
    search?: string;
    pageNumber?: number;
};

const CatalogPage: Page<CatalogPageProps> = memo(({pageNumber, search}) => {
    return (
        <CatalogProvider search={search} pageNumber={pageNumber}>
            <Meta />

            <div className="flex gap-8">
                <Menu />
                <Products />
            </div>
        </CatalogProvider>
    );
});

if (isDev) {
    CatalogPage.displayName = 'CatalogPage';
}

export default CatalogPage;

import {FC, memo, useState} from 'react';
import {isDev} from '@core/helpers';
import {useTrans, useUI} from '@core/hooks';
import {UiButton, UiRadioGroup} from '@core/components/ui';
import {CheckCircleIcon} from '@core/icons/solid';
import {Sort} from '../context/types';

type MobileSortProps = {
    sortOptions: {value: string; label: string}[];
    sort: Sort;
    onSort: (value: string) => void;
};

const MobileSort: FC<MobileSortProps> = memo(({sortOptions, sort, onSort}) => {
    const t = useTrans();
    const {closeSideBar} = useUI();
    const [selectedSort, setSelectedSort] = useState<string>(
        typeof sort === 'object'
            ? `${sort.field}|${sort.direction}`
            : sortOptions[0].value
    );

    return (
        <div className="flex h-full w-full flex-col overflow-hidden">
            <div className="flex-1 overflow-y-auto">
                <UiRadioGroup
                    className="mt-4"
                    value={selectedSort}
                    onChange={(s: any) => setSelectedSort(s)}
                >
                    <div className="-mt-3 divide-y">
                        {sortOptions.map(option => (
                            <UiRadioGroup.Option
                                key={option.value}
                                value={option.value}
                                className="flex items-center px-4 py-3"
                            >
                                {({checked}) => (
                                    <>
                                        {checked ? (
                                            <CheckCircleIcon
                                                className="mr-3 h-6 w-6 text-primary-600"
                                                aria-hidden="true"
                                            />
                                        ) : (
                                            <div className="mr-3 h-6 w-6 rounded-full border border-gray-300"></div>
                                        )}

                                        <div className="flex-1">
                                            {t(option.label)}
                                        </div>
                                    </>
                                )}
                            </UiRadioGroup.Option>
                        ))}
                    </div>
                </UiRadioGroup>
            </div>

            <div className="border-t p-4">
                <UiButton
                    variant="solid"
                    color="primary"
                    className="w-full"
                    onClick={() => {
                        onSort(selectedSort);
                        closeSideBar();
                    }}
                >
                    {t('Apply Sort')}
                </UiButton>
            </div>
        </div>
    );
});

if (isDev) {
    MobileSort.displayName = 'MobileSort';
}

export default MobileSort;

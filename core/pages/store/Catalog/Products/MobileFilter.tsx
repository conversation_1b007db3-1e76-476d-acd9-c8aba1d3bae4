import {
    Dispatch,
    FC,
    memo,
    SetStateAction,
    useCallback,
    useEffect,
    useMemo,
    useState
} from 'react';
import {clone, isDev, toLower, trim} from '@core/helpers';
import {Filter as FilterType, FilterItem as FilterItemType} from '@core/types';
import {useTrans, useUI} from '@core/hooks';
import {UiButton, UiCheckbox, UiInput} from '@core/components/ui';
import {CheckCircleIcon, ChevronRightIcon} from '@core/icons/solid';
import {AppliedFilter} from '../context/types';

type FilterItem = FilterItemType & {
    isSelected: boolean;
};

type Filter = Omit<FilterType, 'items'> & {
    items: FilterItem[];
};

type SelectedFilterProps = {
    selectedFilter: Filter;
    setSelectedFilter: Dispatch<SetStateAction<Filter | undefined>>;
    onItemsSelected: (selectedItems: FilterItem[]) => void;
};

const SelectedFilter: FC<SelectedFilterProps> = memo(
    ({selectedFilter, setSelectedFilter, onItemsSelected}) => {
        const t = useTrans();
        const [search, setSearch] = useState('');
        const [selectedItems, setSelectedItems] = useState<FilterItem[]>(() =>
            selectedFilter.items.filter(item => !!item.isSelected)
        );

        const filteredItems = useMemo(
            () =>
                (selectedFilter.items as FilterItem[])
                    .filter(
                        item =>
                            toLower(item.label).indexOf(
                                toLower(trim(search))
                            ) !== -1
                    )
                    .map(item => ({
                        ...item,
                        isSelected:
                            selectedItems.findIndex(
                                selectedItem =>
                                    selectedItem.value === item.value
                            ) !== -1
                    })),
            [selectedFilter.items, search, selectedItems]
        );

        const onItemSelectionChange = useCallback((item: FilterItem) => {
            if (item.isSelected) {
                setSelectedItems(currentSelectedItems =>
                    currentSelectedItems.filter(
                        currentSelectedItem =>
                            currentSelectedItem.value !== item.value
                    )
                );
            } else {
                setSelectedItems(currentSelectedItems => [
                    ...currentSelectedItems,
                    item
                ]);
            }
        }, []);

        return (
            <div className="flex h-full w-full flex-col overflow-hidden">
                <div className="border-b p-4">
                    <UiInput
                        className="border-gray-100 bg-gray-100 shadow-sm transition focus:bg-white"
                        placeholder={t(`{artifact} search`, {
                            artifact: t(selectedFilter.label)
                        })}
                        onChange={e => setSearch(e.target.value)}
                    />
                </div>

                <div className="flex-1 divide-y overflow-y-auto">
                    {filteredItems.map(item => (
                        <button
                            className="flex w-full items-center px-4 py-3 text-left"
                            key={item.value}
                            onClick={() => onItemSelectionChange(item)}
                        >
                            {item.isSelected ? (
                                <CheckCircleIcon
                                    className="mr-3 h-6 w-6 text-primary-600"
                                    aria-hidden="true"
                                />
                            ) : (
                                <div className="mr-3 h-6 w-6 rounded-full border border-gray-300"></div>
                            )}

                            <div className="flex-1">{item.label}</div>

                            {selectedFilter.isColorAttribute && (
                                <div
                                    className="relative ml-4 h-5 w-5 overflow-hidden rounded-full border"
                                    style={
                                        !!item.color
                                            ? {
                                                  backgroundColor: item.color
                                              }
                                            : {}
                                    }
                                >
                                    {!item.color && (
                                        <svg
                                            className="absolute inset-0 h-full w-full stroke-2 text-red-600"
                                            viewBox="0 0 100 100"
                                            preserveAspectRatio="none"
                                            stroke="currentColor"
                                        >
                                            <line
                                                x1={0}
                                                y1={100}
                                                x2={100}
                                                y2={0}
                                                vectorEffect="non-scaling-stroke"
                                            />
                                        </svg>
                                    )}
                                </div>
                            )}
                        </button>
                    ))}
                </div>

                <div className="space-y-2 border-t p-4">
                    <UiButton
                        variant="solid"
                        color="primary"
                        className="w-full"
                        disabled={selectedItems.length < 1}
                        onClick={() => onItemsSelected(selectedItems)}
                    >
                        {t('Apply')}
                    </UiButton>

                    <UiButton
                        variant="light"
                        color="primary"
                        className="w-full"
                        onClick={() => setSelectedFilter(undefined)}
                    >
                        {t('Cancel')}
                    </UiButton>
                </div>
            </div>
        );
    }
);

if (isDev) {
    SelectedFilter.displayName = 'SelectedFilter';
}

type FiltersProps = {
    filters: Filter[];
    setSelectedFilter: Dispatch<SetStateAction<Filter | undefined>>;
    onApplyFilters: () => void;
    onClearAllFilters: () => void;
};

const Filters: FC<FiltersProps> = memo(
    ({filters, setSelectedFilter, onApplyFilters, onClearAllFilters}) => {
        const t = useTrans();

        return (
            <div className="flex h-full w-full flex-col overflow-hidden">
                <div className="flex-1 divide-y overflow-y-auto">
                    {filters.map(filter => {
                        const selectedItemCount = filter.items.filter(
                            item => !!item.isSelected
                        ).length;

                        return (
                            <button
                                className="flex w-full items-center justify-between px-4 py-3"
                                key={filter.field}
                                onClick={() => setSelectedFilter(filter)}
                            >
                                <div className="mr-4 flex flex-1 items-center">
                                    {t(filter.label)}
                                </div>

                                <div className="flex items-center space-x-3">
                                    {selectedItemCount > 0 && (
                                        <div className="flex h-5 w-5 items-center justify-center rounded-full bg-red-600 text-sm text-white">
                                            {selectedItemCount}
                                        </div>
                                    )}

                                    <ChevronRightIcon className="h-4 w-4 text-muted" />
                                </div>
                            </button>
                        );
                    })}
                </div>

                <div className="space-y-2 border-t p-4">
                    <UiButton
                        variant="solid"
                        color="primary"
                        className="w-full"
                        onClick={onApplyFilters}
                    >
                        {t('Apply Filters')}
                    </UiButton>

                    <UiButton
                        variant="light"
                        color="primary"
                        className="w-full"
                        onClick={onClearAllFilters}
                    >
                        {t('Clear All Filters')}
                    </UiButton>
                </div>
            </div>
        );
    }
);

if (isDev) {
    Filters.displayName = 'Filters';
}

type MobileFilterProps = {
    filters: Filter[];
    appliedFilters: AppliedFilter[];
    updateFilters: (appliedFilters: AppliedFilter[]) => void;
    clearFilters: () => void;
};

const MobileFilter: FC<MobileFilterProps> = memo(
    ({
        filters: initialFilters,
        updateFilters,
        clearFilters,
        appliedFilters
    }) => {
        const t = useTrans();
        const {closeSideBar} = useUI();
        const {setSideBarTitle} = useUI();
        const [filters, setFilters] = useState<Filter[]>(
            () =>
                clone(initialFilters).map(initialFilter => {
                    initialFilter.items = initialFilter.items.map(item => {
                        item.isSelected =
                            appliedFilters.findIndex(
                                appliedFilter =>
                                    appliedFilter.field ===
                                        initialFilter.field &&
                                    appliedFilter.selected.value === item.value
                            ) !== -1;

                        return item;
                    });

                    return initialFilter;
                }) as Filter[]
        );
        const [selectedFilter, setSelectedFilter] = useState<Filter>();

        const onItemsSelected = useCallback(
            (selectedItems: FilterItem[]) => {
                if (typeof selectedFilter === 'undefined') return;

                const currentFilters = clone(filters).map(currentFilter => {
                    if (currentFilter.field === selectedFilter.field) {
                        currentFilter.items = currentFilter.items.map(item => {
                            item.isSelected =
                                selectedItems.findIndex(
                                    selectedItem =>
                                        selectedItem.value === item.value
                                ) !== -1;

                            return item;
                        });
                    }

                    return currentFilter;
                });

                setFilters(currentFilters);
                setSelectedFilter(undefined);
            },
            [filters, selectedFilter]
        );

        const onApplyFilters = useCallback(() => {
            const appliedFilters: AppliedFilter[] = [];

            for (const filter of filters) {
                for (const item of filter.items) {
                    if (item.isSelected) {
                        appliedFilters.push({
                            type: filter.type,
                            field: filter.field,
                            label: filter.label,
                            isColorAttribute: filter.isColorAttribute,
                            selected: {
                                label: item.label,
                                value: item.value,
                                color: item.color
                            }
                        });
                    }
                }
            }

            updateFilters(appliedFilters);
            closeSideBar();
        }, [filters, updateFilters, closeSideBar]);

        const onClearAllFilters = useCallback(() => {
            clearFilters();
            closeSideBar();
        }, [clearFilters, closeSideBar]);

        useEffect(() => {
            if (typeof selectedFilter !== 'undefined') {
                setSideBarTitle(t(selectedFilter.label));
            } else {
                setSideBarTitle(t('Filter'));
            }
        }, [selectedFilter, setSideBarTitle, t]);

        if (typeof selectedFilter !== 'undefined') {
            return (
                <SelectedFilter
                    selectedFilter={selectedFilter}
                    setSelectedFilter={setSelectedFilter}
                    onItemsSelected={onItemsSelected}
                />
            );
        }

        return (
            <Filters
                filters={filters}
                setSelectedFilter={setSelectedFilter}
                onApplyFilters={onApplyFilters}
                onClearAllFilters={onClearAllFilters}
            />
        );
    }
);

if (isDev) {
    MobileFilter.displayName = 'MobileFilter';
}

export default MobileFilter;

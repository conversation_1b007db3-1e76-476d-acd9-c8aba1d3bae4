import {FC, memo, useCallback, useEffect} from 'react';
import {isDev} from '@core/helpers';
import {useIntersection, useTrans, useUI} from '@core/hooks';
import {UiButton, UiCheckbox} from '@core/components/ui';
import {FilterIcon, SortIcon, SearchIcon} from '@core/icons/regular';
import ProductCard from '@components/common/ProductCard';
import useCatalog from '../useCatalog';
import MobileSort from './MobileSort';
import MobileFilter from './MobileFilter';
import Filters from '../Filters';
import Sort from '../Filters/Sort';

const Products: FC = memo(() => {
    const t = useTrans();
    const {openSideBar} = useUI();
    const [ref, observer] = useIntersection({
        threshold: 0
    });

    const {
        appliedFilters,
        products,
        filters,
        initialSkip,
        hasNextPage,
        loadMore,
        loadPrevious,
        isLoading,
        isInitial,
        sortOptions,
        sort,
        updateSort,
        updateFilters,
        clearFilters,
        seo,
        inStockOnly,
        handleInStockChange
    } = useCatalog();

    const onOpenMobileSort = useCallback(() => {
        openSideBar(
            t('Sort'),
            <MobileSort
                sortOptions={sortOptions}
                sort={sort}
                onSort={selectedSort => {
                    const [field, direction] = selectedSort.split('|');

                    updateSort({
                        field,
                        direction: direction === 'desc' ? 'desc' : 'asc'
                    });
                }}
            />
        );
    }, [openSideBar, sort, sortOptions, t, updateSort]);
    const onOpenMobileFilter = useCallback(() => {
        openSideBar(
            t('Filter'),
            <MobileFilter
                filters={filters as any}
                appliedFilters={appliedFilters}
                updateFilters={updateFilters}
                clearFilters={clearFilters}
            />
        );
    }, [
        appliedFilters,
        clearFilters,
        filters,
        openSideBar,
        t,
        updateFilters,
        inStockOnly,
        handleInStockChange
    ]);

    useEffect(() => {
        if (observer?.isIntersecting) {
            loadMore();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [observer]);

    return (
        <section className="card-container flex-1 bg-white p-3">
            <div className="mb-4 flex items-center space-x-2 xl:hidden">
                <UiButton
                    className="button-primary flex-1"
                    leftIcon={<SortIcon className="mr-2.5 h-4 w-4" />}
                    onClick={onOpenMobileSort}
                >
                    {t('Sort')}
                </UiButton>
                <UiButton
                    className="button-primary flex-1"
                    leftIcon={<FilterIcon className="mr-2.5 h-4 w-4" />}
                    onClick={onOpenMobileFilter}
                >
                    {t('Filter')}

                    {appliedFilters.length > 0 && (
                        <div className="ml-3 flex h-5 w-5 items-center justify-center rounded-full bg-red-600 text-[10px] text-white">
                            {appliedFilters.length}
                        </div>
                    )}
                </UiButton>
                <UiButton className="button-primary flex-1 ">
                    <label className="flex items-center gap-1 text-base">
                        <UiCheckbox
                            name="inStockOnly"
                            checked={inStockOnly}
                            onChange={handleInStockChange}
                        />
                        {t('In Stock')}
                    </label>
                </UiButton>
            </div>
            {products.length < 1 && (
                <div className="hidden justify-end xl:flex">
                    <div className="flex items-center justify-end gap-4">
                        <label className="flex w-24 items-center gap-1 text-sm">
                            <UiCheckbox
                                name="inStockOnly"
                                checked={inStockOnly}
                                onChange={handleInStockChange}
                            />
                            {t('In Stock')}
                        </label>
                        <Sort />
                    </div>
                </div>
            )}

            {products.length > 0 && <Filters />}

            {initialSkip > 0 && (
                <>
                    <UiButton
                        variant="outline"
                        color="primary"
                        size="lg"
                        className="mb-4 w-full"
                        onClick={loadPrevious}
                    >
                        {t('SHOW PREVIOUS PRODUCTS')}
                    </UiButton>
                    <a href={seo.prevUrl} className="hidden">
                        {t('SHOW PREVIOUS PRODUCTS')}
                    </a>
                </>
            )}

            <div className="relative grid grid-cols-2 gap-2 xl:grid-cols-4 xl:gap-4">
                {products.map(product => (
                    <ProductCard
                        key={product.productId}
                        product={product}
                        // @ts-ignore
                        isFake={product.isFake}
                    />
                ))}

                {products.length > 0 && !isLoading && hasNextPage && (
                    <>
                        <div
                            ref={ref}
                            className="absolute bottom-[100px] left-0 h-0 w-full bg-amber-400"
                        ></div>
                        <a href={seo.nextUrl} className="hidden">
                            {t('SHOW NEXT PRODUCTS')}
                        </a>
                    </>
                )}
            </div>

            {products.length < 1 && !isLoading && !isInitial && (
                <div className="flex flex-1 flex-col items-center justify-center py-24 md:px-12">
                    <div className="flex h-24 w-24 items-center justify-center rounded-md border border-dashed border-gray-500 text-muted">
                        <SearchIcon className="h-8 w-8" />
                    </div>

                    <h2 className="pt-8 text-center text-2xl font-semibold">
                        {t('No products found!')}
                    </h2>

                    <p className="px-10 pt-2 text-center text-muted">
                        {t(
                            'Please change your search criteria and try again. If still not finding anything relevant, please visit the Home page and try out some of our bestsellers!'
                        )}
                    </p>
                </div>
            )}
        </section>
    );
});

if (isDev) {
    Products.displayName = 'Products';
}

export default Products;

import {Filter, ProductListItem} from '@core/types';

export type AppliedFilter = Pick<
    Filter,
    'type' | 'field' | 'label' | 'isColorAttribute'
> & {
    selected: {value: string; label: string; color?: string};
};

export type Sort = {
    field: string;
    direction: 'asc' | 'desc';
};

export type CatalogProviderProps = {
    search?: string;
    pageNumber?: number;
};

export type CatalogContextType = {
    filters: Filter[];
    search?: string;
    appliedFilters: AppliedFilter[];
    products: ProductListItem[];
    productsPerPage: number;
    initialSkip: number;
    sort: Sort;
    pageNumber?: number;
    hasNextPage: boolean;
    totalProductCountText: string;
    isLoading: boolean;
    isLoadingMore: boolean;
    isInitial: boolean;
    seo: Record<string, any>;
    sortOptions: {value: string; label: string}[];
    inStockOnly: boolean;
    handleInStockChange: () => void;

    addFilter: (appliedFilter: AppliedFilter) => void;
    removeFilter: (appliedFilter: AppliedFilter) => void;
    updateFilters: (appliedFilters: AppliedFilter[]) => void;
    clearFilters: () => void;
    updateSort: (sort: Sort) => void;
    loadMore: () => void;
    loadPrevious: () => void;
};

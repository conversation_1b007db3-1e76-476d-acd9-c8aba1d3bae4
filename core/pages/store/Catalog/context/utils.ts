import {trim, parseURLSearchParams} from '@core/helpers';
import {NextRouter} from 'next/router';

export function parsePath(router: NextRouter) {
    let path = trim(router.asPath, '/');

    let queryString = undefined;
    if (path.indexOf('?') !== -1) {
        const parts = path.split('?');

        path = parts[0];
        queryString = parts[1];
    }

    return {path: `/${path}`, queryString};
}

export function getQuery(router: NextRouter) {
    const parsed = parsePath(router);
    const query: Record<string, any> = {};

    const searchParams = parseURLSearchParams(parsed.queryString ?? '');
    for (const field of Object.keys(searchParams)) {
        const value = searchParams[field];

        if (field !== 'slug') {
            query[field] = value;
        }
    }

    return query;
}

import {FC, memo, useCallback, useEffect, useState} from 'react';
import storeConfig from '~/store.config';
import {Cart, Contact, DeliveryOption, PaymentMethod} from '@core/types';
import {isDev} from '@core/helpers';
import {useIOSDevice, useMobile, useTrans, useUI} from '@core/hooks';
import {UiStickyBox} from '@core/components/ui';
import {ChevronUpIcon} from '@core/icons/outline';
import Price from '@components/common/Price';
import Seo from '@components/common/Seo';
import Content from './Content';
import {CheckoutProvider} from './context';
import Header from './Header';
import MobileSteps from './MobileSteps';
import SideBar from './SideBar';
import useCheckout from './useCheckout';

const CheckoutContent: FC = memo(() => {
    const t = useTrans();
    const {openSideBar} = useUI();
    const {isMobile} = useMobile();
    const {cart, getDeliveryDate, isLoading} = useCheckout();
    const {isIOSDevice} = useIOSDevice();
    const [step, setStep] = useState('');

    const [minHeight, setMinHeight] = useState(
        () => `calc(100vh - ${storeConfig.theme.accountHeaderHeight} - 1px)`
    );
    useEffect(() => {
        if (isMobile) {
            setMinHeight('auto');
        } else {
            setMinHeight(
                `calc(100vh - ${storeConfig.theme.accountHeaderHeight} - 1px)`
            );
        }
    }, [isMobile]);

    const onOpenMobileOrderSummary = useCallback(() => {
        openSideBar(
            t('Order Summary'),
            <SideBar cart={cart} getDeliveryDate={getDeliveryDate} />
        );
    }, [cart, getDeliveryDate, openSideBar, t]);

    useEffect(() => {
        window.scrollTo({top: 0, behavior: 'smooth'});
    }, [step]);

    return (
        <>
            <Seo title={t('Checkout')} noindex={true} />

            <div className="h-fit w-full ">
                <div
                    className="fixed left-0 top-0 hidden h-screen w-1/2 bg-white xl:block"
                    aria-hidden="true"
                />
                <div
                    className="fixed right-0 top-0 hidden h-screen w-1/2 bg-gray-100 xl:block"
                    aria-hidden="true"
                />

                <main
                    className="relative flex h-fit flex-col gap-4 xl:flex-row"
                    style={{minHeight}}
                >
                    <MobileSteps setStep={setStep} />
                    <div className="flex w-full flex-1 xl:mx-auto xl:max-w-7xl">
                        <div className="flex w-full flex-1 flex-col space-x-20 xl:flex-row">
                            <div className="w-full rounded-lg bg-white pb-4 pt-6 xl:w-1/2 xl:pb-12 xl:pl-2  xl:pt-12 ">
                                <Header />
                                <Content />
                            </div>

                            <div className="hidden flex-col justify-start bg-gray-100 xl:flex xl:w-1/2">
                                <UiStickyBox className="-ml-12 rounded-lg pb-12 pl-12 pt-12">
                                    <SideBar
                                        cart={cart}
                                        getDeliveryDate={getDeliveryDate}
                                    />
                                </UiStickyBox>
                            </div>
                        </div>
                    </div>
                </main>
            </div>

            <button
                className="fixed bottom-0 left-0 z-[49] flex w-full items-center justify-between border-t border-gray-200 bg-white px-6 text-left xl:hidden"
                style={{
                    height: `${
                        isIOSDevice
                            ? storeConfig.theme.iosTabBarHeight
                            : storeConfig.theme.mobileTabBarHeight
                    }`
                }}
                disabled={isLoading}
                onClick={() => {
                    if (!isLoading) {
                        onOpenMobileOrderSummary();
                    }
                }}
            >
                <div className="text-sm">
                    <div className="mb-0.5 font-medium">{t('Total')}</div>
                    <div className="text-xs text-muted">
                        {t('Tax Included')}
                    </div>
                </div>

                <div className="flex items-center space-x-4">
                    <Price
                        className="text-xl font-semibold"
                        price={cart.grandTotal}
                    />

                    {!isLoading && <ChevronUpIcon className="h-4 w-4" />}
                </div>
            </button>
        </>
    );
});

if (isDev) {
    CheckoutContent.displayName = 'CheckoutContent';
}

type CheckoutProps = {
    countries: Record<string, any>[];
    initialStates?: string[];
    initialCities: string[];
    deliveryOptions: DeliveryOption[];
    paymentMethods: PaymentMethod[];
    contacts?: Contact[];
    cart: Cart;
    salesContractText: string;
    preliminaryInformationForm: string;
};

const Checkout: FC<CheckoutProps> = memo(props => {
    const {
        cart,
        countries,
        initialCities,
        initialStates,
        deliveryOptions,
        paymentMethods,
        contacts
    } = props;

    return (
        <CheckoutProvider
            cart={cart}
            countries={countries}
            initialStates={initialStates}
            initialCities={initialCities}
            deliveryOptions={deliveryOptions}
            paymentMethods={paymentMethods}
            contacts={contacts}
        >
            <CheckoutContent />
        </CheckoutProvider>
    );
});

if (isDev) {
    Checkout.displayName = 'Checkout';
}

export default Checkout;

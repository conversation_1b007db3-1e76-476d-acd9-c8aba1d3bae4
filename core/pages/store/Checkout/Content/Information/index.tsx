import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useCustomer} from '@core/hooks';

import CustomerInformation from './CustomerInformation';
import GuestInformation from './GuestInformation';

const Information: FC = memo(() => {
    const customer = useCustomer();

    if (typeof customer !== 'undefined') {
        return <CustomerInformation />;
    }

    return <GuestInformation />;
});

if (isDev) {
    Information.displayName = 'Information';
}

export default Information;

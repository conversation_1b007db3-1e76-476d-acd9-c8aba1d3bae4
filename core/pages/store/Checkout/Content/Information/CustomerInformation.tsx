import {FC, memo, useCallback, useEffect, useRef} from 'react';
import {useRouter} from 'next/router';
import {cls, isDev, jsonRequest} from '@core/helpers';
import {useCustomer, useTrans, useUI} from '@core/hooks';
import {Contact} from '@core/types';
import {
    UiAlert,
    UiButton,
    UiDivider,
    UiLink,
    UiRadioGroup
} from '@core/components/ui';
import {PencilIcon, PlusIcon} from '@core/icons/regular';
import {ArrowRightIcon, CheckCircleIcon} from '@core/icons/solid';
import Address from '@core/pages/account/MyAddresses/Address';
import useCheckout from '../../useCheckout';

const CustomerInformation: FC = memo(() => {
    const router = useRouter();
    const t = useTrans();
    const {openSideBar} = useUI();
    const customer = useCustomer();
    const {
        cart,
        countries,
        initialStates,
        initialCities,
        contacts,
        errorMessage,
        setErrorMessage,
        isLoading,
        setIsLoading,
        changeAddress,
        saveCustomerInformation,
        updateContacts
    } = useCheckout();
    const inProgress = useRef(false);

    useEffect(() => {
        (async () => {
            if (!contacts || contacts.length < 1) {
                const newContacts = await jsonRequest({
                    url: '/api/customers/contacts',
                    method: 'POST',
                    data: {}
                });

                if (Array.isArray(newContacts) && newContacts.length > 0) {
                    updateContacts(newContacts);
                }

                if (Array.isArray(newContacts) && newContacts.length > 0) {
                    if (!cart.deliveryAddressId) {
                        const deliveryContact = newContacts.find(
                            contact => contact.type === 'delivery-address'
                        );

                        if (!!deliveryContact) {
                            try {
                                await changeAddress(
                                    'delivery-address',
                                    deliveryContact.id
                                );
                            } catch (error: any) {
                                setErrorMessage(error.message);

                                const container =
                                    document.querySelector('.content-wrapper');
                                if (container !== null) {
                                    container?.scrollTo({top: 0});
                                }
                            }
                        }
                    }

                    if (!cart.billingAddressId) {
                        const billingContact = newContacts.find(
                            contact => contact.type === 'billing-address'
                        );

                        if (!!billingContact) {
                            try {
                                await changeAddress(
                                    'billing-address',
                                    billingContact.id
                                );
                            } catch (error: any) {
                                setErrorMessage(error.message);

                                const container =
                                    document.querySelector('.content-wrapper');
                                if (container !== null) {
                                    container?.scrollTo({top: 0});
                                }
                            }
                        }
                    }
                }
            }
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const onAddressSave = useCallback(async () => {
        const newContacts: Contact[] = await jsonRequest({
            url: '/api/customers/contacts',
            method: 'POST',
            data: {}
        });

        updateContacts(newContacts);

        if (Array.isArray(newContacts) && newContacts.length > 0) {
            if (!cart.deliveryAddressId) {
                const deliveryContact = newContacts.find(
                    contact => contact.type === 'delivery-address'
                );

                if (!!deliveryContact) {
                    try {
                        await changeAddress(
                            'delivery-address',
                            deliveryContact.id
                        );
                    } catch (error: any) {
                        setErrorMessage(error.message);

                        const container =
                            document.querySelector('.content-wrapper');
                        if (container !== null) {
                            container?.scrollTo({top: 0});
                        }
                    }
                }
            }

            if (!cart.billingAddressId) {
                const billingContact = newContacts.find(
                    contact => contact.type === 'billing-address'
                );

                if (!!billingContact) {
                    try {
                        await changeAddress(
                            'billing-address',
                            billingContact.id
                        );
                    } catch (error: any) {
                        setErrorMessage(error.message);

                        const container =
                            document.querySelector('.content-wrapper');
                        if (container !== null) {
                            container?.scrollTo({top: 0});
                        }
                    }
                }
            }
        }
    }, [
        updateContacts,
        cart.deliveryAddressId,
        cart.billingAddressId,
        changeAddress,
        setErrorMessage
    ]);
    const onAddressDetail = useCallback(
        (type: 'delivery-address' | 'billing-address', contact?: Contact) => {
            if (typeof contact === 'undefined') {
                if (type === 'delivery-address') {
                    const firstContact = (contacts ?? []).find(
                        c => c.type === 'delivery-address'
                    );

                    if (typeof firstContact !== 'undefined') {
                        contact = {
                            relevantContact: {
                                name: firstContact.relevantContact?.name,
                                email: firstContact.relevantContact?.email,
                                phoneNumber:
                                    firstContact.relevantContact?.phoneNumber,
                                phoneCode:
                                    firstContact.relevantContact?.phoneCode,
                                phoneCountryCode:
                                    firstContact.relevantContact
                                        ?.phoneCountryCode
                            }
                        } as any;
                    } else {
                        contact = {
                            relevantContact: {
                                name: customer?.name,
                                email: customer?.email,
                                phoneNumber: customer?.phoneNumber,
                                phoneCode: customer?.phoneCode,
                                phoneCountryCode: customer?.phoneCountryCode
                            }
                        } as any;
                    }
                } else {
                    const firstContact = (contacts ?? []).find(
                        c => c.type === 'billing-address'
                    );

                    if (typeof firstContact !== 'undefined') {
                        contact = {
                            relevantContact: {
                                name: firstContact.relevantContact?.name,
                                email: firstContact.relevantContact?.email,
                                phoneNumber:
                                    firstContact.relevantContact?.phoneNumber,
                                phoneCode:
                                    firstContact.relevantContact?.phoneCode,
                                phoneCountryCode:
                                    firstContact.relevantContact
                                        ?.phoneCountryCode
                            },
                            invoiceType: firstContact.invoiceType,
                            companyName: firstContact.companyName,
                            taxIdentificationNumber:
                                firstContact.taxIdentificationNumber,
                            taxOffice: firstContact.taxOffice,
                            identityNumber: firstContact.identityNumber
                        } as any;
                    } else {
                        contact = {
                            relevantContact: {
                                name: customer?.name,
                                email: customer?.email,
                                phoneNumber: customer?.phoneNumber,
                                phoneCode: customer?.phoneCode,
                                phoneCountryCode: customer?.phoneCountryCode
                            }
                        } as any;
                    }
                }
            }

            openSideBar(
                type === 'delivery-address'
                    ? t('Delivery Address')
                    : t('Billing Address'),
                <Address
                    contact={contact}
                    countries={countries}
                    initialStates={initialStates}
                    initialCities={initialCities}
                    type={type}
                    onSave={onAddressSave}
                />
            );
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [countries, initialStates, initialCities, contacts, onAddressSave]
    );

    const onChange = useCallback(
        async (
            type: 'delivery-address' | 'billing-address',
            addressId: string
        ) => {
            if (inProgress.current) return;

            inProgress.current = true;
            setIsLoading(true);

            try {
                await changeAddress(type, addressId);
            } catch (error: any) {
                setErrorMessage(error.message);

                const container = document.querySelector('.content-wrapper');
                if (container !== null) {
                    container?.scrollTo({top: 0});
                }
            }

            setIsLoading(false);
            inProgress.current = false;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    const onSubmit = useCallback(
        async () => {
            if (inProgress.current) return;

            inProgress.current = true;
            setIsLoading(true);

            try {
                await saveCustomerInformation(cart, contacts ?? []);
            } catch (error: any) {
                if (error.code === 'no_delivery_address_selected') {
                    setErrorMessage(t('No delivery address selected!'));
                } else if (error.code === 'no_billing_address_selected') {
                    setErrorMessage(t('No billing address selected!'));
                } else {
                    setErrorMessage(error.message);
                }

                const container = document.querySelector('.content-wrapper');

                container?.scrollTo({top: 0, behavior: 'smooth'});
            }

            setIsLoading(false);
            inProgress.current = false;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [cart, contacts]
    );

    return (
        <div className="px-4">
            {!!errorMessage && (
                <UiAlert className="mb-4 xl:mb-8" color="danger">
                    {t(errorMessage)}
                </UiAlert>
            )}

            <UiRadioGroup
                value={cart.deliveryAddressId}
                onChange={() => null}
                disabled={isLoading}
            >
                <UiRadioGroup.Label className="mb-4 flex items-center text-lg font-medium xl:mb-6">
                    <div className="mr-3 flex h-7 w-7 items-center justify-center rounded-full bg-gray-900 text-sm font-medium text-white">
                        1
                    </div>
                    {t('Delivery Address')}
                </UiRadioGroup.Label>

                <div className="grid grid-cols-1 gap-2 xl:grid-cols-2 xl:gap-4">
                    {contacts
                        ?.filter(contact => contact.type === 'delivery-address')
                        .map(contact => (
                            <UiRadioGroup.Option
                                key={contact.id}
                                value={contact.id}
                                className="shadow-small relative flex cursor-pointer items-center rounded-md border bg-white p-4 outline outline-1 outline-transparent hover:border-primary-600 hover:outline-primary-600"
                            >
                                {({checked}) => (
                                    <div
                                        className="flex w-full items-center"
                                        onClick={() =>
                                            onChange(
                                                'delivery-address',
                                                contact.id
                                            )
                                        }
                                    >
                                        {checked ? (
                                            <CheckCircleIcon
                                                className="mr-4 h-7 w-7 text-primary-600"
                                                aria-hidden="true"
                                            />
                                        ) : (
                                            <div className="mr-4 h-7 w-7 rounded-full border"></div>
                                        )}

                                        <div className="flex flex-1">
                                            <div className="flex flex-1 flex-col">
                                                <UiRadioGroup.Label
                                                    as="span"
                                                    className="flex items-center justify-between text-sm font-medium"
                                                >
                                                    {contact.name}

                                                    <div
                                                        className="group/inner ml-4 flex cursor-pointer items-center justify-center rounded-full p-1.5 transition hover:bg-warning-600"
                                                        onClick={e => {
                                                            e.preventDefault();
                                                            e.stopPropagation();

                                                            onAddressDetail(
                                                                'delivery-address',
                                                                contact
                                                            );

                                                            return false;
                                                        }}
                                                    >
                                                        <PencilIcon className="h-4 w-4 transition group-hover/inner:text-white" />
                                                    </div>
                                                </UiRadioGroup.Label>

                                                <UiRadioGroup.Description
                                                    as="span"
                                                    className="mt-2 flex items-center text-sm text-muted"
                                                >
                                                    <address className="not-italic">
                                                        <div>
                                                            {
                                                                contact.address
                                                                    .street
                                                            }
                                                        </div>
                                                        {!!contact.address
                                                            .street2 && (
                                                            <div>
                                                                {
                                                                    contact
                                                                        .address
                                                                        .street2
                                                                }
                                                            </div>
                                                        )}
                                                        {contact.address
                                                            .subDistrict && (
                                                            <div>
                                                                {
                                                                    contact
                                                                        .address
                                                                        .subDistrict
                                                                }
                                                            </div>
                                                        )}
                                                        <div className="flex items-center space-x-2">
                                                            {contact.address
                                                                .district && (
                                                                <span>
                                                                    {
                                                                        contact
                                                                            .address
                                                                            .district
                                                                    }
                                                                </span>
                                                            )}
                                                            <span>
                                                                {
                                                                    contact
                                                                        .address
                                                                        .city
                                                                }
                                                            </span>
                                                            {contact.address
                                                                .state && (
                                                                <span>
                                                                    {
                                                                        contact
                                                                            .address
                                                                            .state
                                                                    }
                                                                </span>
                                                            )}
                                                        </div>
                                                        <div className="flex items-center space-x-2">
                                                            <span>
                                                                {
                                                                    contact
                                                                        .address
                                                                        .postalCode
                                                                }
                                                            </span>
                                                            <span>
                                                                {
                                                                    contact
                                                                        .address
                                                                        .countryName
                                                                }
                                                            </span>
                                                        </div>
                                                    </address>
                                                </UiRadioGroup.Description>
                                            </div>
                                        </div>

                                        <div
                                            className={cls(
                                                checked
                                                    ? 'border-primary-600'
                                                    : 'border-transparent',
                                                'pointer-events-none absolute -inset-px rounded-md border-2'
                                            )}
                                            aria-hidden="true"
                                        />
                                    </div>
                                )}
                            </UiRadioGroup.Option>
                        ))}

                    <div
                        className="shadow-small flex cursor-pointer flex-col items-center justify-center rounded-md border bg-white px-4 py-8 text-sm outline outline-1 outline-transparent transition hover:border-green-600 hover:outline-primary-600"
                        onClick={() => onAddressDetail('delivery-address')}
                    >
                        <PlusIcon className="mb-3 h-7 w-7" />
                        <div className="mb-0.5 font-medium">
                            {t('New Address')}
                        </div>
                        <div>{t('Add a new delivery address.')}</div>
                    </div>
                </div>
            </UiRadioGroup>

            <UiDivider
                orientation="horizontal"
                className="my-6 block xl:hidden"
            />

            <UiRadioGroup
                value={cart.billingAddressId}
                onChange={() => null}
                disabled={isLoading}
            >
                <UiRadioGroup.Label className="mb-4 flex items-center text-lg font-medium xl:mb-6 xl:mt-12">
                    <div className="mr-3 flex h-7 w-7 items-center justify-center rounded-full bg-gray-900 text-sm font-medium text-white">
                        2
                    </div>
                    {t('Invoice Address')}
                </UiRadioGroup.Label>

                <div className="grid grid-cols-1 gap-2 xl:grid-cols-2 xl:gap-4">
                    {contacts
                        ?.filter(contact => contact.type === 'billing-address')
                        .map(contact => (
                            <UiRadioGroup.Option
                                key={contact.id}
                                value={contact.id}
                                className="shadow-small relative flex cursor-pointer items-center rounded-md border bg-white p-4 outline outline-1 outline-transparent hover:border-primary-600 hover:outline-primary-600"
                            >
                                {({checked}) => (
                                    <div
                                        className="flex w-full items-center"
                                        onClick={() =>
                                            onChange(
                                                'billing-address',
                                                contact.id
                                            )
                                        }
                                    >
                                        {checked ? (
                                            <CheckCircleIcon
                                                className="mr-4 h-7 w-7 text-primary-600"
                                                aria-hidden="true"
                                            />
                                        ) : (
                                            <div className="mr-4 h-7 w-7 rounded-full border"></div>
                                        )}

                                        <div className="flex flex-1">
                                            <div className="flex flex-1 flex-col">
                                                <UiRadioGroup.Label
                                                    as="span"
                                                    className="flex items-center justify-between text-sm font-medium"
                                                >
                                                    {contact.name}

                                                    <div
                                                        className="group/inner ml-4 flex cursor-pointer items-center justify-center rounded-full p-1.5 transition hover:bg-warning-600"
                                                        onClick={e => {
                                                            e.preventDefault();
                                                            e.stopPropagation();

                                                            onAddressDetail(
                                                                'billing-address',
                                                                contact
                                                            );

                                                            return false;
                                                        }}
                                                    >
                                                        <PencilIcon className="h-4 w-4 transition group-hover/inner:text-white" />
                                                    </div>
                                                </UiRadioGroup.Label>

                                                <UiRadioGroup.Description
                                                    as="span"
                                                    className="mt-2 flex items-center text-sm text-muted"
                                                >
                                                    <address className="not-italic">
                                                        <div>
                                                            {
                                                                contact.address
                                                                    .street
                                                            }
                                                        </div>
                                                        {!!contact.address
                                                            .street2 && (
                                                            <div>
                                                                {
                                                                    contact
                                                                        .address
                                                                        .street2
                                                                }
                                                            </div>
                                                        )}
                                                        {contact.address
                                                            .subDistrict && (
                                                            <div>
                                                                {
                                                                    contact
                                                                        .address
                                                                        .subDistrict
                                                                }
                                                            </div>
                                                        )}
                                                        <div className="flex items-center space-x-2">
                                                            {contact.address
                                                                .district && (
                                                                <span>
                                                                    {
                                                                        contact
                                                                            .address
                                                                            .district
                                                                    }
                                                                </span>
                                                            )}
                                                            <span>
                                                                {
                                                                    contact
                                                                        .address
                                                                        .city
                                                                }
                                                            </span>
                                                            {contact.address
                                                                .state && (
                                                                <span>
                                                                    {
                                                                        contact
                                                                            .address
                                                                            .state
                                                                    }
                                                                </span>
                                                            )}
                                                        </div>
                                                        <div className="flex items-center space-x-2">
                                                            <span>
                                                                {
                                                                    contact
                                                                        .address
                                                                        .postalCode
                                                                }
                                                            </span>
                                                            <span>
                                                                {
                                                                    contact
                                                                        .address
                                                                        .countryName
                                                                }
                                                            </span>
                                                        </div>
                                                    </address>
                                                </UiRadioGroup.Description>
                                            </div>
                                        </div>

                                        <div
                                            className={cls(
                                                checked
                                                    ? 'border-primary-600'
                                                    : 'border-transparent',
                                                'pointer-events-none absolute -inset-px rounded-md border-2'
                                            )}
                                            aria-hidden="true"
                                        />
                                    </div>
                                )}
                            </UiRadioGroup.Option>
                        ))}

                    <div
                        className="shadow-small flex cursor-pointer flex-col items-center justify-center rounded-md border bg-white px-4 py-8 text-sm outline outline-1 outline-transparent transition hover:border-green-600 hover:outline-primary-600"
                        onClick={() => onAddressDetail('billing-address')}
                    >
                        <PlusIcon className="mb-3 h-7 w-7" />
                        <div className="mb-0.5 font-medium">
                            {t('New Address')}
                        </div>
                        <div>{t('Add a new billing address.')}</div>
                    </div>
                </div>
            </UiRadioGroup>

            <UiDivider
                orientation="horizontal"
                className="mb-4 mt-6 block xl:hidden"
            />

            <div className="flex flex-col xl:mt-12 xl:flex-row xl:items-center xl:justify-between">
                <UiLink
                    className="hidden text-primary-600 transition hover:text-primary-900 xl:block"
                    href="/"
                >
                    <span aria-hidden="true">&larr; </span>
                    {t('Continue Shopping')}
                </UiLink>

                <UiButton
                    type="submit"
                    variant="solid"
                    color="primary"
                    size="lg"
                    leftIcon={<ArrowRightIcon className="mr-2 h-4 w-4" />}
                    loading={isLoading}
                    onClick={onSubmit}
                >
                    {t('Continue to Delivery')}
                </UiButton>

                <UiButton
                    className="mt-2 block w-full xl:hidden"
                    variant="light"
                    color="primary"
                    size="md"
                    onClick={() => router.push('/')}
                >
                    <span aria-hidden="true">&larr; </span>
                    {t('Continue Shopping')}
                </UiButton>
            </div>
        </div>
    );
});

if (isDev) {
    CustomerInformation.displayName = 'CustomerInformation';
}

export default CustomerInformation;

import {
    FC,
    memo,
    Mouse<PERSON><PERSON><PERSON><PERSON><PERSON>,
    useCallback,
    useEffect,
    useRef,
    useState
} from 'react';
import {useRouter} from 'next/router';
import {cls, isDev, pushIntoGTMDataLayer} from '@core/helpers';
import {useStore, useTrans, useUI} from '@core/hooks';
import {
    UiAlert,
    UiButton,
    UiCheckbox,
    UiDivider,
    UiLink
} from '@core/components/ui';
import {CheckCircleIcon, CheckIcon} from '@core/icons/solid';
import useCheckout from '../../useCheckout';

import CreditCard from './methods/CreditCard';
import MoneyOrder from './methods/MoneyOrder';
import {CartItem} from '@core/types';

// eslint-disable-next-line react/display-name
const StaticContent: FC<{content?: string}> = ({content = ''}) => (
    <div className="w-full px-6 pb-6 pt-6 xl:pt-0">
        <div
            className="prose"
            dangerouslySetInnerHTML={{
                __html: content
            }}
        />
    </div>
);

const Payment: FC = memo(() => {
    const t = useTrans();
    const router = useRouter();
    const {openSideBar} = useUI();
    const {currency} = useStore();
    const {
        approveOrder,
        cart,
        paymentMethods,
        updatePaymentMethodId,
        errorMessage,
        setErrorMessage,
        isLoading,
        setIsLoading,
        setProcessPayment,
        salesContractText,
        preliminaryInformationForm
    } = useCheckout();
    const [isApproved, setIsApproved] = useState(false);
    const [triedWithoutApprove, setTriedWithoutApprove] = useState(false);
    const inProgress = useRef(false);

    // ---------- Google Tag Manager ----------
    useEffect(() => {
        try {
            pushIntoGTMDataLayer({
                event: 'add_payment_info',
                data: {
                    currency: currency.name === 'TL' ? 'TRY' : currency.name,
                    value: cart.items
                        .map((item: CartItem) => item.price * item.quantity)
                        .reduce((a, b) => a + b, 0),
                    coupon: cart.couponCode || null,
                    payment_type: 'Kredi Kartı',
                    items: cart.items.map(item => ({
                        item_id: item.productCode,
                        item_name: item.productName,
                        discount: item.discountedPrice
                            ? item.price - item.discountedPrice
                            : 0,
                        price: item.price,
                        brandName: item.brandName,
                        productCategory: item.productCategory,
                        quantity: item.quantity
                    }))
                }
            });
        } catch (error) {}
    }, [cart.couponCode, cart.items, currency.name]);

    const onApproveOrder = useCallback(
        async () => {
            if (inProgress.current) return;

            if (!isApproved) {
                setTriedWithoutApprove(true);

                return;
            }

            inProgress.current = true;
            setIsLoading(true);
            setTriedWithoutApprove(false);

            try {
                await approveOrder();

                setIsLoading(false); // Delete this.
            } catch (error: any) {
                setErrorMessage(error.message);

                const container = document.querySelector('.content-wrapper');
                if (container !== null) {
                    container.scrollTo({top: 0, behavior: 'smooth'});
                }
            }

            setIsLoading(false);
            inProgress.current = false;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [isApproved]
    );
    const onPaymentMethodIdChange = useCallback(
        async (paymentMethodId: string) => {
            if (cart.paymentMethodId === paymentMethodId) {
                return;
            }

            if (inProgress.current) return;
            inProgress.current = true;

            setIsLoading(true);

            try {
                setProcessPayment(undefined);
                await updatePaymentMethodId(paymentMethodId);
            } catch (error: any) {
                setErrorMessage(t(error.message));
            }

            setIsLoading(false);
            inProgress.current = false;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [cart]
    );

    const onPaymentApproveTextClick: MouseEventHandler<HTMLDivElement> =
        useCallback(
            e => {
                // @ts-ignore
                const id = e.target.id;

                if (id === 'preliminaryInformationForm') {
                    e.preventDefault();

                    openSideBar(
                        t('Preliminary Information Form'),
                        <StaticContent content={preliminaryInformationForm} />,
                        'large'
                    );
                } else if (id === 'salesContractText') {
                    e.preventDefault();

                    openSideBar(
                        t('Distance Sales Contract'),
                        <StaticContent content={salesContractText} />,
                        'large'
                    );
                }
            },
            [preliminaryInformationForm, salesContractText, t, openSideBar]
        );

    return (
        <div className="px-4">
            <div className="mb-4 text-lg font-medium xl:mb-6">
                {t('Payment Methods')}
            </div>

            {!!errorMessage && (
                <UiAlert className="my-4 xl:mb-6" color="danger">
                    {t(errorMessage)}
                </UiAlert>
            )}

            <div className="space-y-2 xl:space-y-4">
                {paymentMethods.map(paymentMethod => (
                    <div
                        key={paymentMethod.id}
                        className={cls(
                            cart.paymentMethodId === paymentMethod.id &&
                                'border-transparent',
                            'shadow-small relative rounded-md border bg-white p-4 focus:outline-none'
                        )}
                    >
                        <>
                            <div
                                className="flex cursor-pointer select-none items-center"
                                onClick={() =>
                                    onPaymentMethodIdChange(paymentMethod.id)
                                }
                            >
                                {cart.paymentMethodId === paymentMethod.id ? (
                                    <CheckCircleIcon
                                        className="mr-4 h-7 w-7 text-primary-600"
                                        aria-hidden="true"
                                    />
                                ) : (
                                    <div className="mr-4 h-7 w-7 rounded-full border"></div>
                                )}

                                <div className="flex-1">
                                    <div className="flex flex-1 flex-col">
                                        <span className="block text-sm font-medium">
                                            {t(paymentMethod.name)}
                                        </span>

                                        <span className="mt-1 flex items-center text-sm text-muted">
                                            {t(paymentMethod.description)}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            {cart.paymentMethodId === paymentMethod.id &&
                                paymentMethod.type === 'credit-card' && (
                                    <CreditCard paymentMethod={paymentMethod} />
                                )}
                            {cart.paymentMethodId === paymentMethod.id &&
                                paymentMethod.type === 'money-order' && (
                                    <MoneyOrder paymentMethod={paymentMethod} />
                                )}

                            <div
                                className={cls(
                                    cart.paymentMethodId === paymentMethod.id
                                        ? 'border-primary-600'
                                        : 'border-transparent',
                                    'pointer-events-none absolute -inset-px rounded-md border-2'
                                )}
                                aria-hidden="true"
                            />
                        </>
                    </div>
                ))}
            </div>

            <div
                className={cls(
                    'shadow-small relative mt-4 rounded-md border bg-white p-4 focus:outline-none xl:mt-6',
                    {
                        'border-red-600 bg-red-50': triedWithoutApprove,
                        'border-secondary-300 bg-white': !triedWithoutApprove
                    }
                )}
            >
                <UiCheckbox
                    // @ts-ignore
                    onChange={e => setIsApproved(!!e.target.checked)}
                    className="mt-1 self-start"
                    // @ts-ignore
                    invalid={triedWithoutApprove ? true : undefined}
                >
                    <div
                        onClick={onPaymentApproveTextClick}
                        dangerouslySetInnerHTML={{
                            __html: t(
                                'I have read and accept the <span id="preliminaryInformationForm" class="text-primary-600">preliminary information form/span> and <span id="salesContractText" class="text-primary-600">distance sales contract/span>.'
                            )
                        }}
                    ></div>
                </UiCheckbox>
            </div>

            <UiDivider
                orientation="horizontal"
                className="mb-4 mt-6 block xl:hidden"
            />

            <div className="flex flex-col xl:mt-12 xl:flex-row xl:items-center xl:justify-between">
                <UiLink
                    className="hidden text-primary-600 transition hover:text-primary-900 xl:block"
                    href="/"
                >
                    <span aria-hidden="true">&larr; </span>
                    {t('Continue Shopping')}
                </UiLink>

                <UiButton
                    type="submit"
                    variant="solid"
                    color="primary"
                    size="lg"
                    leftIcon={<CheckIcon className="mr-2 h-4 w-4" />}
                    loading={isLoading}
                    onClick={onApproveOrder}
                >
                    {t('Approve Order')}
                </UiButton>

                <UiButton
                    className="mt-2 block w-full xl:hidden"
                    variant="light"
                    color="primary"
                    size="md"
                    onClick={() => router.push('/')}
                >
                    <span aria-hidden="true">&larr; </span>
                    {t('Continue Shopping')}
                </UiButton>
            </div>
        </div>
    );
});

if (isDev) {
    Payment.displayName = 'Payment';
}

export default Payment;

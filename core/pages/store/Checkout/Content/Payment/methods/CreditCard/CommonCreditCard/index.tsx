import {FC, memo, useCallback, useEffect, useState} from 'react';
import {isDev, jsonRequest, trim} from '@core/helpers';
import {useCart, useTrans, useUI} from '@core/hooks';
import useCheckout from '@core/pages/store/Checkout/useCheckout';
import CardBox from './CardBox';
import Form from './Form';
import InstallmentOptions from './InstallmentOptions';
import IFrameRedirection from './IFrameRedirection';

const CommonCreditCard: FC = memo(() => {
    const t = useTrans();
    const {openModal} = useUI();
    const {setCart} = useCart();
    const {cart, setProcessPayment, setIsLoading} = useCheckout();
    const [cardBrand, setCardBrand] = useState('none');
    const [cardBrandLogo, setCardBrandLogo] = useState<string>();
    const [bankLogo, setBankLogo] = useState<string>();
    const [cardSchemaLogo, setCardSchemaLogo] = useState<string>();
    const [cardNumber, setCardNumber] = useState<string>('');
    const [cardHolder, setCardHolder] = useState<string>('');
    const [cardExpiry, setCardExpiry] = useState<string>('');
    const [cardCvv, setCardCvv] = useState<string>('');
    const [focussedField, setFocussedField] = useState<string>();

    useEffect(() => {
        setProcessPayment(async () => {
            const payload = {
                cardBrand: cardBrand ?? 'none',
                cardNumber: '',
                cardHolder: '',
                expireMonth: 1,
                expireYear: new Date().getFullYear(),
                cvv: ''
            };

            payload.cardNumber = trim(
                cardNumber.replaceAll('-', '').replaceAll(' ', '')
            );
            if (payload.cardNumber.length !== 16) {
                throw new Error(t('Card number is invalid'));
            }

            payload.cardHolder = trim(cardHolder);
            if (payload.cardHolder.length < 3) {
                throw new Error(t('Card holder is invalid'));
            }

            const expiryStr = trim(
                cardExpiry
                    .replaceAll(' ', '')
                    .replaceAll('-', '')
                    .replaceAll('/', '')
            );
            if (expiryStr.length < 4) {
                throw new Error(t('Card expiry is invalid'));
            }
            const month = parseInt(expiryStr.slice(0, 2));
            if (!(month >= 1 && month <= 12)) {
                throw new Error(t('Card expiry is invalid'));
            }
            const currentYearStr = new Date().getFullYear().toString();
            const year = parseInt(
                `${currentYearStr.slice(0, 2)}${expiryStr.slice(2)}`
            );
            payload.expireMonth = month;
            payload.expireYear = year;

            payload.cvv = trim(cardCvv.replaceAll('-', '').replaceAll(' ', ''));
            if (payload.cvv.length < 3) {
                throw new Error(t('Card cvv is invalid'));
            }

            const paymentIntent = await jsonRequest({
                url: '/api/checkout/create-payment-intent',
                method: 'POST',
                data: payload
            });

            if (paymentIntent.status === 'redirect') {
                await new Promise((resolve, reject) => {
                    openModal(t('3D Validation'), IFrameRedirection, {
                        url: paymentIntent.url,
                        isClosable: false,
                        onSuccess: () => {
                            resolve(true);
                        },
                        onError: (errorMessage: string) => {
                            reject(new Error(errorMessage));
                        }
                    });
                });
            }

            return {
                paymentIntentCode: paymentIntent.code,
                documentNo: paymentIntent.code,
                skipOrderApproval: paymentIntent.skipOrderApproval
            };
        });
    }, [
        cardBrand,
        cardCvv,
        cardExpiry,
        cardHolder,
        cardNumber,
        openModal,
        setProcessPayment,
        t
    ]);

    const onChange = useCallback(
        (field: string, value: string) => {
            if (field === 'cardNumber') {
                if (value !== cardNumber) {
                    const normalizedCardNumber = value
                        .replaceAll(' ', '')
                        .replaceAll('-', '');

                    if (normalizedCardNumber.length === 16) {
                        (async () => {
                            setIsLoading(true);

                            try {
                                const {info, cart: newCart} = await jsonRequest(
                                    {
                                        url: '/api/checkout/get-card-details',
                                        method: 'POST',
                                        data: {cardNumber: normalizedCardNumber}
                                    }
                                );

                                setCart(newCart);
                                setCardBrand(info.cardBrand);
                                setCardBrandLogo(info.cardBrandLogo);
                                setBankLogo(info.bankLogo);
                                setCardSchemaLogo(info.cardSchemaLogo);
                            } catch (error: any) {}

                            setIsLoading(false);
                        })();
                    }
                }

                setCardNumber(value);
            }
            if (field === 'cardHolder') {
                setCardHolder(value);
            }
            if (field === 'cardExpiry') {
                setCardExpiry(value);
            }
            if (field === 'cardCvv') {
                setCardCvv(value);
            }
        },
        [cardNumber, setCart, setIsLoading]
    );
    const onFocus = useCallback((field: string) => {
        setTimeout(() => {
            setFocussedField(field);
        }, 25);
    }, []);
    const onBlur = useCallback(() => {
        setFocussedField(undefined);
    }, []);

    return (
        <div className="mt-5 border-t pt-4">
            <CardBox
                cardBrandLogo={cardBrandLogo}
                bankLogo={bankLogo}
                cardSchemaLogo={cardSchemaLogo}
                cardNumber={cardNumber}
                cardHolder={cardHolder}
                cardExpiry={cardExpiry}
                cardCvv={cardCvv}
                focussedField={focussedField}
            />

            <Form onChange={onChange} onFocus={onFocus} onBlur={onBlur} />

            {(cart.installmentOptions ?? []).length > 0 && (
                <InstallmentOptions
                    installmentOptions={cart.installmentOptions ?? []}
                    installmentCount={cart.installmentCount}
                />
            )}
        </div>
    );
});

if (isDev) {
    CommonCreditCard.displayName = 'CommonCreditCard';
}

export default CommonCreditCard;

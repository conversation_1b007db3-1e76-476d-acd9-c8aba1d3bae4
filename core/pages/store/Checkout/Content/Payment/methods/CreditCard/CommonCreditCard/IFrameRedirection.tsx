import {FC, memo, useEffect} from 'react';
import {isDev} from '@core/helpers';
import {useUI} from '@core/hooks';

type IFrameRedirectionProps = {
    url: string;
    onSuccess: () => void;
    onError: (errorMessage: string) => void;
};

const IFrameRedirection: FC<IFrameRedirectionProps> = memo(
    ({url, onSuccess, onError}) => {
        const {closeModal} = useUI();

        useEffect(() => {
            const onMessage = (e: MessageEvent) => {
                if (typeof e.data !== 'string' || e.data.length < 1) {
                    return;
                }

                let payload;
                try {
                    payload = JSON.parse(e.data);
                } catch (e) {}

                if (!payload) return;

                if (payload.status === 'success') {
                    onSuccess();
                } else {
                    const errorMessage = payload.message;

                    if (
                        !!payload.functionName &&
                        payload.functionName === 'iframePingPop'
                    ) {
                        return;
                    }

                    if (!errorMessage) return;

                    onError(errorMessage);
                }

                closeModal();
            };

            window.addEventListener('message', onMessage);

            return () => {
                window.removeEventListener('message', onMessage);
            };
        }, [closeModal, onError, onSuccess]);

        return (
            <iframe
                src={url}
                className="h-full w-full border-none xl:min-h-[640px]"
                width="100%"
                height="100%"
            />
        );
    }
);

if (isDev) {
    IFrameRedirection.displayName = 'IFrameRedirection';
}

export default IFrameRedirection;

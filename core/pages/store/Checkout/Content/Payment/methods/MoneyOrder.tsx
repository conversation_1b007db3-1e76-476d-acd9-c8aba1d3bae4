import {FC, memo, useCallback, useEffect, useRef} from 'react';
import {isDev} from '@core/helpers';
import {PaymentMethod} from '@core/types';
import {useTrans} from '@core/hooks';
import {UiRadioGroup} from '@core/components/ui';
import {CheckCircleIcon} from '@core/icons/solid';
import useCheckout from '../../../useCheckout';

type MoneyOrderProps = {
    paymentMethod: PaymentMethod;
};

const MoneyOrder: FC<MoneyOrderProps> = memo(({paymentMethod}) => {
    const {bankAccounts} = paymentMethod;
    const t = useTrans();
    const {
        cart,
        setProcessPayment,
        updateSubPaymentMethodId,
        isLoading,
        setErrorMessage,
        setIsLoading
    } = useCheckout();
    const inProgress = useRef(false);

    useEffect(() => {
        setProcessPayment(async () => {
            if (!cart.subPaymentMethodId) {
                throw new Error(
                    t(
                        'Before you can approve the order with the money transfer method, you must first select the bank account to which you will transfer.'
                    )
                );
            }

            return {};
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [cart, setProcessPayment]);

    const onSubPaymentMethodIdChange = useCallback(
        async (subPaymentMethodId: string) => {
            if (cart.subPaymentMethodId === subPaymentMethodId) {
                return;
            }

            if (inProgress.current) return;
            inProgress.current = true;

            setIsLoading(true);

            try {
                await updateSubPaymentMethodId(subPaymentMethodId);
            } catch (error: any) {
                setErrorMessage(error.message);
            }

            setIsLoading(false);
            inProgress.current = false;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [cart]
    );

    if (!bankAccounts) return null;

    return (
        <div className="mt-5 border-t pt-4">
            <UiRadioGroup
                value={cart.subPaymentMethodId}
                onChange={() => null}
                disabled={isLoading}
            >
                <div className="-mb-1 -mt-2 ml-4 divide-y">
                    {bankAccounts.map(bankAccount => (
                        <UiRadioGroup.Option
                            key={bankAccount.paymentMethodId}
                            value={bankAccount.paymentMethodId}
                            className="relative flex items-center bg-white focus:outline-none"
                        >
                            {({checked, active}) => (
                                <div
                                    className="flex cursor-pointer select-none items-center py-3"
                                    onClick={() =>
                                        onSubPaymentMethodIdChange(
                                            bankAccount.paymentMethodId
                                        )
                                    }
                                >
                                    {checked ? (
                                        <CheckCircleIcon
                                            className="mr-4 h-6 w-6 text-primary-600"
                                            aria-hidden="true"
                                        />
                                    ) : (
                                        <div className="mr-4 h-6 w-6 rounded-full border border-gray-300"></div>
                                    )}

                                    <div className="flex flex-1 items-center">
                                        <div className="mr-3 rounded">
                                            {/* eslint-disable-next-line @next/next/no-img-element */}
                                            <img
                                                src={bankAccount.bankLogo}
                                                alt={bankAccount.bankName}
                                                className="rounded"
                                                style={{
                                                    width: 'auto',
                                                    height: '60px'
                                                }}
                                            />
                                        </div>

                                        <div className="flex flex-1 flex-col">
                                            <UiRadioGroup.Label
                                                as="span"
                                                className="block text-sm font-medium"
                                            >
                                                {bankAccount.bankName}
                                            </UiRadioGroup.Label>

                                            <UiRadioGroup.Description
                                                as="span"
                                                className="mt-1 flex items-center text-sm text-muted"
                                            >
                                                {bankAccount.bankBranchName}
                                            </UiRadioGroup.Description>
                                            <UiRadioGroup.Description
                                                as="span"
                                                className="mt-1 flex select-text items-center text-sm text-muted"
                                            >
                                                {bankAccount.iban}
                                            </UiRadioGroup.Description>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </UiRadioGroup.Option>
                    ))}
                </div>
            </UiRadioGroup>
        </div>
    );
});

if (isDev) {
    MoneyOrder.displayName = 'MoneyOrder';
}

export default MoneyOrder;

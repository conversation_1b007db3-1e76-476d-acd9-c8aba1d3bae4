import {FC, memo, useMemo} from 'react';
import {cls, isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import useCheckout from '../useCheckout';

type StepProps = {
    text: string;
    isCurrent?: boolean;
    isCompleted?: boolean;
    isDisabled?: boolean;
    onClick?: () => void;
};

const Step: FC<StepProps> = ({
    text,
    isCurrent,
    isCompleted,
    onClick,
    isDisabled
}) => {
    return (
        <button
            className={cls(
                'relative flex h-9 items-center justify-center rounded-full border px-6',
                'select-none text-sm font-medium leading-3',
                {
                    'border-primary-600 bg-primary-100 text-primary-600':
                        isCurrent && !isCompleted,
                    'border-primary-600 bg-white text-primary-600':
                        !isCurrent && !isCompleted,
                    'cursor-pointer border-primary-600 bg-primary-600 text-white':
                        isCompleted && !isDisabled,
                    'cursor-default border-primary-600 bg-primary-600 text-white':
                        isCompleted && isDisabled
                }
            )}
            style={{minWidth: '120px'}}
            onClick={() => isCompleted && !isDisabled && !!onClick && onClick()}
        >
            <span className="whitespace-nowrap">{text}</span>

            {isCurrent && (
                <div className="absolute left-1/2 right-auto top-full -ml-3 w-8 overflow-hidden">
                    <div className="h-4 w-4 origin-top-left -rotate-45 transform border border-primary-600 bg-primary-100"></div>
                </div>
            )}
        </button>
    );
};

const Steps: FC = memo(() => {
    const t = useTrans();
    const {cart, updateStep} = useCheckout();
    const step = useMemo(() => cart.step, [cart]);

    return (
        <div className="flex flex-1 items-center justify-center">
            <Step
                text={t('Information')}
                isCurrent={step === 'information'}
                isCompleted={step === 'delivery' || step === 'payment'}
                isDisabled={cart.status === 'completed'}
                onClick={() => updateStep('information')}
            />
            <div className="mx-2 w-16 border"></div>
            <Step
                text={t('Delivery')}
                isCurrent={step === 'delivery'}
                isCompleted={step === 'payment'}
                isDisabled={cart.status === 'completed'}
                onClick={() => updateStep('delivery')}
            />
            <div className="mx-2 w-16 border"></div>
            <Step
                text={t('Payment')}
                isCurrent={step === 'payment' && cart.status !== 'completed'}
                isDisabled={cart.status === 'completed'}
                isCompleted={cart.status === 'completed'}
            />
        </div>
    );
});

if (isDev) {
    Steps.displayName = 'Steps';
}

export default Steps;

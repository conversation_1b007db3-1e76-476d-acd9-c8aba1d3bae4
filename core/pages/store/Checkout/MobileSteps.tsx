import {FC, memo, useEffect, useMemo} from 'react';
import {cls, isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import useCheckout from './useCheckout';
import {CheckCircleIcon} from '@core/icons/regular';
import {CheckCircleIcon as CheckCircleSolidIcon} from '@core/icons/solid';

type MobileStepProps = {
    text: string;
    isCurrent?: boolean;
    isCompleted?: boolean;
    isDisabled?: boolean;
    onClick?: () => void;
};

const MobileStep: FC<MobileStepProps> = memo(
    ({text, isCurrent, isCompleted, onClick, isDisabled}) => {
        return (
            <button
                className={cls('flex flex-col items-center justify-center', {
                    'text-primary-600': isCurrent && !isCompleted,
                    'text-default': !isCurrent && !isCompleted,
                    'text-success-600': isCompleted && !isDisabled,
                    'text-muted': isCompleted && isDisabled
                })}
                onClick={() =>
                    isCompleted && !isDisabled && !!onClick && onClick()
                }
            >
                {(isCurrent || !isCompleted) && (
                    <CheckCircleIcon className="h-4 w-4" />
                )}
                {isCompleted && !isDisabled && (
                    <CheckCircleSolidIcon className="h-4 w-4" />
                )}
                <div className="mt-1.5 text-sm">
                    <span className="whitespace-nowrap">{text}</span>
                </div>
            </button>
        );
    }
);

if (isDev) {
    MobileStep.displayName = 'MobileStep';
}

type Props = {
    setStep: React.Dispatch<React.SetStateAction<string>>;
};

const MobileSteps: FC<Props> = memo(({setStep}) => {
    const t = useTrans();
    const {cart, updateStep} = useCheckout();
    const step = useMemo(() => cart.step, [cart]);

    useEffect(() => {
        setStep(step);
    }, [step]);

    return (
        <div className="card-container flex items-center justify-center py-3 xl:hidden">
            <MobileStep
                text={t('Information')}
                isCurrent={step === 'information'}
                isCompleted={step === 'delivery' || step === 'payment'}
                isDisabled={cart.status === 'completed'}
                onClick={() => updateStep('information')}
            />
            <div className="mx-3 w-12 border border-primary-100"></div>
            <MobileStep
                text={t('Delivery')}
                isCurrent={step === 'delivery'}
                isCompleted={step === 'payment'}
                isDisabled={cart.status === 'completed'}
                onClick={() => updateStep('delivery')}
            />
            <div className="mx-3 w-12 border border-primary-100"></div>
            <MobileStep
                text={t('Payment')}
                isCurrent={step === 'payment' && cart.status !== 'completed'}
                isDisabled={cart.status === 'completed'}
                isCompleted={cart.status === 'completed'}
            />
        </div>
    );
});

if (isDev) {
    MobileSteps.displayName = 'MobileSteps';
}

export default MobileSteps;

import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useCart, useTrans} from '@core/hooks';
import {UiStickyBox} from '@core/components/ui';
import Price from '@components/common/Price';
import Actions from './Actions';
import storeConfig from '~/store.config';

const SideBar: FC = memo(() => {
    const t = useTrans();
    const {cart} = useCart();

    return (
        <div className="relative w-80">
            <UiStickyBox
                offsetTop={
                    parseInt(storeConfig.theme.headerHeight.replace('px', '')) +
                    8
                }
            >
                <div className="card-container p-6">
                    <h2 id="summary-heading" className="text-lg font-medium">
                        {t('Order Summary')}
                    </h2>

                    <div className="mt-6">
                        <div className="flex items-center justify-between pb-2.5 text-sm">
                            <div>{t('Products total')}</div>
                            <Price
                                className="font-medium"
                                price={cart.subTotal}
                            />
                        </div>
                        <div className="flex items-center justify-between border-t py-2.5 text-sm">
                            <div>{t('Tax estimate')}</div>
                            <Price
                                className="font-medium"
                                price={cart.taxTotal}
                            />
                        </div>
                        <div className="flex items-center justify-between border-t py-2.5 text-sm">
                            <div>{t('Delivery estimate')}</div>
                            <Price
                                className="font-medium"
                                price={cart.deliveryTotal}
                            />
                        </div>
                        {Array.isArray(cart.discounts) &&
                            cart.discounts.length > 0 &&
                            cart.discounts.map(discount => (
                                <div
                                    key={discount.id}
                                    className="flex items-center justify-between border-t py-2.5 text-xs"
                                >
                                    <div>{discount.description}</div>
                                    <Price
                                        className="font-medium text-primary-600"
                                        price={-discount.amount}
                                    />
                                </div>
                            ))}
                        <div className="flex items-center justify-between border-t pt-2.5 text-base">
                            {typeof cart.discountTotalIncludingProductDiscounts ===
                                'number' &&
                                cart.discountTotalIncludingProductDiscounts >
                                    0 && (
                                    <div className="flex flex-col items-center justify-center rounded-md bg-primary-100 px-2 py-1.5 text-xs font-medium text-primary-600">
                                        <p className="text-muted">
                                            {t('Total Discount')}
                                        </p>
                                        <Price
                                            price={
                                                cart.discountTotalIncludingProductDiscounts
                                            }
                                        />
                                    </div>
                                )}
                            <Price
                                className="ml-auto font-semibold"
                                price={cart.grandTotal}
                            />
                        </div>
                    </div>
                </div>

                <Actions />
            </UiStickyBox>
        </div>
    );
});

if (isDev) {
    SideBar.displayName = 'SideBar';
}

export default SideBar;

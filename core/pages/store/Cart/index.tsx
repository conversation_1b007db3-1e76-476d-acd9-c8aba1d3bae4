import {FC, memo, useEffect, useState} from 'react';
import {CartItem, Cart as CartType} from '@core/types';
import {isDev, pushIntoGTMDataLayer} from '@core/helpers';
import {useCart, useStore, useTrans} from '@core/hooks';
import {TrashIcon} from '@core/icons/outline';

import Seo from '@components/common/Seo';
import Items from './Items';
import SideBar from './Sidebar';

type CartProps = {
    cart: CartType;
};

const Cart: FC<CartProps> = memo(props => {
    const t = useTrans();
    const {productCount, isLoading, cart, setCart, removeItems} = useCart();
    const {currency} = useStore();

    // Set Initial cart.
    useEffect(() => {
        if (typeof cart !== 'undefined') {
            setCart(cart);
            // ---------- Google Tag Manager ----------
            pushIntoGTMDataLayer({
                event: 'view_cart',
                data: {
                    currency: currency.name === 'TL' ? 'TRY' : currency.name,
                    value: cart.items
                        .map((item: CartItem) => item.price * item.quantity)
                        .reduce((a, b) => a + b, 0),
                    items: cart.items.map(item => ({
                        item_id: item.productCode,
                        item_name: item.productName,
                        discount: item.discountedPrice
                            ? item.price - item.discountedPrice
                            : 0,
                        price: item.price,
                        item_brand: item.brandName,
                        item_category: item.productCategory,
                        quantity: item.quantity
                    }))
                }
            });
            // ----------------------------------------
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <>
            <Seo title={t('My Cart')} />

            <div className="">
                <div className="relative">
                    <div className="flex items-stretch space-x-4 ">
                        <div className="grid flex-1 gap-8">
                            <div className="card-container h-fit flex-1  border border-gray-200 p-8 shadow-sm">
                                {productCount > 0 && (
                                    <div className="mb-8 flex items-center justify-between gap-4">
                                        <h1 className="text-2xl font-medium">
                                            {t('My Cart')} (
                                            {productCount > 1
                                                ? t('{count} Products', {
                                                      count: productCount
                                                  })
                                                : t('{count} Product', {
                                                      count: 1
                                                  })}
                                            )
                                        </h1>
                                        <button
                                            onClick={removeItems}
                                            className="flex items-center gap-2 text-sm font-medium text-muted hover:text-danger-600"
                                        >
                                            <span>{t('Remove Products')}</span>
                                            <TrashIcon className="h-4 w-4" />
                                        </button>
                                    </div>
                                )}

                                <Items />
                            </div>
                        </div>

                        <SideBar />
                    </div>

                    {isLoading && (
                        <div className="absolute inset-0 flex h-full w-full items-center justify-center bg-white bg-opacity-20"></div>
                    )}
                </div>
            </div>
        </>
    );
});

if (isDev) {
    Cart.displayName = 'Cart';
}

export default Cart;

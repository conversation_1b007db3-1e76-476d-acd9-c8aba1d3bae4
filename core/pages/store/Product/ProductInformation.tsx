import {FC, memo, useEffect} from 'react';
import {isDev} from '@core/helpers';
import {useIntersection, useTrans} from '@core/hooks';
import {UiDivider} from '@core/components/ui';
import useProduct from './useProduct';

const ProductInformation: FC = memo(() => {
    const t = useTrans();
    const {selectedProduct, setActiveTab} = useProduct();

    const [ref, observer] = useIntersection({
        threshold: 0.5
    });
    useEffect(() => {
        if (observer?.isIntersecting) {
            setActiveTab('productInformation');
        }
    }, [observer, setActiveTab]);

    return (
        <div
            ref={ref}
            id="productInformation"
            className="xl:card-container mb-12 mt-12 xl:p-10"
        >
            <h2 className="text-xl font-medium">{t('Product Information')}</h2>

            <div className="mt-6 grid xl:gap-8">
                <div
                    className="prose max-w-none"
                    dangerouslySetInnerHTML={{
                        __html: selectedProduct.description ?? ''
                    }}
                />
            </div>

            {selectedProduct.features.length > 0 && (
                <>
                    <UiDivider className="my-6 opacity-100" />

                    <h3 className="font-medium leading-4">
                        {t('Product Features')}
                    </h3>

                    <div className="mt-6 grid grid-cols-12 gap-5">
                        {selectedProduct.features.map((feature, index) => (
                            <div
                                className="shadow-small col-span-6 flex cursor-default items-center justify-between rounded-md border-2 border-transparent bg-secondary-100 px-4 py-2.5 text-sm transition hover:border-primary-600 hover:bg-white"
                                key={feature.code + index}
                            >
                                <div className="font-medium">
                                    {feature.label}
                                </div>
                                <div className="text-muted">
                                    {feature.value}
                                </div>
                            </div>
                        ))}
                    </div>
                </>
            )}
        </div>
    );
});

if (isDev) {
    ProductInformation.displayName = 'ProductInformation';
}

export default ProductInformation;

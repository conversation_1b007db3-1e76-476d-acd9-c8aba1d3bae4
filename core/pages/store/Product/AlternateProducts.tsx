import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useIntersection, useTrans} from '@core/hooks';
import ProductSlider from '@components/common/ProductSlider';
import {Product} from '@core/types';

const AlternateProducts: FC<{alternateProducts?: Product[]}> = memo(props => {
    const {alternateProducts = []} = props;
    const t = useTrans();

    const [ref, observer] = useIntersection({
        threshold: 0.5
    });

    return (
        <div
            ref={ref}
            id="alternate-products"
            className="card-container my-12 p-4"
        >
            <h2 className="text-xl font-medium">{t('Similar Products')}</h2>

            <div className="mt-4">
                <ProductSlider products={alternateProducts} />
            </div>
        </div>
    );
});

if (isDev) {
    AlternateProducts.displayName = 'AlternateProducts';
}

export default AlternateProducts;

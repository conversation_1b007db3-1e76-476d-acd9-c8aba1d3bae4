import {
    createContext,
    FC,
    memo,
    PropsWithChildren,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {
    base64,
    isDev,
    jsonRequest,
    trim,
    pushIntoGTMDataLayer
} from '@core/helpers';
import {useRouter} from 'next/router';
import {
    Campaign,
    CartItem,
    CustomerProductPrams,
    Product,
    ProductListItem,
    ProductOption
} from '@core/types';
import {
    useCart,
    useCustomer,
    useDebouncedCallback,
    useMobile,
    useStore,
    useTrans
} from '@core/hooks';
import {
    getInitialSelectedProduct,
    getProductOptions,
    getSelectedProduct
} from './utils';
import {ProductContextType} from './types';
import ProductCartSummary from '@components/common/ProductCartSummary';
import {notification} from '@core/components/ui';

export const ProductContext = createContext<ProductContextType>({} as any);

if (isDev) {
    ProductContext.displayName = 'ProductContext';
}

type ProductProviderProps = {
    product: Product;
    selectedAttributes?: Record<string, any>;
    relatedProducts: ProductListItem[];
    campaigns: Campaign[];
};

export const ProductProvider: FC<PropsWithChildren<ProductProviderProps>> =
    memo(props => {
        const {
            product: initialProduct,
            selectedAttributes,
            relatedProducts,
            campaigns: productCampaigns,
            ...rest
        } = props;
        const router = useRouter();
        const t = useTrans();
        const {isMobile} = useMobile();
        const {locale, currency} = useStore();
        const customer = useCustomer();
        const {
            cart,
            addItem: addCartItem,
            updateItem: updateCartItem
        } = useCart();
        const [campaigns, setCampaigns] = useState(() => productCampaigns);
        const [product, setProduct] = useState(() => initialProduct);
        const [selectedWarehouseId, setSelectedWarehouseId] = useState(
            () => initialProduct.defaultWarehouseId
        );
        const [selectedProduct, setSelectedProduct] = useState(() =>
            getInitialSelectedProduct(initialProduct, selectedAttributes)
        );

        useEffect(() => {
            const stock =
                selectedProduct.warehouseStocks?.find(
                    w => w.warehouseId === selectedWarehouseId
                )?.availableQuantity ?? 0;

            setProduct(prev => ({...prev, availableQuantity: stock}));
        }, [selectedWarehouseId, selectedProduct]);

        const [productOptions, setProductOptions] = useState(() => {
            const initialSelectedProduct = getInitialSelectedProduct(
                initialProduct,
                selectedAttributes
            );

            let productOptions = [] as ProductOption[];

            if (
                Array.isArray(initialProduct.options) &&
                initialProduct.options.length > 0
            ) {
                const firstOption = initialProduct.options[0];

                if (firstOption.type === 'color') {
                    productOptions = getProductOptions(
                        initialProduct,
                        firstOption.code,
                        initialSelectedProduct.attributes![firstOption.code]
                    );
                } else {
                    productOptions = getProductOptions(initialProduct);
                }
            }

            return productOptions;
        });
        const isInitial = useRef(true);
        const [isAddToCartInProgress, setIsAddToCartInProgress] =
            useState(false);
        const [customerProductParams, setCustomerProductParams] =
            useState<CustomerProductPrams>({
                productId: initialProduct.productId,
                isFavorite: false,
                isInCollection: false,
                isInAlarmCollection: false
            });
        const [activeTab, setActiveTab] = useState('productInformation');
        const [currentPCMPayload, setCurrentPCMPayload] = useState<
            Record<string, any>
        >(() => {});

        // Is initial.
        useEffect(() => {
            setTimeout(() => {
                isInitial.current = false;
            }, 250);
        }, []);

        // Refresh.
        const refresh = useCallback(async () => {
            const result = await jsonRequest({
                url: '/api/catalog/product',
                method: 'POST',
                data: {
                    slug: initialProduct.slug
                }
            });

            setCampaigns(result.campaigns);
            setProduct(result.product);
            setSelectedProduct(
                getInitialSelectedProduct(result.product, selectedAttributes)
            );

            const initialSelectedProduct = getInitialSelectedProduct(
                result.product,
                selectedAttributes
            );
            let productOptions = [] as ProductOption[];
            if (
                Array.isArray(result.product.options) &&
                result.product.options.length > 0
            ) {
                const firstOption = result.product.options[0];

                if (firstOption.type === 'color') {
                    productOptions = getProductOptions(
                        result.product,
                        firstOption.code,
                        initialSelectedProduct.attributes![firstOption.code]
                    );
                } else {
                    productOptions = getProductOptions(result.product);
                }
            }
        }, [initialProduct.slug, selectedAttributes]);

        const seo = useMemo(() => {
            const seo: Record<string, any> = {};

            seo.params = {title: selectedProduct.name};

            return seo;
        }, [selectedProduct]);

        // Available quantity.
        const availableQuantity = useMemo(() => {
            let availableQuantity = selectedProduct.availableQuantity;

            if ((selectedProduct.warehouseStocks ?? []).length > 0) {
                availableQuantity =
                    selectedProduct.warehouseStocks?.find(
                        w => w.warehouseId === selectedWarehouseId
                    )?.availableQuantity ?? 0;
            }

            for (const cartItem of cart.items ?? []) {
                if (
                    cartItem.productId === selectedProduct.productId &&
                    cartItem.warehouseId === selectedWarehouseId
                ) {
                    availableQuantity -= cartItem.quantity;
                }
            }

            return availableQuantity;
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [cart, selectedProduct, selectedWarehouseId]);

        // In stock.
        const inStock = useMemo(
            () => availableQuantity > 0,
            [availableQuantity]
        );

        // Set attribute.
        const setAttribute = useCallback(
            (code: string, value: string) => {
                const newAttributes = {
                    ...selectedProduct.attributes,
                    [code]: value
                };
                const variants = (product.variants ?? []).filter(variant => {
                    return variant.attributes[code] === value;
                });

                let variant = variants.find(variant => {
                    for (const newAttributesCode of Object.keys(
                        newAttributes
                    )) {
                        if (
                            newAttributes[newAttributesCode] !==
                            variant.attributes[newAttributesCode]
                        ) {
                            return false;
                        }
                    }

                    return true;
                });
                if (!variant && variants.length > 0) {
                    variant = variants[0];
                }

                if (!!variant) {
                    const option = product.options.find(
                        option => option.code == code
                    );

                    setSelectedProduct(
                        getSelectedProduct({
                            product,
                            variant,
                            selectedProduct
                        })
                    );

                    if (option?.type === 'color') {
                        setProductOptions(
                            getProductOptions(product, code, value)
                        );

                        router.push(
                            `${trim(product.slug, '/')}-${base64.encode(
                                new URLSearchParams({
                                    ...newAttributes,
                                    _es: 'true'
                                }).toString()
                            )}`,
                            undefined,
                            {
                                shallow: true,
                                locale: router.locale
                            }
                        );
                    } else {
                        router.push(
                            `${trim(product.slug, '/')}-${base64.encode(
                                new URLSearchParams({
                                    ...newAttributes,
                                    _es: 'true'
                                }).toString()
                            )}`,
                            undefined,
                            {
                                shallow: true,
                                locale: router.locale
                            }
                        );
                    }
                }
            },
            [product, selectedProduct, router]
        );

        // Set quantity.
        const setQuantity = useCallback(
            (quantity: number) => {
                quantity = parseFloat(quantity as any);

                if (!isNaN(quantity)) {
                    setSelectedProduct({
                        ...selectedProduct,
                        quantity: quantity
                    });
                }
            },
            [selectedProduct]
        );

        // Add to cart.
        const addToCart = useCallback(() => {
            if (isAddToCartInProgress) {
                return;
            }

            setIsAddToCartInProgress(true);

            (async () => {
                const item: CartItem = {
                    productId: selectedProduct.productId,
                    productSlug: selectedProduct.slug,
                    productImage: (selectedProduct.images as string[])[0],
                    productName:
                        typeof product.brandName === 'string' &&
                        product.brandName.length > 0
                            ? `${product.brandName} ${selectedProduct.name}`
                            : selectedProduct.name,
                    productStockQuantity: selectedProduct.availableQuantity,
                    productRating: selectedProduct.rating,
                    productReviewCount: selectedProduct.reviewCount,
                    productLink: `/${product.slug}`,
                    price: selectedProduct.salesPrice,
                    unitId: selectedProduct.unitId,
                    unitName: selectedProduct.unitName,
                    warehouseId: selectedWarehouseId,
                    quantity: selectedProduct.quantity,
                    weight: selectedProduct.weight,
                    width: selectedProduct.width,
                    height: selectedProduct.height,
                    depth: selectedProduct.depth,
                    volumetricWeight: 0,
                    deliveryType: 'standard',
                    deliveryOptionIds: selectedProduct.deliveryOptionIds ?? [],
                    deliveryPrice: 0,
                    estimatedDeliveryDuration:
                        selectedProduct.estimatedDeliveryDuration,
                    deliveryAtSpecifiedDate:
                        selectedProduct.deliveryAtSpecifiedDate,
                    deliveryAtSpecifiedTime:
                        selectedProduct.deliveryAtSpecifiedTime,
                    selected: true,
                    removed: false
                };

                if (
                    selectedProduct.isPCMProduct &&
                    !!currentPCMPayload &&
                    Object.keys(currentPCMPayload).length > 0
                ) {
                    item.isPCMProduct = true;
                    item.pcmPayload = currentPCMPayload;
                }

                if (
                    !!selectedProduct.attributes &&
                    Object.keys(selectedProduct.attributes).length > 0
                ) {
                    const attributes: {
                        code: string;
                        label: string;
                        value: string;
                        color?: string;
                    }[] = [];

                    for (const code of Object.keys(
                        selectedProduct.attributes
                    )) {
                        const value = selectedProduct.attributes[code];
                        const option = productOptions.find(
                            productOption => productOption.code === code
                        ) as ProductOption;
                        const selection = option.selections.find(
                            selection => selection.value === value
                        );

                        attributes.push({
                            code,
                            label: option.label as string,
                            value,
                            color: selection?.color
                        });
                    }

                    item.productLink = `/${trim(
                        trim(product.slug, '/')
                    )}-${base64.encode(
                        new URLSearchParams({
                            ...selectedProduct.attributes,
                            _es: 'true'
                        }).toString()
                    )}`;

                    item.productAttributes = attributes;
                }
                const inCartProduct = cart.items.find(
                    item =>
                        item.productId === selectedProduct.productId &&
                        item.warehouseId === selectedWarehouseId
                );

                let result = null;

                if (inCartProduct) {
                    result = await updateCartItem({
                        ...item,
                        quantity: item.quantity + inCartProduct.quantity
                    });
                } else {
                    result = await addCartItem(item);
                }

                if (result) {
                    setQuantity(1);

                    // ---------- Google Tag Manager ----------
                    pushIntoGTMDataLayer({
                        event: 'add_to_cart',
                        data: {
                            currency:
                                currency.name === 'TL' ? 'TRY' : currency.name,
                            value:
                                (selectedProduct.unDiscountedSalesPrice
                                    ? selectedProduct.unDiscountedSalesPrice
                                    : selectedProduct.salesPrice) *
                                selectedProduct.quantity,
                            items: [
                                {
                                    item_id: selectedProduct.code,
                                    item_name: selectedProduct.name,
                                    discount:
                                        selectedProduct.unDiscountedSalesPrice >
                                        0
                                            ? selectedProduct.unDiscountedSalesPrice -
                                              selectedProduct.salesPrice
                                            : 0,
                                    item_brand: selectedProduct.brandName,
                                    item_category: selectedProduct.categoryName,
                                    price: selectedProduct.unDiscountedSalesPrice
                                        ? selectedProduct.unDiscountedSalesPrice
                                        : selectedProduct.salesPrice,
                                    quantity: selectedProduct.quantity
                                }
                            ]
                        }
                    });
                    // ----------------------------------------

                    notification({
                        title: t('Added to Cart'),
                        description: t('Product has been added to your cart.'),
                        status: 'success',
                        detailRenderer: closeNotification => (
                            <ProductCartSummary
                                locale={locale}
                                currency={currency}
                                item={item}
                                onDetail={() => {
                                    closeNotification();

                                    if (isMobile) {
                                        router.push(
                                            `/mobile/my-cart?t=${Date.now()}`
                                        );
                                    } else {
                                        router.push(`/cart?t=${Date.now()}`);
                                    }
                                }}
                            />
                        )
                    });
                } else {
                    notification({
                        title: t('Error'),
                        description: t(
                            'An error occurred while adding the product to the cart!'
                        ),
                        status: 'error'
                    });
                }

                setIsAddToCartInProgress(false);
            })();
        }, [
            isAddToCartInProgress,
            selectedProduct.productId,
            selectedProduct.slug,
            selectedProduct.images,
            selectedProduct.name,
            selectedProduct.availableQuantity,
            selectedProduct.rating,
            selectedProduct.reviewCount,
            selectedProduct.salesPrice,
            selectedProduct.unitId,
            selectedProduct.unitName,
            selectedProduct.quantity,
            selectedProduct.weight,
            selectedProduct.width,
            selectedProduct.height,
            selectedProduct.depth,
            selectedProduct.deliveryOptionIds,
            selectedProduct.estimatedDeliveryDuration,
            selectedProduct.deliveryAtSpecifiedDate,
            selectedProduct.deliveryAtSpecifiedTime,
            selectedProduct.isPCMProduct,
            selectedProduct.attributes,
            selectedProduct.unDiscountedSalesPrice,
            selectedProduct.code,
            selectedProduct.brandName,
            selectedProduct.categoryName,
            product.brandName,
            product.slug,
            selectedWarehouseId,
            currentPCMPayload,
            cart.items,
            productOptions,
            updateCartItem,
            addCartItem,
            setQuantity,
            currency,
            t,
            locale,
            isMobile,
            router
        ]);

        // Get customer product params.
        const loadProductParamsForCustomer = () => {
            (async () => {
                try {
                    const params = await jsonRequest<CustomerProductPrams>({
                        url: '/api/customers/product-params-for-customer',
                        method: 'POST',
                        data: {
                            productId: selectedProduct.productId
                        }
                    });

                    setCustomerProductParams(params);
                } catch (error: any) {
                    console.log(error);
                }
            })();
        };
        const loadProductParamsForCustomerDebounced = useDebouncedCallback(
            loadProductParamsForCustomer,
            500,
            [selectedProduct, customer]
        );
        useEffect(() => {
            if (customer) {
                if (isInitial.current) {
                    loadProductParamsForCustomer();
                } else {
                    loadProductParamsForCustomerDebounced();
                }
            }
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [selectedProduct, customer]);

        // Last visited products.
        useEffect(() => {
            let lastVisitedProductIds = JSON.parse(
                localStorage.getItem('lastVisitedProductIds') ?? '[]'
            );

            if (!lastVisitedProductIds.includes(product.productId)) {
                lastVisitedProductIds.unshift(product.productId);

                lastVisitedProductIds = lastVisitedProductIds.slice(0, 50);

                localStorage.setItem(
                    'lastVisitedProductIds',
                    JSON.stringify(lastVisitedProductIds)
                );
            }
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [customer]);

        const value: any = useMemo(
            () => ({
                product,
                selectedProduct,
                productOptions,
                seo,
                isAddToCartInProgress,
                availableQuantity,
                inStock,
                customerProductParams,
                activeTab,
                relatedProducts,
                currentPCMPayload,
                campaigns,
                selectedWarehouseId,

                setProduct,
                setSelectedProduct,
                setProductOptions,
                setAttribute,
                setQuantity,
                addToCart,
                setCustomerProductParams,
                setActiveTab,
                setCurrentPCMPayload,
                setSelectedWarehouseId,
                refresh
            }),
            [
                product,
                selectedProduct,
                productOptions,
                seo,
                isAddToCartInProgress,
                availableQuantity,
                inStock,
                customerProductParams,
                activeTab,
                relatedProducts,
                currentPCMPayload,
                setAttribute,
                setQuantity,
                addToCart,
                refresh,
                campaigns,
                selectedWarehouseId
            ]
        );

        return <ProductContext.Provider value={value} {...rest} />;
    });

if (isDev) {
    ProductProvider.displayName = 'ProductProvider';
}

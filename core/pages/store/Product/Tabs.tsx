import {FC, memo} from 'react';
import {cls, isDev} from '@core/helpers';
import {useScrollIntoView, useTrans} from '@core/hooks';
import {UiStickyBox} from '@core/components/ui';
import useProduct from './useProduct';
import storeConfig from '~/store.config';

const Tabs: FC = memo(() => {
    const t = useTrans();
    const {scrollIntoView: scrollToProductInformation} =
        useScrollIntoView<HTMLDivElement>({
            target: 'productInformation',
            offset: 108
        });
    const {scrollIntoView: scrollToProductReviews} =
        useScrollIntoView<HTMLDivElement>({
            target: 'productReviews',
            offset: 108
        });
    const {scrollIntoView: scrollToRelatedProducts} =
        useScrollIntoView<HTMLDivElement>({
            target: 'relatedProducts',
            offset: 105
        });
    const {activeTab, relatedProducts} = useProduct();

    return (
        <UiStickyBox
            offsetTop={
                parseInt(storeConfig.theme.headerHeight.replace('px', '')) + 8
            }
            className="z-10 hidden xl:block"
        >
            <div className="card-container mt-12 w-full">
                <div className="-mb-px flex justify-center space-x-8">
                    <button
                        className={cls(
                            activeTab === 'productInformation'
                                ? 'border-primary-600 text-primary-600 '
                                : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                            'cursor-pointer whitespace-nowrap border-b-2 py-5 text-sm font-medium uppercase transition'
                        )}
                        onClick={() => scrollToProductInformation()}
                    >
                        {t('Product Information')}
                    </button>

                    <button
                        className={cls(
                            activeTab === 'productReviews'
                                ? 'border-primary-600 text-primary-600'
                                : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                            'cursor-pointer whitespace-nowrap border-b-2 py-5 text-sm font-medium uppercase transition'
                        )}
                        onClick={() => scrollToProductReviews()}
                    >
                        {t('Product Reviews')}
                    </button>

                    {relatedProducts.length > 0 && (
                        <button
                            className={cls(
                                activeTab === 'relatedProducts'
                                    ? 'border-primary-600 text-primary-600'
                                    : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                                'cursor-pointer whitespace-nowrap border-b-2 py-5 text-sm font-medium uppercase transition'
                            )}
                            onClick={() => scrollToRelatedProducts()}
                        >
                            {t('Related Products')}
                        </button>
                    )}
                </div>
            </div>
        </UiStickyBox>
    );
});

if (isDev) {
    Tabs.displayName = 'Tabs';
}

export default Tabs;

import {FC, memo, useEffect} from 'react';
import {isDev} from '@core/helpers';
import Seo from '@components/common/Seo';
import useProduct from './useProduct';

const Meta: FC = memo(() => {
    const {seo} = useProduct();
    const {refresh} = useProduct();

    useEffect(() => {
        // Refresh product for initial load!.
        refresh();
        // eslint-disable-next-line
    }, []);

    return <Seo {...seo.params} />;
});

if (isDev) {
    Meta.displayName = 'Meta';
}

export default Meta;

import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {useScrollIntoView, useStore, useTrans} from '@core/hooks';
import {UiButton, UiRating, UiStickyBox} from '@core/components/ui';
import useProduct from './useProduct';
import ProductCampaigns from './ProductDetails/ProductCampaigns';
import ShipmentOptions from './ProductDetails/ShipmentOptions';
import storeConfig from '~/store.config';

const SideBar: FC = memo(() => {
    const t = useTrans();
    const {scrollIntoView: scrollToProductReviews} =
        useScrollIntoView<HTMLDivElement>({
            target: 'productReviews',
            offset: 108
        });
    const {locale} = useStore();
    const {product} = useProduct();

    const featuredReview = useMemo(() => {
        const review = product.featuredReview;

        if (typeof review !== 'undefined') {
            if (review.content.length > 70) {
                review.content = `${review.content.slice(0, 70)}...`;
            }

            const formatter = new Intl.DateTimeFormat(locale, {
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            });
            const date = new Date(Date.parse(review.createdAt));
            review.dateFornatted = formatter.format(date);
        }

        return review;
    }, [locale, product.featuredReview]);

    return (
        <UiStickyBox
            offsetTop={
                parseInt(storeConfig.theme.headerHeight.replace('px', '')) + 8
            }
        >
            <div className="space-y-4">
                <ProductCampaigns />

                <ShipmentOptions />

                <div className="card-container">
                    <div className="border-b p-4 text-xs font-semibold">
                        {t('FEATURED REVIEW')}
                    </div>

                    {typeof featuredReview !== 'undefined' ? (
                        <div className="flex flex-col p-4">
                            <div className="text-sm leading-5">
                                <span className="inline-block">
                                    <UiRating
                                        className="mr-2"
                                        initialRating={featuredReview.rating}
                                        size="xs"
                                        readonly
                                    />
                                </span>
                                {featuredReview.content}
                            </div>

                            <div className="mt-3 text-xs text-muted">
                                {`${featuredReview.author}  |  ${featuredReview.dateFornatted}`}
                            </div>

                            <UiButton
                                className="button-primary mt-4 w-full"
                                size="sm"
                                variant="outline"
                                onClick={() => scrollToProductReviews()}
                            >
                                {t('More Reviews')}
                            </UiButton>
                        </div>
                    ) : (
                        <div className="p-4 text-xs">
                            {t('No review found!')}
                        </div>
                    )}
                </div>
            </div>
        </UiStickyBox>
    );
});

if (isDev) {
    SideBar.displayName = 'SideBar';
}

export default SideBar;

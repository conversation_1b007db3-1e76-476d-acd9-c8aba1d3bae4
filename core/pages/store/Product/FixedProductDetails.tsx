import Price from '@components/common/Price';
import {UiButton, UiImage, UiListBox, UiTransition} from '@core/components/ui';
import {cls, isDev} from '@core/helpers';
import {useLayout, useTrans} from '@core/hooks';
import {BagIcon, ChevronDownIcon} from '@core/icons/solid';
import {FC, Fragment, memo, useMemo} from 'react';
import useProduct from './useProduct';

const FixedProductDetails: FC = memo(() => {
    const t = useTrans();
    const {
        selectedProduct,
        inStock,
        isAddToCartInProgress,
        addToCart,
        productOptions,
        setAttribute
    } = useProduct();

    const {isSideMenuOpen} = useLayout();

    const sizeSelections = useMemo(
        () => productOptions.find(option => option.type === 'size')!,
        [productOptions]
    );

    const value = useMemo(
        () => (selectedProduct.attributes ?? {})[sizeSelections?.code],
        [selectedProduct, sizeSelections?.code]
    );

    const selectedLabel = useMemo(() => {
        const option = sizeSelections?.selections.find(
            option => option.value === value
        );

        return option?.value ?? '';
    }, [value, sizeSelections?.selections]);

    return (
        <section
            className={cls(
                'fixed bottom-0 left-0 right-0 z-10 hidden border-t bg-white py-2.5 transition-[margin] duration-200 xl:block',
                isSideMenuOpen ? 'xl:ml-64' : 'xl:ml-20'
            )}
        >
            <div className="container flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <UiImage
                        src={(selectedProduct.images as string[])[0]}
                        alt={selectedProduct.name}
                        width={45}
                        height={80}
                        className="rounded-sm"
                    />
                    <div className="flex items-center gap-4">
                        <div>
                            <p className="mb-1 font-semibold">
                                {selectedProduct.name}
                            </p>
                            <div className="flex items-center gap-2 text-center text-sm font-semibold">
                                {selectedProduct.hasDiscount &&
                                    selectedProduct.discount > 0 && (
                                        <p className="hidden rounded border-2 border-discount px-1 py-0.5 text-xs text-discount xl:block">
                                            %
                                            {Math.ceil(
                                                selectedProduct.discount
                                            )}
                                        </p>
                                    )}
                                <Price
                                    price={
                                        selectedProduct.hasDiscount
                                            ? selectedProduct.unDiscountedSalesPrice
                                            : selectedProduct.salesPrice
                                    }
                                    discountedPrice={
                                        selectedProduct.hasDiscount
                                            ? selectedProduct.salesPrice
                                            : null
                                    }
                                />
                            </div>
                        </div>

                        {typeof selectedProduct.salesPriceAtCart === 'number' &&
                            selectedProduct.salesPriceAtCart > 0 && (
                                <p className="flex items-center justify-center gap-1 rounded bg-red-50 p-1">
                                    <span className="text-xs text-discount">
                                        {t('Price on Cart')}:
                                    </span>
                                    <Price
                                        price={selectedProduct.salesPriceAtCart}
                                        className="font-semibold [&>span]:text-discount"
                                    />
                                </p>
                            )}
                    </div>
                </div>

                <div className="flex items-center">
                    {Array.isArray(sizeSelections?.selections) &&
                        sizeSelections !== undefined &&
                        sizeSelections?.selections.length > 0 && (
                            <UiListBox
                                value={value}
                                onChange={(value: string) =>
                                    setAttribute(sizeSelections?.code, value)
                                }
                                as="div"
                                className="relative space-y-1"
                                style={{minWidth: '180px'}}
                            >
                                {({open}) => (
                                    <>
                                        <UiListBox.Button
                                            className={cls(
                                                'button-primary relative flex w-10/12 items-center px-3 py-1.5 text-sm font-medium transition hover:bg-secondary-100',
                                                {
                                                    'border-primary-600 ring-1 ring-primary-600':
                                                        open
                                                }
                                            )}
                                        >
                                            <span className="truncate text-sm">
                                                {selectedLabel}
                                            </span>
                                            <span className="pointer-events-none absolute right-2 ml-3 flex items-center">
                                                <ChevronDownIcon
                                                    className={cls(
                                                        'h-3 w-3 transition',
                                                        {'-rotate-180': open}
                                                    )}
                                                />
                                            </span>
                                        </UiListBox.Button>

                                        <UiTransition
                                            show={open}
                                            as={Fragment}
                                            enter="transition duration-150 ease-in-out"
                                            enterFrom="transform scale-95 opacity-0"
                                            enterTo="transform scale-100 opacity-100"
                                            leave="transition duration-150 ease-in-out"
                                            leaveFrom="transform scale-100 opacity-100"
                                            leaveTo="transform scale-95 opacity-0"
                                        >
                                            <UiListBox.Options
                                                static
                                                className="card-container absolute bottom-12 left-0 z-40 max-h-64 w-40 origin-top-left overflow-auto rounded-lg p-1.5 outline-none"
                                            >
                                                {sizeSelections?.selections.map(
                                                    option => (
                                                        <UiListBox.Option
                                                            className="relative"
                                                            key={option.value}
                                                            value={option.value}
                                                        >
                                                            {({
                                                                active,
                                                                selected,
                                                                disabled
                                                            }) => (
                                                                <button
                                                                    disabled={
                                                                        disabled
                                                                    }
                                                                    aria-disabled={
                                                                        disabled
                                                                    }
                                                                    className={cls(
                                                                        'flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal focus:outline-none',
                                                                        active &&
                                                                            'bg-gray-100'
                                                                    )}
                                                                >
                                                                    <span
                                                                        className={cls(
                                                                            'block flex-1 truncate',
                                                                            selected
                                                                                ? 'font-medium'
                                                                                : 'font-normal'
                                                                        )}
                                                                    >
                                                                        {t(
                                                                            option.value
                                                                        )}
                                                                    </span>
                                                                    {selected && (
                                                                        <span
                                                                            className="absolute -left-1 h-6 rounded-full bg-primary-600"
                                                                            style={{
                                                                                width: 2
                                                                            }}
                                                                        ></span>
                                                                    )}
                                                                </button>
                                                            )}
                                                        </UiListBox.Option>
                                                    )
                                                )}
                                            </UiListBox.Options>
                                        </UiTransition>
                                    </>
                                )}
                            </UiListBox>
                        )}

                    <UiButton
                        className="w-40"
                        variant="solid"
                        color="primary"
                        size="md"
                        leftIcon={<BagIcon className="mr-3 h-5 w-5" />}
                        loading={isAddToCartInProgress}
                        disabled={!inStock}
                        onClick={addToCart}
                    >
                        {t('ADD TO CART')}
                    </UiButton>
                </div>
            </div>
        </section>
    );
});

if (isDev) {
    FixedProductDetails.displayName = 'FixedProductDetails';
}

export default FixedProductDetails;

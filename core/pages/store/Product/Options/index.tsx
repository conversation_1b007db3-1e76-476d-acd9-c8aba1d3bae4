import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import useProduct from '../useProduct';

import ImageOptions from './ImageOptions';
import ColorOptions from './ColorOptions';
import SizeOptions from './SizeOptions';
import OtherOptions from './OtherOptions';

const Options: FC = memo(() => {
    const {productOptions} = useProduct();

    return (
        <div className="mb-10 space-y-8 border-b pb-10">
            {productOptions.map(option => (
                <div key={option.code}>
                    {option.type === 'color' && option.showVariantImage && (
                        <ImageOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                    {option.type === 'color' && !option.showVariantImage && (
                        <ColorOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                    {option.type === 'size' && (
                        <SizeOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                    {option.type === 'other' && (
                        <OtherOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                </div>
            ))}
        </div>
    );
});

if (isDev) {
    Options.displayName = 'Options';
}

export default Options;

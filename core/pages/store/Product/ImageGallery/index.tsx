import {FC, memo, useMemo, useState} from 'react';
import {cls, isDev} from '@core/helpers';
import {useElementSize} from '@core//hooks';
import {UiImage, UiSlider} from '@core/components/ui';
import storeConfig from '~/store.config';
import useProduct from '../useProduct';
import SelectedImage from './SelectedImage';
import SwiperCore, {Navigation, Thumbs} from 'swiper';

type GalleryProps = {
    images: string[];
    productName: string;
};

const Gallery: FC<GalleryProps> = memo(({productName, images}) => {
    const [isSliding, setIsSliding] = useState(false);
    const [thumbsSwiper, setThumbsSwiper] = useState<SwiperCore>();

    const {
        ref: containerRef,
        width: containerWidth,
        height: containerHeight
    } = useElementSize();

    return (
        <>
            <div
                ref={containerRef}
                className="thumb-swiper-wrapper group relative rounded-md"
            >
                <UiSlider
                    modules={[Navigation, Thumbs]}
                    thumbs={{swiper: thumbsSwiper}}
                    className="h-full w-full"
                    navigation={{disabledClass: 'disable-slide-navigation'}}
                    watchOverflow
                    onSlideChangeTransitionStart={() => setIsSliding(true)}
                    onSlideChangeTransitionEnd={() => setIsSliding(false)}
                >
                    {images.map((image, index) => (
                        <UiSlider.Slide
                            key={image}
                            className={cls({
                                'aspect-h-3 aspect-w-2':
                                    storeConfig.catalog.productImageShape ===
                                    'rectangle',
                                'aspect-h-1 aspect-w-1':
                                    storeConfig.catalog.productImageShape !==
                                    'rectangle'
                            })}
                        >
                            <SelectedImage
                                src={image}
                                alt={productName}
                                preload={index === 0}
                                containerRef={containerRef}
                                containerWidth={containerWidth}
                                containerHeight={containerHeight}
                                isSliding={isSliding}
                                images={images}
                            />
                        </UiSlider.Slide>
                    ))}
                </UiSlider>
            </div>

            <UiSlider
                className="thumb-swiper mt-3 h-28 w-full select-none px-2"
                slidesPerView={4}
                spaceBetween={10}
                watchSlidesProgress
                threshold={2}
                modules={[Navigation, Thumbs]}
                onSwiper={setThumbsSwiper}
            >
                {images.map((image, index) => (
                    <UiSlider.Slide key={image} className="h-28 cursor-pointer">
                        <UiImage
                            className="rounded-md"
                            src={`${image}?w=180&q=50`}
                            alt={productName}
                            fit="cover"
                            position="center"
                            fill
                        />
                    </UiSlider.Slide>
                ))}
            </UiSlider>
        </>
    );
});

if (isDev) {
    Gallery.displayName = 'Gallery';
}

const ImageGallery: FC = memo(() => {
    const {selectedProduct} = useProduct();

    // Get product images.
    const images = useMemo(() => {
        let images = selectedProduct.images;

        if (!Array.isArray(images) || images.length < 1) {
            images = ['/no-image.png'];
        }

        return images;
    }, [selectedProduct.images]);

    return (
        <Gallery
            key={JSON.stringify(images)}
            images={images}
            productName={selectedProduct.name}
        />
    );
});

if (isDev) {
    ImageGallery.displayName = 'ImageGallery';
}

export default ImageGallery;

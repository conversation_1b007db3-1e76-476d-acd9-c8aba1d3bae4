import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import ConfiguratorBase from '@components/common/Configurator';
import {UiButton} from '@core/components/ui';
import {BagIcon} from '@core/icons/solid';
import useProduct from './useProduct';

const Configurator: FC = memo(() => {
    const t = useTrans();
    const {
        selectedProduct: {
            pcmPayload,
            pcmPayloadInitialValues,
            availableQuantity
        },
        setCurrentPCMPayload,
        isAddToCartInProgress,
        addToCart,
        inStock,
        setQuantity
    } = useProduct();

    return (
        <div>
            <ConfiguratorBase
                initialValues={pcmPayloadInitialValues}
                payload={pcmPayload}
                availableQuantity={availableQuantity}
                AddToCartButton={
                    <UiButton
                        variant="solid"
                        color="primary"
                        leftIcon={<BagIcon className="mr-2 h-4 w-4" />}
                        loading={isAddToCartInProgress}
                        disabled={!inStock}
                        onClick={addToCart}
                    >
                        {t('ADD TO CART')}
                    </UiButton>
                }
                onChange={payload => {
                    setCurrentPCMPayload(payload);
                    setQuantity(payload.quantity);
                }}
            />
        </div>
    );
});

if (isDev) {
    Configurator.displayName = 'Configurator';
}

export default Configurator;

import {memo, useEffect, useMemo, useState} from 'react';
import {isDev, jsonRequest, pushIntoGTMDataLayer} from '@core/helpers';
import {useMobile, useStore} from '@core/hooks';
import {Campaign, Page, Product, ProductListItem} from '@core/types';

import {ProductProvider} from './context';
import Meta from './Meta';
import ImageGallery from './ImageGallery';
import Info from './Info';
import Options from './Options';
import Actions from './Actions';
import Stats from './Stats';
import SideBar from './SideBar';
import Tabs from './Tabs';
import ProductReviews from './ProductReviews';
import ProductInformation from './ProductInformation';
import RelatedProducts from './RelatedProducts';
import MobileDetail from './MobileDetail';
import Configurator from './Configurator';
import WarehouseSelection from './WarehouseSelection';
import AlternateProducts from './AlternateProducts';

type ProductPageProps = {
    campaigns: Campaign[];
    product: Product;
    selectedAttributes?: Record<string, any>;
    relatedProducts: ProductListItem[];
};

const ProductPage: Page<ProductPageProps> = memo(props => {
    const {
        campaigns: initialCampaigns,
        product: initialProduct,
        selectedAttributes,
        relatedProducts
    } = props;
    const {isMobile} = useMobile();
    const {currency} = useStore();

    const [currentCampaigns, setCurrentCampaigns] = useState<Campaign[] | null>(
        null
    );
    const [currentProduct, setCurrentProduct] = useState<Product | null>(null);
    const campaigns = useMemo(() => {
        if (
            typeof Array.isArray(currentCampaigns) &&
            currentCampaigns !== null
        ) {
            return currentCampaigns;
        }

        return initialCampaigns;
    }, [initialCampaigns, currentCampaigns]);
    const product = useMemo(() => {
        if (typeof currentProduct === 'object' && currentProduct !== null) {
            return currentProduct;
        }

        return initialProduct;
    }, [initialProduct, currentProduct]);
    useEffect(() => {
        (async () => {
            const result = await jsonRequest({
                url: '/api/catalog/product',
                method: 'POST',
                data: {
                    slug: initialProduct.slug
                }
            });

            setCurrentCampaigns(result.campaigns);
            setCurrentProduct(result.product);
        })();
        // eslint-disable-next-line
    }, []);

    useEffect(() => {
        // ---------- Google Tag Manager ----------
        pushIntoGTMDataLayer({
            event: 'view_item',
            data: {
                currency: currency.name === 'TL' ? 'TRY' : currency.name,
                value: product.unDiscountedSalesPrice
                    ? product.unDiscountedSalesPrice
                    : product.salesPrice,
                items: [
                    {
                        item_id: product.code,
                        item_name: product.name,
                        discount:
                            product.unDiscountedSalesPrice > 0
                                ? product.unDiscountedSalesPrice -
                                  product.salesPrice
                                : 0,
                        item_brand: product.brandName,
                        item_category: product.categoryName,
                        price: product.unDiscountedSalesPrice
                            ? product.unDiscountedSalesPrice
                            : product.salesPrice
                    }
                ]
            }
        });
        // ----------------------------------------
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    return (
        <ProductProvider
            product={product}
            selectedAttributes={selectedAttributes}
            relatedProducts={relatedProducts}
            campaigns={campaigns}
        >
            <Meta />

            <div className="relative hidden xl:block">
                {product.isPCMProduct && !isMobile ? (
                    <Configurator />
                ) : (
                    <div className="flex">
                        <div className="flex-1 pt-4 xl:pt-0">
                            <div className="xl:card-container grid grid-cols-1 xl:grid-cols-12 xl:gap-8 xl:p-10">
                                <div className="xl:col-span-6">
                                    <ImageGallery />
                                </div>

                                <div className="mt-8 xl:col-span-6 xl:mt-0">
                                    <Info />

                                    {product.isConfigurable &&
                                        (product.variants ?? []).length > 0 && (
                                            <Options />
                                        )}

                                    {(product.warehouseStocks ?? []).length >
                                        0 && <WarehouseSelection />}

                                    <Actions />

                                    <Stats />
                                </div>
                            </div>
                        </div>

                        <div className="ml-8 hidden w-60 xl:block">
                            <SideBar />
                        </div>
                    </div>
                )}

                <Tabs />
                <ProductInformation />
                <ProductReviews />
                <RelatedProducts />
                {product.alternateProducts &&
                    product.alternateProducts.length > 0 && (
                        <AlternateProducts
                            alternateProducts={
                                product.alternateProducts as Product[]
                            }
                        />
                    )}
            </div>

            {!product.isPCMProduct && (
                <div className="fixed inset-0 z-50 block bg-white xl:hidden">
                    {isMobile && <MobileDetail />}
                </div>
            )}
            {!!product.isPCMProduct && (
                <div className="fixed inset-0 z-50 block overflow-y-auto bg-white xl:hidden">
                    {isMobile && <Configurator />}
                </div>
            )}
        </ProductProvider>
    );
});

if (isDev) {
    ProductPage.displayName = 'ProductPage';
}

export default ProductPage;

import {FC, memo} from 'react';
import {SelectedProduct} from '@core/types';
import {isDev} from '@core/helpers';

type ProductFeaturesProps = {
    selectedProduct: SelectedProduct;
};

const ProductFeatures: FC<ProductFeaturesProps> = memo(({selectedProduct}) => {
    return (
        <div className="p-4">
            <div className="-my-4 divide-y">
                {selectedProduct.features.map(feature => (
                    <div
                        key={feature.code}
                        className="flex justify-between p-4"
                    >
                        <span className="font-medium">{feature.label}</span>
                        <span className="text-muted">{feature.value}</span>
                    </div>
                ))}
            </div>
        </div>
    );
});

if (isDev) {
    ProductFeatures.displayName = 'ProductFeatures';
}

export default ProductFeatures;

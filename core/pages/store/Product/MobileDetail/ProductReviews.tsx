import {FC, memo, useCallback, useState} from 'react';
import {SelectedProduct} from '@core/types';
import {isDev, jsonRequest} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiAvatar, UiButton, UiRating, UiSpinner} from '@core/components/ui';
import {MessageLinesIcon} from '@core/icons/regular';

type ProductReviewsProps = {
    selectedProduct: SelectedProduct;
};

const ProductReviews: FC<ProductReviewsProps> = memo(({selectedProduct}) => {
    const t = useTrans();
    const {createReview, locale} = useStore();
    const [reviews, setReviews] = useState(() =>
        (selectedProduct.reviews ?? []).slice(0, 4).map(review => {
            const formatter = new Intl.DateTimeFormat(locale, {
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            });
            const date = new Date(Date.parse(review.createdAt));

            review.dateFornatted = formatter.format(date);

            return review;
        })
    );
    const [skip, setSkip] = useState(4);
    const [isLoading, setIsLoading] = useState(false);

    const onReviewCreate = useCallback(async () => {
        if (isLoading) return;

        setIsLoading(true);

        await createReview({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        setIsLoading(false);
    }, [
        createReview,
        isLoading,
        selectedProduct.images,
        selectedProduct.name,
        selectedProduct.productId,
        selectedProduct.salesPrice
    ]);

    const onLoadMore = useCallback(async () => {
        if (isLoading) {
            return;
        }

        setIsLoading(true);

        try {
            const newReviews = await jsonRequest({
                url: '/api/catalog/product-reviews',
                method: 'POST',
                data: {
                    locale,
                    productId: selectedProduct.productId,
                    skip,
                    limit: 4
                }
            });
            setSkip(currentSkip => currentSkip + 4);
            setReviews(currentReviews => [
                ...currentReviews,
                ...newReviews.map((review: any) => {
                    const formatter = new Intl.DateTimeFormat(locale, {
                        day: 'numeric',
                        month: 'long',
                        year: 'numeric'
                    });
                    const date = new Date(Date.parse(review.createdAt));

                    review.dateFornatted = formatter.format(date);

                    return review;
                })
            ]);
        } catch (error: any) {
            console.log(error);
        }

        setIsLoading(false);
    }, [isLoading, locale, selectedProduct.productId, skip]);

    return (
        <div className="flex h-full w-full flex-col overflow-hidden">
            <div className="flex-1 overflow-y-auto p-4">
                {reviews.length > 0 && (
                    <>
                        <div className="flex items-center">
                            <div className="flex items-center">
                                <div className="h-5">
                                    <UiRating
                                        initialRating={selectedProduct.rating}
                                        size="sm"
                                        readonly
                                    />
                                </div>
                            </div>
                            <div className="ml-2.5 mt-0.5 text-sm">
                                {t('Based on {count} reviews', {
                                    count: selectedProduct.reviewCount
                                })}
                            </div>
                        </div>

                        <div className="mt-4 flow-root border-t pt-6">
                            <div className="-my-6 divide-y">
                                {reviews.map((review: any) => (
                                    <div key={review.id} className="py-6">
                                        <div className="flex items-center">
                                            <UiAvatar
                                                className="h-10 w-10 bg-primary-600 text-white"
                                                name={review.author}
                                                size="lg"
                                            />
                                            <div className="ml-4 flex-1">
                                                <div className="flex items-center justify-between">
                                                    <h4 className="text-sm font-semibold text-gray-900">
                                                        {review.author}
                                                    </h4>

                                                    <div className="text-xs text-muted">
                                                        {review.dateFornatted}
                                                    </div>
                                                </div>
                                                <div className="mt-1 flex items-center">
                                                    <UiRating
                                                        initialRating={
                                                            review.rating
                                                        }
                                                        readonly
                                                    />
                                                </div>
                                            </div>
                                        </div>

                                        {!!review.hasContent && (
                                            <div
                                                className="mt-4 space-y-3 text-base text-muted"
                                                dangerouslySetInnerHTML={{
                                                    __html: review.content
                                                }}
                                            />
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </>
                )}

                {selectedProduct.reviewCount > reviews.length && (
                    <div className="mb-4 mt-6 flex items-center justify-center">
                        {isLoading ? (
                            <div className="flex items-center justify-center text-muted">
                                <UiSpinner className="mr-2" size="sm" />
                                <div>{t('Loading reviews..')}</div>
                            </div>
                        ) : (
                            <UiButton
                                className="px-8"
                                variant="outline"
                                onClick={onLoadMore}
                            >
                                {t('Load More Reviews')}
                            </UiButton>
                        )}
                    </div>
                )}

                {reviews.length < 1 && !isLoading && (
                    <div className="flex h-full flex-col items-center justify-center">
                        <div className="flex h-24 w-24 items-center justify-center rounded-md border border-dashed border-gray-500 text-muted">
                            <MessageLinesIcon className="h-8 w-8" />
                        </div>

                        <h3 className="pt-8 text-center text-xl font-medium">
                            {t('No reviews found!')}
                        </h3>

                        <p className="px-10 pt-2 text-center text-muted">
                            {t(
                                'No reviews were found for this product. Would you like to write the first review?'
                            )}
                        </p>
                    </div>
                )}
            </div>

            <div className="border-t p-4">
                <UiButton
                    variant="solid"
                    color="primary"
                    className="w-full"
                    onClick={onReviewCreate}
                >
                    {t('Write a Review')}
                </UiButton>
            </div>
        </div>
    );
});

if (isDev) {
    ProductReviews.displayName = 'ProductReviews';
}

export default ProductReviews;

import {FC, memo, useMemo, useState} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiImage, UiImageProps, UiSlider, UiSpinner} from '@core/components/ui';
import {Autoplay} from '@core/components/ui/Slider';
import {XIcon} from '@core/icons/solid';
import storeConfig from '~/store.config';
import useProduct from '../useProduct';
import FavoriteAction from './Actions/FavoriteAction';

type ImageWithLoadingProps = UiImageProps & {
    withSpinner?: boolean;
};

const ImageWithLoading: FC<ImageWithLoadingProps> = memo(
    ({withSpinner = false, ...rest}) => {
        const [isImageLoading, setIsImageLoading] = useState(true);

        return (
            <>
                {isImageLoading && (
                    <div className="absolute inset-0 z-10 h-full w-full">
                        {withSpinner ? (
                            <div className="flex h-full w-full items-center justify-center bg-black text-white">
                                <UiSpinner size="xl" />
                            </div>
                        ) : (
                            <div className="skeleton-card h-full w-full" />
                        )}
                    </div>
                )}

                <UiImage
                    {...rest}
                    onLoadingComplete={() => setIsImageLoading(false)}
                />
            </>
        );
    }
);

if (isDev) {
    ImageWithLoading.displayName = 'ImageWithLoading';
}

type ZoomedImageGalleryProps = {
    images: string[];
    productName: string;
    onClose: () => void;
};

const ZoomedImageGallery: FC<ZoomedImageGalleryProps> = memo(
    ({images, productName, onClose}) => {
        const [currentSlideIndex, setCurrentSlideIndex] = useState(1);

        return (
            <div className="fixed inset-0 z-[51] flex items-center bg-black text-white">
                <UiSlider
                    className="flex-1"
                    modules={[Autoplay]}
                    autoplay={{
                        delay: 5000
                    }}
                    loop
                    spaceBetween={4}
                    slidesPerView={1}
                    slidesPerGroup={1}
                    onSlideChange={swiper =>
                        setCurrentSlideIndex(swiper.realIndex + 1)
                    }
                >
                    {images.map((image, index) => (
                        <UiSlider.Slide key={image + index}>
                            <div
                                className={cls('', {
                                    'aspect-h-3 aspect-w-2':
                                        storeConfig.catalog
                                            .productImageShape === 'rectangle',
                                    'aspect-h-1 aspect-w-1':
                                        storeConfig.catalog
                                            .productImageShape !== 'rectangle'
                                })}
                            >
                                <div>
                                    <ImageWithLoading
                                        withSpinner
                                        src={image}
                                        alt={productName}
                                        priority={index === 0}
                                        fill
                                        fit="cover"
                                        position="center"
                                    />
                                </div>
                            </div>
                        </UiSlider.Slide>
                    ))}
                </UiSlider>

                <button
                    className="
                    absolute right-2 top-2 z-10 flex h-10 w-10 items-center justify-center
                    rounded-full bg-blackAlpha-600 text-white active:opacity-30
                    "
                    onClick={onClose}
                >
                    <XIcon className="h-6 w-6" />
                </button>

                <div className="absolute bottom-8 left-1/2 z-10 -translate-x-1/2 rounded-full bg-blackAlpha-600 px-3 py-1 text-sm text-white">
                    {currentSlideIndex} / {images.length}
                </div>
            </div>
        );
    }
);

if (isDev) {
    ZoomedImageGallery.displayName = 'ZoomedImageGallery';
}

const ImageGallery: FC = memo(() => {
    const {selectedProduct} = useProduct();
    const [currentSlideIndex, setCurrentSlideIndex] = useState(1);
    const [isZoomShown, setIsZoomShown] = useState(false);

    // Get product images.
    const images = useMemo(() => {
        let images = selectedProduct.images;

        if (!Array.isArray(images) || images.length < 1) {
            images = ['/no-image.png'];
        }

        return images;
    }, [selectedProduct.images]);

    return (
        <>
            <div className="relative">
                <div
                    className="
                    absolute left-0 top-0 z-[5] h-mobile-header w-full
                    bg-gradient-to-b from-gray-900 to-transparent opacity-40
                    "
                />

                <UiSlider
                    modules={[Autoplay]}
                    autoplay={{
                        delay: 5000
                    }}
                    loop
                    spaceBetween={4}
                    slidesPerView={1}
                    slidesPerGroup={1}
                    onSlideChange={swiper =>
                        setCurrentSlideIndex(swiper.realIndex + 1)
                    }
                >
                    {images.map((image, index) => (
                        <UiSlider.Slide key={image + index}>
                            <div
                                className={cls('', {
                                    'aspect-h-3 aspect-w-2':
                                        storeConfig.catalog
                                            .productImageShape === 'rectangle',
                                    'aspect-h-1 aspect-w-1':
                                        storeConfig.catalog
                                            .productImageShape !== 'rectangle'
                                })}
                            >
                                <div>
                                    <ImageWithLoading
                                        src={`${image}?w=720&q=75`}
                                        alt={selectedProduct.name}
                                        priority={index === 0}
                                        fill
                                        fit="cover"
                                        position="center"
                                        onClick={() => setIsZoomShown(true)}
                                    />
                                </div>
                            </div>
                        </UiSlider.Slide>
                    ))}
                </UiSlider>

                <div className="absolute bottom-4 left-4 z-10 rounded-full bg-whiteAlpha-700 px-3 py-1 text-xs shadow-sm">
                    {currentSlideIndex} / {images.length}
                </div>

                <div className="absolute bottom-4 right-4 z-10 flex items-center space-x-2 rounded-full bg-whiteAlpha-700 pr-3 text-xs shadow-sm">
                    <FavoriteAction />
                    <div>{selectedProduct.favoritesCount}</div>
                </div>
            </div>

            {isZoomShown && (
                <ZoomedImageGallery
                    images={images}
                    productName={selectedProduct.name}
                    onClose={() => setIsZoomShown(false)}
                />
            )}
        </>
    );
});

if (isDev) {
    ImageGallery.displayName = 'ImageGallery';
}

export default ImageGallery;

import {FC, memo, useEffect, useState} from 'react';
import {useRouter} from 'next/router';
import {cls, isDev} from '@core/helpers';
import {useCart, useMobile} from '@core/hooks';
import {UiLink} from '@core/components/ui';
import {BagIcon, SearchIcon, StoreIcon} from '@core/icons/regular';
import {ArrowLeftIcon} from '@core/icons/solid';
import AlarmAction from './AlarmAction';
import CollectionAction from './CollectionAction';

const Actions: FC = memo(() => {
    const router = useRouter();
    const {setIsMobileSearchShown} = useMobile();
    const {itemCount: cartItemsCount} = useCart();

    const [scrollPast, setScrollPast] = useState(false);
    useEffect(() => {
        const container = document.querySelector(
            '.mobile-product-content-wrapper'
        );
        const actionsContainer = document.querySelector(
            '.mobile-product-actions'
        );

        const onScroll = (e: Event) => {
            try {
                // @ts-ignore
                if (e.target.scrollTop > actionsContainer.clientHeight) {
                    if (!scrollPast) {
                        setScrollPast(true);
                    }
                } else {
                    if (scrollPast) {
                        setScrollPast(false);
                    }
                }
            } catch (e: any) {}
        };

        if (container !== null && actionsContainer !== null) {
            container.addEventListener('scroll', onScroll);
        }

        return () => {
            if (container !== null) {
                container.removeEventListener('scroll', onScroll);
            }
        };
    }, [scrollPast]);

    return (
        <div
            className={cls(
                'mobile-product-actions fixed left-0 top-0 flex w-full flex-row items-center justify-between',
                'z-20 select-none p-4 transition',
                {
                    'border-b bg-white shadow-sm': scrollPast
                }
            )}
        >
            <div className="flex items-center space-x-3">
                <button
                    className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-100 shadow transition active:opacity-30"
                    onClick={() => router.back()}
                >
                    <ArrowLeftIcon className="h-3.5 w-3.5" />
                </button>

                <UiLink
                    className="relative flex h-9 w-9 items-center justify-center rounded-full bg-gray-100 shadow transition active:opacity-30"
                    href="/"
                >
                    <StoreIcon className="h-3.5 w-3.5" />
                </UiLink>
            </div>

            <div className="flex flex-row space-x-3">
                {scrollPast && (
                    <>
                        <AlarmAction />
                        <CollectionAction />
                    </>
                )}

                <button
                    className="relative flex h-9 w-9 items-center justify-center rounded-full bg-gray-100 shadow transition duration-150 ease-in-out active:opacity-30"
                    onClick={() =>
                        router.push(`/mobile/my-cart?t=${Date.now()}`)
                    }
                >
                    <span className="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary-600 text-[10px] text-white">
                        {cartItemsCount}
                    </span>
                    <BagIcon className="h-3.5 w-3.5" />
                </button>

                <button
                    className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-100 shadow transition active:opacity-30"
                    onClick={() => setIsMobileSearchShown(true)}
                >
                    <SearchIcon className="h-3.5 w-3.5" />
                </button>
            </div>
        </div>
    );
});

if (isDev) {
    Actions.displayName = 'Actions';
}

export default Actions;

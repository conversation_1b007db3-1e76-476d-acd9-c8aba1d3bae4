import {FC, memo, useCallback, useState} from 'react';
import {useStore} from '@core/hooks';
import {UiSpinner} from '@core/components/ui';
import {BookmarkIcon} from '@core/icons/regular';
import {BookmarkIcon as BookmarkSolidIcon} from '@core/icons/solid';
import useProduct from '../../useProduct';

const CollectionAction: FC = memo(() => {
    const {updateProductCollections} = useStore();
    const {selectedProduct, customerProductParams, setCustomerProductParams} =
        useProduct();

    const [isCollectionUpdateInProgress, setIsCollectionUpdateInProgress] =
        useState(false);
    const onAddToCollection = useCallback(async () => {
        if (isCollectionUpdateInProgress) {
            return;
        }

        setIsCollectionUpdateInProgress(true);

        const result = await updateProductCollections({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        setCustomerProductParams({
            ...customerProductParams,
            isInCollection: result.length > 0,
            collectionIds: result
        });

        setIsCollectionUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isCollectionUpdateInProgress,
        updateProductCollections,
        selectedProduct
    ]);

    return !isCollectionUpdateInProgress ? (
        <button
            className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-100 shadow transition active:opacity-30"
            onClick={onAddToCollection}
        >
            {customerProductParams.isInCollection ? (
                <BookmarkSolidIcon className="h-3.5 w-3.5 text-primary-600" />
            ) : (
                <BookmarkIcon className="h-3.5 w-3.5" />
            )}
        </button>
    ) : (
        <div className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-100 shadow">
            <UiSpinner size="sm" />
        </div>
    );
});

CollectionAction.displayName = 'CollectionAction';

export default CollectionAction;

import {FC, memo, useCallback} from 'react';
import {isDev} from '@core/helpers';
import {useTrans, useUI} from '@core/hooks';
import {ChevronUpIcon} from '@core/icons/solid';
import storeConfig from '~/store.config';
import useProduct from '../useProduct';

import ImageGallery from './ImageGallery';
import Actions from './Actions';
import Info from './Info';
import Options from './Options';
import ProductInformation from './ProductInformation';
import ProductFeatures from './ProductFeatures';
import ProductReviews from './ProductReviews';
import RelatedProducts from './RelatedProducts';
import Summary from './Summary';
import ProductCampaigns from '../ProductDetails/ProductCampaigns';
import ShipmentOptions from '../ProductDetails/ShipmentOptions';

const MobileDetail: FC = memo(() => {
    const t = useTrans();
    const {openSideBar} = useUI();
    const {product, selectedProduct, relatedProducts} = useProduct();

    const onOpenProductInformation = useCallback(() => {
        openSideBar(
            t('Product Information'),
            <ProductInformation selectedProduct={selectedProduct} />
        );
    }, [selectedProduct, t, openSideBar]);
    const onOpenProductFeatures = useCallback(() => {
        openSideBar(
            t('Product Features'),
            <ProductFeatures selectedProduct={selectedProduct} />
        );
    }, [selectedProduct, t, openSideBar]);
    const onOpenProductReviews = useCallback(() => {
        openSideBar(
            t('Product Reviews'),
            <ProductReviews selectedProduct={selectedProduct} />
        );
    }, [selectedProduct, t, openSideBar]);

    return (
        <div
            className="mobile-product-content-wrapper relative h-full w-full overflow-x-auto overflow-y-auto"
            style={{
                paddingBottom: `${storeConfig.theme.mobileTabBarHeight}`
            }}
        >
            <Actions />

            <ImageGallery />

            <Info onOpenReviews={onOpenProductReviews} />

            {product.isConfigurable && (product.variants ?? []).length > 0 && (
                <Options />
            )}

            <div className="-mt-6 mb-6 divide-y border-b">
                <button
                    className="flex w-full items-center justify-between bg-white p-4 text-left"
                    onClick={onOpenProductInformation}
                >
                    <span className="font-medium">
                        {t('Product Information')}
                    </span>

                    <span className="ml-6 flex items-center text-muted">
                        <ChevronUpIcon className="h-4 w-4" aria-hidden="true" />
                    </span>
                </button>

                {selectedProduct.features.length > 0 && (
                    <button
                        className="flex w-full items-center justify-between bg-white p-4 text-left"
                        onClick={onOpenProductFeatures}
                    >
                        <span className="font-medium">
                            {t('Product Features')}
                        </span>

                        <span className="ml-6 flex items-center text-muted">
                            <ChevronUpIcon
                                className="h-4 w-4"
                                aria-hidden="true"
                            />
                        </span>
                    </button>
                )}

                <button
                    className="flex w-full items-center justify-between bg-white p-4 text-left"
                    onClick={onOpenProductReviews}
                >
                    <span className="font-medium">{t('Product Reviews')}</span>

                    <span className="ml-6 flex items-center text-muted">
                        <ChevronUpIcon className="h-4 w-4" aria-hidden="true" />
                    </span>
                </button>
            </div>

            <ProductCampaigns isMobile />

            <ShipmentOptions isMobile />

            {relatedProducts.length > 0 && <RelatedProducts />}

            <Summary />
        </div>
    );
});

if (isDev) {
    MobileDetail.displayName = 'MobileDetail';
}

export default MobileDetail;

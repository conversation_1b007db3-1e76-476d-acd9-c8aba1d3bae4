import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {useScrollIntoView, useTrans} from '@core/hooks';
import Price from '@components/common/Price';
import {UiLink, UiRating} from '@core/components/ui';
import useProduct from './useProduct';

const Info: FC = memo(() => {
    const t = useTrans();
    const {selectedProduct} = useProduct();
    const {scrollIntoView: scrollToProductReviews} =
        useScrollIntoView<HTMLDivElement>({
            target: 'productReviews',
            offset: 108
        });
    const title = useMemo(() => {
        let title = '';

        if (!!selectedProduct.colorAttributeValue) {
            title = `${selectedProduct.colorAttributeValue} ${selectedProduct.name} ${selectedProduct.code}`;
        } else {
            title = `${selectedProduct.name} ${selectedProduct.code}`;
        }

        return title;
    }, [selectedProduct]);

    return (
        <div className="mb-6 border-b pb-6">
            {selectedProduct.brandName ? (
                <h1 className="-mt-0.5 text-2xl font-medium">
                    <UiLink
                        className="mr-1.5 inline hover:text-primary-600 hover:underline"
                        href={`/${selectedProduct.brandSlug}`}
                    >
                        {selectedProduct.brandName}
                    </UiLink>
                    <span className="inline">{title}</span>
                </h1>
            ) : (
                <h1 className="-mt-0.5 text-2xl font-medium">{title}</h1>
            )}

            <div className="mt-4 flex items-center space-x-3">
                <div className="flex items-center">
                    <UiRating
                        size="sm"
                        initialRating={selectedProduct.rating}
                        readonly
                    />
                </div>

                <button
                    className="flex cursor-pointer items-center text-xs text-muted transition hover:text-primary-600 hover:underline"
                    onClick={() => scrollToProductReviews()}
                >
                    {t('{reviewCount} reviews', {
                        reviewCount: selectedProduct.reviewCount
                    })}
                </button>
            </div>

            <div className="mt-6 flex items-center gap-3 font-medium">
                {selectedProduct.hasDiscount &&
                    selectedProduct.discount > 0 && (
                        <p className="rounded border-2 border-discount px-1.5 py-1 text-discount">
                            %{Math.ceil(selectedProduct.discount)}
                        </p>
                    )}
                <Price
                    price={
                        selectedProduct.hasDiscount
                            ? selectedProduct.unDiscountedSalesPrice
                            : selectedProduct.salesPrice
                    }
                    discountedPrice={
                        selectedProduct.hasDiscount
                            ? selectedProduct.salesPrice
                            : null
                    }
                    dontWrapDiscountedPrice
                    className="text-xl"
                />
            </div>

            {typeof selectedProduct.salesPriceAtCart === 'number' &&
                selectedProduct.salesPriceAtCart > 0 && (
                    <div className="mt-5 flex w-fit flex-col items-center gap-1 rounded border-2 border-discount p-1.5 font-medium">
                        <div className="flex items-center gap-2 rounded bg-red-100 px-12 py-1 text-sm text-discount">
                            <svg
                                className="h-[10px] w-4 stroke-discount stroke-[1px] [&>path]:fill-discount"
                                viewBox="0 0 16 10"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path d="M10.278 9.469h4.607a.526.526 0 0 0 .218-.044.621.621 0 0 0 .37-.577v-5.83h-1.17v4.297L9.86 2.438a.55.55 0 0 0-.807-.024L5.97 5.504 1.47.681l-.822.884L5.53 6.809a.55.55 0 0 0 .81.02l3.108-3.085 4.074 4.479h-3.244v1.246Z" />
                            </svg>
                            <p>{t('Price on Cart')}</p>
                        </div>
                        <Price
                            price={selectedProduct.salesPriceAtCart}
                            dontWrapDiscountedPrice
                            className="text-xl [&>span]:text-discount"
                        />
                    </div>
                )}
        </div>
    );
});

if (isDev) {
    Info.displayName = 'Info';
}

export default Info;

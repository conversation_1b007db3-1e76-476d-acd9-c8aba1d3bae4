import {FC, memo, useMemo} from 'react';
import {isDev, cls} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {TruckFastIcon} from '@core/icons/solid';
import useProduct from '../useProduct';

type ShipmentOptionsProps = {
    isMobile?: boolean;
};

const ShipmentOptions: FC<ShipmentOptionsProps> = memo(({isMobile = false}) => {
    const t = useTrans();
    const {locale} = useStore();
    const {
        product: {estimatedDeliveryDuration}
    } = useProduct();

    const getEstimatedShippingDate = useMemo(() => {
        const currentDate = new Date();
        currentDate.setDate(
            currentDate.getDate() + (estimatedDeliveryDuration ?? 2)
        );

        const formatter = new Intl.DateTimeFormat(locale, {
            day: 'numeric',
            month: 'long'
        });
        return formatter.format(currentDate);
    }, [locale, estimatedDeliveryDuration]);

    return typeof estimatedDeliveryDuration === 'number' ? (
        <div
            className={cls({
                'card-container': !isMobile
            })}
        >
            <p
                className={cls(
                    'border-b text-xs font-semibold',
                    {'mt-3 px-4 pb-2': isMobile},
                    {'p-4': !isMobile}
                )}
            >
                {t('SHIPPING OPTIONS')}
            </p>

            <div
                className={cls('space-y-2 text-xs', {
                    'px-4 py-2': isMobile,
                    'px-2 py-3': !isMobile
                })}
            >
                {estimatedDeliveryDuration === 1 && (
                    <div
                        className={cls(
                            'flex items-center gap-3 rounded-md bg-green-50 p-2',
                            {'border border-green-600': isMobile}
                        )}
                    >
                        <TruckFastIcon className="h-6 w-6 text-green-600" />
                        <p className="w-full text-green-600">
                            {t('If you order now, we will ship it tomorrow!')}
                        </p>
                    </div>
                )}
                {estimatedDeliveryDuration >= 2 && (
                    <div
                        className={cls(
                            'flex items-center gap-3 rounded-md p-2',
                            {
                                'border border-primary-600 bg-primary-50':
                                    isMobile
                            }
                        )}
                    >
                        <TruckFastIcon className="h-6 w-6 text-primary-600" />
                        <p
                            dangerouslySetInnerHTML={{
                                __html: t(
                                    'We will ship it on {getEstimatedShippingDate}!',
                                    {getEstimatedShippingDate}
                                )
                            }}
                        />
                    </div>
                )}
            </div>
        </div>
    ) : null;
});

if (isDev) {
    ShipmentOptions.displayName = 'ShipmentOptions';
}

export default ShipmentOptions;

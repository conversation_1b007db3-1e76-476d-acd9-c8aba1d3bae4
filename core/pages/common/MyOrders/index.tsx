import {useState} from 'react';
import {
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable
} from '@tanstack/react-table';
import {
    UiTable,
    UiTableBody,
    UiTableCell,
    UiTableHead,
    UiTableHeader,
    UiTableRow
} from '@core/components/ui';
import {
    SkeletonRows,
    EmptyTableRow,
    ColumnToggle,
    SearchFilter,
    PaginationButtons,
    DatePicker
} from '@components/common/DataTable';
import Seo from '@components/common/Seo';
import {OrdersIcon} from '@core/icons/solid';
import {useTrans} from '@core/hooks';
import useColumns from './useColumns';
import useOrders from './useOrders';

const MyOrders = () => {
    const {orders, isLoading} = useOrders();

    const columns = useColumns();

    const t = useTrans();

    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
        projectCode: false,
        projectName: false,
        documentNo: false,
        salespersonName: false
    });
    const [globalFilter, setGlobalFilter] = useState('');

    const table = useReactTable({
        data: orders,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onGlobalFilterChange: setGlobalFilter,
        state: {
            columnVisibility,
            globalFilter
        },
        initialState: {
            pagination: {
                pageSize: 10
            }
        }
    });

    return (
        <>
            <Seo title={t('My Orders')} />

            <p className="mb-4 hidden text-xl font-medium xl:block">
                {t('My Orders')}
            </p>

            <div className="card-container mb-4 p-3 max-md:space-y-2 md:flex md:items-center md:justify-end md:gap-3">
                <div className="mr-auto flex items-center justify-between gap-3">
                    <DatePicker
                        filteredColumn={table.getColumn('orderDate')}
                        className="max-md:w-full"
                    />
                    <ColumnToggle
                        className="md:hidden"
                        tableColumns={table.getAllColumns()}
                    />
                </div>
                <div className="flex items-center gap-3">
                    <SearchFilter
                        onChange={e => table.setGlobalFilter(e.target.value)}
                        value={table.getState().globalFilter}
                    />
                    <ColumnToggle
                        className="hidden md:inline-block"
                        tableColumns={table.getAllColumns()}
                    />
                    <PaginationButtons
                        hasNextPage={table.getCanNextPage()}
                        hasPreviousPage={table.getCanPreviousPage()}
                        nextPage={table.nextPage}
                        pageCount={table.getPageCount()}
                        pageIndex={table.getState().pagination.pageIndex}
                        previousPage={table.previousPage}
                        className="hidden md:flex"
                    />
                </div>
            </div>

            <div className="cursor-default overflow-hidden rounded-lg border bg-white">
                <UiTable>
                    <UiTableHeader>
                        {table.getHeaderGroups().map(headerGroup => (
                            <UiTableRow key={headerGroup.id}>
                                {headerGroup.headers.map(header => (
                                    <UiTableHead
                                        key={header.id}
                                        className="whitespace-nowrap bg-gray-50 text-xs font-semibold text-gray-700"
                                    >
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                  header.column.columnDef
                                                      .header,
                                                  header.getContext()
                                              )}
                                    </UiTableHead>
                                ))}
                            </UiTableRow>
                        ))}
                    </UiTableHeader>
                    <UiTableBody>
                        {isLoading ? (
                            <SkeletonRows
                                rowSize={table.getAllColumns().length}
                            />
                        ) : table.getRowModel().rows?.length > 0 ? (
                            table.getRowModel().rows.map(row => (
                                <UiTableRow
                                    key={row.id}
                                    className="transition hover:bg-secondary-100"
                                >
                                    {row.getVisibleCells().map(cell => (
                                        <UiTableCell key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </UiTableCell>
                                    ))}
                                </UiTableRow>
                            ))
                        ) : (
                            <EmptyTableRow
                                colSize={table.getAllColumns().length}
                                icon={<OrdersIcon className="h-8 w-8" />}
                                title="No orders found!"
                                description="There are no orders in your account. After shopping on our site, you can access the order details from this page."
                            />
                        )}
                    </UiTableBody>
                </UiTable>
            </div>

            <PaginationButtons
                hasNextPage={table.getCanNextPage()}
                hasPreviousPage={table.getCanPreviousPage()}
                nextPage={table.nextPage}
                pageCount={table.getPageCount()}
                pageIndex={table.getState().pagination.pageIndex}
                previousPage={table.previousPage}
                className="card-container ml-auto mt-4 w-fit justify-end p-2 md:hidden"
            />
        </>
    );
};

export default MyOrders;

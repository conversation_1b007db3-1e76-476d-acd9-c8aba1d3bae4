import {jsonRequest} from '@core/helpers';
import {useEffect, useState} from 'react';
import {Order} from './types';

const useOrders = () => {
    const [orders, setOrders] = useState<Order[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        (async () => {
            try {
                const ordersData = await jsonRequest({
                    url: '/api/customers/all-orders',
                    method: 'POST',
                    data: {
                        limit: 1000
                    }
                });

                setOrders(ordersData);
            } catch (err) {
                console.error(err);
            } finally {
                setIsLoading(false);
            }
        })();
    }, []);

    return {orders, isLoading};
};

export default useOrders;

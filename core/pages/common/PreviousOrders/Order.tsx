import {FC, memo, useCallback, useMemo, useState} from 'react';
import {cls, isDev, jsonRequest} from '@core/helpers';
import {
    CartItem,
    DeliveryOption,
    Order as OrderType,
    PaymentMethod
} from '@core/types';
import {useStore, useTrans, useUI} from '@core/hooks';
import {UiImage, UiLink} from '@core/components/ui';
import {BanIcon} from '@core/icons/regular';
import Price from '@components/common/Price';
import storeConfig from '~/store.config';
import {ReturnIcon, TruckFastIcon} from '@core/icons/solid';
import ShipmentDetail from './ShipmentDetail';
import ReturnForm from './ReturnForm';
import GoBack from '@components/common/GoBack';

type OrderProps = {
    order: OrderType;
    deliveryOptions: DeliveryOption[];
    paymentMethods: PaymentMethod[];
};

const Order: FC<OrderProps> = memo(props => {
    const {order: initialOrder, deliveryOptions, paymentMethods} = props;
    const t = useTrans();
    const {confirm, openSideBar} = useUI();
    const {locale} = useStore();
    const [order, setOrder] = useState(() => initialOrder);

    const paymentMethod = useMemo(
        () =>
            paymentMethods.find(
                paymentMethod => paymentMethod.id === order.paymentMethodId
            ),
        [order.paymentMethodId, paymentMethods]
    );
    const bankAccount = useMemo(() => {
        if (!!order.subPaymentMethodId && !!paymentMethod) {
            return paymentMethod.bankAccounts?.find(
                bankAccount =>
                    bankAccount.paymentMethodId === order.subPaymentMethodId
            );
        }
    }, [order.subPaymentMethodId, paymentMethod]);
    const deliveryOption = useMemo(
        () =>
            deliveryOptions.find(
                deliveryOption => deliveryOption.type === order.deliveryType
            ),
        [order.deliveryType, deliveryOptions]
    );

    const getDurationText = useCallback((deliveryOption: DeliveryOption) => {
        let text = '';

        if (deliveryOption.minDeliveryDays === deliveryOption.maxDeliveryDays) {
            text = deliveryOption.minDeliveryDays.toString();

            if (deliveryOption.minDeliveryDays > 1) {
                text += ` ${t('Days')}`;
            } else {
                text += ` ${t('Day')}`;
            }
        } else {
            text = `${deliveryOption.minDeliveryDays} - ${deliveryOption.maxDeliveryDays}`;

            if (
                deliveryOption.minDeliveryDays > 1 ||
                deliveryOption.maxDeliveryDays > 1
            ) {
                text += ` ${t('Days')}`;
            } else {
                text += ` ${t('Day')}`;
            }
        }

        return text;
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const onRequestOrderCancellation = useCallback(() => {
        confirm(
            t('Cancel Order'),
            t(
                'You are about to request the cancellation of your order. Do you want to continue?'
            ),
            t('Cancel Order'),
            async () => {
                try {
                    setOrder({...order, isCancellationRequested: true});

                    await jsonRequest({
                        url: '/api/customers/request-order-cancellation',
                        method: 'POST',
                        data: {cartId: order.id}
                    });
                } catch (error: any) {
                    console.log(error);
                }
            }
        );
    }, [confirm, order, t]);

    const onCancelCancellationRequest = useCallback(() => {
        (async () => {
            try {
                setOrder({...order, isCancellationRequested: false});

                await jsonRequest({
                    url: '/api/customers/cancel-order-cancellation-request',
                    method: 'POST',
                    data: {cartId: order.id}
                });
            } catch (error: any) {
                console.log(error);
            }
        })();
    }, [order]);

    const formatDate = useCallback(
        (date: Date | string) => {
            if (typeof date === 'string') {
                const dtn = Date.parse(date);

                if (!isNaN(dtn)) {
                    return new Intl.DateTimeFormat(locale, {
                        month: 'long',
                        day: 'numeric'
                    }).format(new Date(dtn));
                } else {
                    return '';
                }
            }

            return new Intl.DateTimeFormat(locale, {
                month: 'long',
                day: 'numeric'
            }).format(date);
        },
        [locale]
    );

    const shipmentDetailHandler = useCallback(
        (item: CartItem) => {
            openSideBar(
                t('Shipment Detail'),
                <ShipmentDetail item={item} order={order} />,
                'large'
            );
        },
        [openSideBar, order, t]
    );

    const returnRequestHandler = useCallback(
        (item: CartItem) => {
            openSideBar(
                t('Return Request'),
                <ReturnForm
                    item={item}
                    order={order}
                    onClose={(
                        item: CartItem,
                        result: {
                            returnShipmentTackingCode: string;
                            carrierName: string;
                        }
                    ) => {
                        setOrder({
                            ...order,
                            items: order.items.map(orderItem => {
                                if (orderItem.productId === item.productId) {
                                    item.isReturnRequested = true;
                                    item.returnShipmentTackingCode =
                                        result.returnShipmentTackingCode;
                                }

                                return orderItem;
                            })
                        });
                    }}
                />
            );
        },
        [openSideBar, order, t]
    );
    const cancelReturnRequestHandler = useCallback(
        (item: CartItem) => {
            (async () => {
                try {
                    setOrder({
                        ...order,
                        items: order.items.map(orderItem => {
                            if (orderItem.productId === item.productId) {
                                item.isReturnRequested = false;
                                delete item.returnShipmentTackingCode;
                            }

                            return orderItem;
                        })
                    });

                    await jsonRequest({
                        url: '/api/customers/cancel-return-order',
                        method: 'POST',
                        data: {
                            locale,
                            orderId: order.orderId,
                            productId: item.productId
                        }
                    });
                } catch (error: any) {
                    console.log(error);
                }
            })();
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [order]
    );

    return (
        <>
            <GoBack />

            <div className="card-container flex flex-col items-stretch justify-between p-8 xl:flex-row">
                <div className="flex-1 text-center xl:text-left">
                    <h1 className="text-2xl font-semibold leading-6">
                        {`${t('Order No')}: ${order.orderCode}`}
                    </h1>
                    <p className="mt-3 text-sm text-muted">
                        {order.companyName
                            ? order.companyName
                            : `${order.firstName} ${order.lastName}`}
                    </p>

                    {order.isCanceled ? (
                        <div className="mt-3 inline-block rounded-full border border-red-600 px-3 py-1.5 text-sm font-semibold text-red-600">
                            {t('Canceled')}
                        </div>
                    ) : order.isCancellationRequested ? (
                        <div className="mt-3 inline-block rounded-full border border-red-600 px-3 py-1.5 text-sm font-semibold text-red-600">
                            {t('Cancellation Requested')}
                        </div>
                    ) : null}
                </div>

                <div className="mt-8 flex items-center justify-center space-x-8 xl:ml-8 xl:mt-0 xl:justify-start xl:space-x-8">
                    {!order.isCanceled && (
                        <>
                            {!order.isCancellationRequested ? (
                                <button
                                    className="group flex cursor-pointer flex-col items-center"
                                    onClick={onRequestOrderCancellation}
                                >
                                    <div className="flex h-10 w-10 flex-col items-center justify-center rounded-full bg-gray-100 text-muted transition group-hover:bg-primary-600 group-hover:text-white">
                                        <BanIcon className="h-5 w-5" />
                                    </div>
                                    <div className="mt-2.5 text-sm leading-3 transition group-hover:text-primary-600">
                                        {t('Cancel Order')}
                                    </div>
                                </button>
                            ) : (
                                <button
                                    className="group flex cursor-pointer flex-col items-center"
                                    onClick={onCancelCancellationRequest}
                                >
                                    <div className="flex h-10 w-10 flex-col items-center justify-center rounded-full bg-gray-100 text-muted transition group-hover:bg-primary-600 group-hover:text-white">
                                        <BanIcon className="h-5 w-5" />
                                    </div>
                                    <div className="mt-2.5 text-sm leading-3 transition group-hover:text-primary-600">
                                        {t('Cancel Cancellation Request')}
                                    </div>
                                </button>
                            )}
                        </>
                    )}
                </div>
            </div>

            <div className="mt-4 space-y-4 divide-y">
                {order.items.map(item => (
                    <div
                        className="card-container flex flex-col p-6 py-10"
                        key={item.productId + item.warehouseId}
                    >
                        <div className="flex flex-col xl:flex-row xl:space-x-6">
                            <div className="flex flex-1 space-x-6">
                                <UiImage
                                    className="h-48 flex-none rounded-lg"
                                    src={`${item.productImage}?w=240&q=75`}
                                    alt={item.productName}
                                    width={
                                        storeConfig.catalog
                                            .productImageShape === 'rectangle'
                                            ? 144
                                            : 192
                                    }
                                    height={192}
                                    fit="cover"
                                    position="center"
                                    quality={75}
                                />

                                <div className="flex flex-auto flex-col">
                                    <div>
                                        <h4 className="font-medium">
                                            <UiLink href={item.productLink}>
                                                {item.productName}
                                            </UiLink>
                                        </h4>

                                        <div className="mt-2 text-sm text-muted">
                                            {Array.isArray(
                                                item.productAttributes
                                            ) &&
                                                item.productAttributes.length >
                                                    0 && (
                                                    <div className="flex items-center space-x-3">
                                                        {item.productAttributes.map(
                                                            attribute => (
                                                                <div
                                                                    className="flex items-center text-muted"
                                                                    key={
                                                                        attribute.value
                                                                    }
                                                                >
                                                                    <div className="mr-0.5">
                                                                        {
                                                                            attribute.label
                                                                        }
                                                                        :
                                                                    </div>
                                                                    <div>
                                                                        {
                                                                            attribute.value
                                                                        }
                                                                    </div>
                                                                </div>
                                                            )
                                                        )}
                                                    </div>
                                                )}
                                        </div>

                                        {item.status === 'returned' ? (
                                            <div className="mt-3 inline-block rounded-full border border-red-600 px-3 py-1.5 text-xs font-semibold text-red-600">
                                                {t('Returned')}
                                            </div>
                                        ) : item.isReturnRequested ? (
                                            <div className="mt-3 inline-block rounded-full border border-red-600 px-3 py-1.5 text-xs font-semibold text-red-600">
                                                {t('Return Requested')}
                                            </div>
                                        ) : null}

                                        {item.isReturnRequested && (
                                            <div className="mb-4 mt-3 flex flex-col  xl:flex-row xl:items-center">
                                                <div className="mb-1 mr-0 text-sm xl:mb-0 xl:mr-1">
                                                    {t(
                                                        'Return Shipment Order Code'
                                                    )}
                                                    :
                                                </div>
                                                <div className="font-semibold text-base">
                                                    {
                                                        item.returnShipmentTackingCode
                                                    }
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    <div className="mt-6 flex flex-1 items-end">
                                        <dl className="flex space-x-4 divide-x text-sm sm:space-x-6">
                                            <div className="flex">
                                                <dt className="font-medium text-gray-900">
                                                    {t('Quantity')}
                                                </dt>
                                                <dd className="ml-2 text-gray-700">
                                                    {item.quantity}
                                                </dd>
                                            </div>

                                            <div className="flex pl-4 sm:pl-6">
                                                <dt className="font-medium text-gray-900">
                                                    {t('Price')}
                                                </dt>
                                                <dd className="ml-2 text-gray-700">
                                                    <Price price={item.price} />
                                                </dd>
                                            </div>
                                        </dl>
                                    </div>
                                </div>
                            </div>

                            <div className="mt-10 flex flex-row justify-center gap-6 xl:mt-0 xl:flex-col xl:justify-start">
                                {Array.isArray(item.shipmentRecords) &&
                                    item.shipmentRecords.length > 0 && (
                                        <button
                                            onClick={() =>
                                                shipmentDetailHandler(item)
                                            }
                                            className="group flex cursor-pointer items-center gap-5"
                                        >
                                            <div className="flex h-10 w-10 flex-col items-center justify-center rounded-full bg-gray-100 text-muted transition group-hover:bg-primary-600 group-hover:text-white">
                                                <TruckFastIcon className="h-5 w-5" />
                                            </div>
                                            <div className="text-sm leading-3 transition group-hover:text-primary-600">
                                                {t('Shipment Detail')}
                                            </div>
                                        </button>
                                    )}

                                {item.shipmentStatus === 'delivered' &&
                                    item.status !== 'returned' && (
                                        <>
                                            {item.isReturnRequested ? (
                                                <button
                                                    onClick={() =>
                                                        cancelReturnRequestHandler(
                                                            item
                                                        )
                                                    }
                                                    className="group flex cursor-pointer items-center gap-5"
                                                >
                                                    <div className="flex h-10 w-10 flex-col items-center justify-center rounded-full bg-gray-100 text-muted transition group-hover:bg-primary-600 group-hover:text-white">
                                                        <ReturnIcon className="h-5 w-5" />
                                                    </div>
                                                    <div className="text-sm leading-3 transition group-hover:text-primary-600">
                                                        {t(
                                                            'Cancel Return Request'
                                                        )}
                                                    </div>
                                                </button>
                                            ) : (
                                                <button
                                                    onClick={() =>
                                                        returnRequestHandler(
                                                            item
                                                        )
                                                    }
                                                    className="group flex cursor-pointer items-center gap-5"
                                                >
                                                    <div className="flex h-10 w-10 flex-col items-center justify-center rounded-full bg-gray-100 text-muted transition group-hover:bg-primary-600 group-hover:text-white">
                                                        <ReturnIcon className="h-5 w-5" />
                                                    </div>
                                                    <div className="text-sm leading-3 transition group-hover:text-primary-600">
                                                        {t('Request Return')}
                                                    </div>
                                                </button>
                                            )}
                                        </>
                                    )}
                            </div>
                        </div>

                        {Array.isArray(item.shipmentStatuses) &&
                            item.shipmentStatuses.length > 0 && (
                                <div className="mt-6 hidden border-t pb-8 pt-6 xl:block">
                                    {item.shipmentDeliveryDate && (
                                        <div className="mb-4 text-sm">
                                            {t('Estimated delivery date')}:{' '}
                                            <span className="text-primary-600">
                                                {formatDate(
                                                    item.shipmentDeliveryDate
                                                )}
                                            </span>
                                        </div>
                                    )}

                                    <div className="relative">
                                        <div
                                            className={cls(
                                                'shipping-status-road',
                                                {
                                                    'is-on-road':
                                                        item.shipmentStatus !==
                                                            'order-created' &&
                                                        item.shipmentStatus !==
                                                            'delivered'
                                                }
                                            )}
                                        ></div>

                                        <div className="z-1 absolute left-0 top-0 flex h-20 w-full">
                                            {item.shipmentStatuses.map(
                                                status => (
                                                    <div
                                                        key={status.value}
                                                        className="relative flex flex-1 flex-col items-center"
                                                    >
                                                        {status.isActive && (
                                                            <div className="absolute left-0 top-0 z-[2] flex w-full justify-center">
                                                                <svg
                                                                    className="-mt-1.5 rotate-90"
                                                                    xmlns="http://www.w3.org/2000/svg"
                                                                    width="16"
                                                                    height="32"
                                                                >
                                                                    <g fill="none">
                                                                        <path
                                                                            fill="#FFF"
                                                                            d="M4 8 3 4l5.5-1.5 5 1.5-1 4z"
                                                                        />
                                                                        <path
                                                                            className="fill-primary-500"
                                                                            d="M3.702 11.45H1.36v20.155h.488V32h12.438v-.395h.488V11.45h-2.476z"
                                                                        />
                                                                        <path
                                                                            className="fill-primary-600"
                                                                            d="M3.032.77c-.987.563-.804 2.558-.804 2.558l.235 7.601h11.074l.235-7.608s.214-1.991-.774-2.555C12.521.495 11.186 0 8.03 0 4.807 0 3.51.498 3.032.77zm-.34 9.22c.038-2.844-.009-4.932-.009-4.932h.27c.469 1.957.2 4.99.2 4.99l-.461-.059zm10.666.015-.34.043s-.268-3.033.202-4.99h.268s-.168 2.103-.13 4.947zm-.575-5.795c.135.293-.302 3-.302 3-3.223-.195-4.365-.13-4.365-.13s-1.172-.065-4.395.13c0 0-.421-2.707-.287-3 .134-.294 1.727-.457 4.683-.457 2.853 0 4.532.163 4.666.457z"
                                                                        />
                                                                        <path
                                                                            className="fill-primary-400"
                                                                            d="M3 30V13h1v17zm3 0V13h1v17zm3 0V13h1v17zm3 0V13h1v17zM3.702 10.93v.52h8.596v-.52z"
                                                                        />
                                                                        <path
                                                                            className="fill-primary-500"
                                                                            d="M.5 30v-4h1v4zm14 0v-4h1v4zm-14-5v-4h1v4zm14 0v-4h1v4zM1.5 7V3h1v4zm12 0V3h1v4z"
                                                                        />
                                                                    </g>
                                                                </svg>
                                                            </div>
                                                        )}

                                                        <div className="h-8 w-8 rounded-full bg-gray-200"></div>

                                                        <div className="mt-4 text-center text-xs font-semibold">
                                                            {t(status.label)}
                                                        </div>
                                                    </div>
                                                )
                                            )}
                                        </div>
                                    </div>
                                </div>
                            )}
                    </div>
                ))}
            </div>

            <dl className="card-container mt-4 grid grid-cols-2 gap-x-6 p-8 text-sm">
                <div>
                    <dt className="font-medium text-gray-900">
                        {t('Delivery address')}
                    </dt>
                    <dd className="mt-2 text-gray-700">
                        <address className="not-italic">
                            <div>{order.deliveryAddress?.street}</div>
                            {!!order.deliveryAddress?.street2 && (
                                <div>{order.deliveryAddress?.street2}</div>
                            )}
                            <div className="flex items-center space-x-2">
                                {order.deliveryAddress?.district && (
                                    <span>
                                        {order.deliveryAddress?.district}
                                    </span>
                                )}
                                <span>{order.deliveryAddress?.city}</span>
                                {order.deliveryAddress?.state && (
                                    <span>{order.deliveryAddress?.state}</span>
                                )}
                            </div>
                            <div className="flex items-center space-x-2">
                                <span>{order.deliveryAddress?.postalCode}</span>
                                <span>
                                    {order.deliveryAddress?.countryName}
                                </span>
                            </div>
                        </address>
                    </dd>
                </div>

                <div>
                    <dt className="font-medium text-gray-900">
                        {t('Billing address')}
                    </dt>
                    <dd className="mt-2 text-gray-700">
                        <address className="not-italic">
                            <div>{order.billingAddress?.street}</div>
                            {!!order.billingAddress?.street2 && (
                                <div>{order.billingAddress?.street2}</div>
                            )}
                            <div className="flex items-center space-x-2">
                                {order.billingAddress?.district && (
                                    <span>
                                        {order.billingAddress?.district}
                                    </span>
                                )}
                                <span>{order.billingAddress?.city}</span>
                                {order.billingAddress?.state && (
                                    <span>{order.billingAddress?.state}</span>
                                )}
                            </div>
                            <div className="flex items-center space-x-2">
                                <span>{order.billingAddress?.postalCode}</span>
                                <span>{order.billingAddress?.countryName}</span>
                            </div>
                        </address>
                    </dd>
                </div>
            </dl>

            <dl className="card-container mt-4 grid grid-cols-2 gap-x-6 p-8 text-sm">
                <div>
                    <dt className="font-medium text-gray-900">
                        {t('Payment method')}
                    </dt>
                    <dd className="mt-2 text-gray-700">
                        <p>{t(paymentMethod?.name as string)}</p>

                        {!!bankAccount && (
                            <>
                                <p>{bankAccount.bankName}</p>
                                <p>{bankAccount.bankBranchName}</p>
                                <p>{bankAccount.iban}</p>
                            </>
                        )}
                    </dd>
                </div>
                <div>
                    <dt className="font-medium text-gray-900">
                        {t('Delivery option')}
                    </dt>
                    <dd className="mt-2 text-gray-700">
                        <p>{t(deliveryOption?.name as string)}</p>
                        <p>
                            {getDurationText(deliveryOption as DeliveryOption)}
                        </p>
                    </dd>
                </div>
            </dl>

            <dl className="card-container mt-4 space-y-6 p-8 text-sm">
                <div className="flex justify-between">
                    <dt className="text-gray-900">{t('Subtotal')}</dt>
                    <dd className="text-gray-700">
                        <Price price={order.subTotal} />
                    </dd>
                </div>
                <div className="flex justify-between">
                    <dt className="text-gray-900"> {t('Tax amount')}</dt>
                    <dd className="text-gray-700">
                        <Price price={order.taxTotal} />
                    </dd>
                </div>
                <div className="flex justify-between">
                    <dt className="text-gray-900"> {t('Delivery amount')}</dt>
                    <dd className="text-gray-700">
                        <Price price={order.deliveryTotal} />
                    </dd>
                </div>
                {order.cashOnDeliveryServiceFee > 0 && (
                    <div className="flex justify-between">
                        <dt className="text-gray-900">
                            {' '}
                            {t('Cash on delivery service fee')}
                        </dt>
                        <dd className="text-gray-700">
                            <Price price={order.cashOnDeliveryServiceFee} />
                        </dd>
                    </div>
                )}
                {Array.isArray(order.discounts) &&
                    order.discounts.length > 0 &&
                    order.discounts.map(discount => (
                        <div
                            key={discount.id}
                            className="flex items-center justify-between"
                        >
                            <dt className="flex items-center gap-2 rounded-lg bg-gray-200 px-3 py-1 text-sm text-muted">
                                <svg
                                    width="13"
                                    height="13"
                                    viewBox="0 0 18 18"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <g mask="url(#a)" fill="#000">
                                        <path
                                            d="M17.765 3.087a1.993 1.993 0 0 0-1.779-1.089h-5.16a2 2 0 0 0-1.42.592L2.59 9.475c0 .002 0 .002-.002.002a2.033 2.033 0 0 0-.5.874L.74 8.048a1.998 1.998 0 0 1 .712-2.735L9.815.424A1.988 1.988 0 0 1 11.34.22l4.985 1.335a1.998 1.998 0 0 1 1.439 1.532Z"
                                            fillOpacity=".55"
                                        />
                                        <path d="M10.826 1.998h5.16a1.998 1.998 0 0 1 1.999 1.998v5.168c0 .53-.21 1.039-.585 1.413l-6.812 6.812a1.999 1.999 0 0 1-2.818.01l-5.162-5.096a2 2 0 0 1-.02-2.825c.002 0 .003 0 .003-.002l6.814-6.884a1.998 1.998 0 0 1 1.421-.594Zm2.663 5.995a1.5 1.5 0 1 0-.001-3 1.5 1.5 0 0 0 0 3Z" />
                                    </g>
                                </svg>
                                <p> {discount.description}</p>
                            </dt>

                            <dd>
                                <Price
                                    className="text-primary-600"
                                    price={-discount.amount}
                                />
                            </dd>
                        </div>
                    ))}
                <div className="flex justify-between">
                    <dt className="font-medium text-gray-900">{t('Total')}</dt>
                    <dd className="font-medium text-gray-900">
                        <Price price={order.grandTotal} />
                    </dd>
                </div>
            </dl>
        </>
    );
});

if (isDev) {
    Order.displayName = 'Order';
}

export default Order;

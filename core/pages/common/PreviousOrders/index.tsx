import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {UiAvatar, UiLink} from '@core/components/ui';
import {useStore, useTrans} from '@core/hooks';
import {Order as OrderType, PaymentMethod} from '@core/types';
import {ArrowRightIcon, BoxOpenIcon} from '@core/icons/solid';
import Price from '@components/common/Price';
import Seo from '@components/common/Seo';

type Order = OrderType & {
    dateTitle: string;
    dateText: string;
    images: string[];
    paymentMethod?: PaymentMethod;
    deliveredCount: number;
    returnedCount: number;
    canceledCount: number;
    waitingCount: number;
};

type PreviousOrdersProps = {
    orders: Order[];
    paymentMethods: PaymentMethod[];
};

const PreviousOrders: FC<PreviousOrdersProps> = memo(props => {
    const {orders, paymentMethods} = props;
    const t = useTrans();
    const {locale} = useStore();

    const items = useMemo(
        () =>
            orders.map(order => {
                order.updatedAt = new Date(
                    Date.parse(order.updatedAt.toString())
                );

                const date = order.updatedAt;
                const now = new Date();
                if (date.getFullYear() === now.getFullYear()) {
                    order.dateTitle = new Intl.DateTimeFormat(locale, {
                        month: 'long'
                    }).format(date);
                    order.dateText = new Intl.DateTimeFormat(locale, {
                        month: 'long',
                        weekday: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    }).format(date);
                } else {
                    order.dateTitle = new Intl.DateTimeFormat(locale, {
                        year: 'numeric',
                        month: 'long'
                    }).format(date);
                    order.dateText = new Intl.DateTimeFormat(locale, {
                        year: 'numeric',
                        month: 'long',
                        weekday: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    }).format(date);
                }

                const images: string[] = [];
                for (const item of order.items) {
                    if (!images.includes(item.productImage)) {
                        images.push(item.productImage);
                    }
                }
                order.images = images;

                let delivered = 0;
                let returned = 0;
                let canceled = 0;
                let waiting = 0;
                for (const item of order.items) {
                    if (item.status === 'delivered') {
                        delivered++;
                    } else if (item.status === 'canceled') {
                        canceled++;
                    } else if (
                        item.status === 'returned' ||
                        item.isReturnRequested
                    ) {
                        returned++;
                    } else {
                        waiting++;
                    }
                }
                order.deliveredCount = delivered;
                order.canceledCount = canceled;
                order.returnedCount = returned;
                order.waitingCount = waiting;

                order.paymentMethod = paymentMethods.find(
                    paymentMethod => paymentMethod.id === order.paymentMethodId
                );

                return order;
            }),
        [locale, orders, paymentMethods]
    );
    const titles = useMemo(() => {
        const titles: string[] = [];

        for (const item of items) {
            if (!titles.includes(item.dateTitle)) {
                titles.push(item.dateTitle);
            }
        }

        return titles;
    }, [items]);

    return (
        <>
            <Seo title={t('Previous Orders')} />

            <p className="mb-4 hidden text-xl font-medium xl:block">
                {t('Previous Orders')}
            </p>

            {orders?.length > 0 ? (
                <div className="my-4 space-y-4 xl:my-0 xl:space-y-12">
                    {titles.map(title => (
                        <div key={title}>
                            <div className="card-container mb-4 px-6 py-4 text-xl font-medium">
                                {title}
                            </div>

                            <div className="space-y-4">
                                {items
                                    .filter(item => item.dateTitle === title)
                                    .map((item, index) => (
                                        <UiLink
                                            href={`/previous-orders/${item.id}`}
                                            className="card-container group relative flex cursor-pointer flex-col items-stretch border-2 border-transparent p-4 transition hover:border-primary-600 xl:flex-row xl:p-6"
                                            key={item.id}
                                        >
                                            {item.images.length > 0 && (
                                                <div className="hidden items-center justify-between xl:mr-6 xl:flex">
                                                    <UiAvatar.Group
                                                        size="2xl"
                                                        max={2}
                                                    >
                                                        {item.images.map(
                                                            image => (
                                                                <UiAvatar
                                                                    key={image}
                                                                    src={image}
                                                                />
                                                            )
                                                        )}
                                                    </UiAvatar.Group>
                                                </div>
                                            )}

                                            <div className="flex flex-1 flex-col justify-center leading-3 xl:mr-8">
                                                <div className="flex">
                                                    <div className="text-muted">
                                                        {t('Order No')}:
                                                    </div>
                                                    <div className="ml-1.5 font-medium">
                                                        {item.orderCode}
                                                    </div>
                                                </div>
                                                <div className="mt-4 text-muted">
                                                    {item.dateText}
                                                </div>

                                                {item.isCanceled ? (
                                                    <div className="mt-6 inline-block self-start rounded-full border border-red-600 px-3 py-1.5 text-sm font-semibold text-red-600">
                                                        {t('Canceled')}
                                                    </div>
                                                ) : item.isCancellationRequested ? (
                                                    <div className="mt-6 inline-block self-start rounded-full border border-red-600 px-3 py-1.5 text-sm font-semibold text-red-600">
                                                        {t(
                                                            'Cancellation Requested'
                                                        )}
                                                    </div>
                                                ) : null}
                                            </div>

                                            <div className="mt-8 flex flex-1 flex-col items-stretch justify-center xl:mr-8 xl:mt-0">
                                                <div className="text-sm leading-3">
                                                    <div className="mb-1 whitespace-nowrap">
                                                        {t(
                                                            'Form {itemCount} product(s)',
                                                            {
                                                                itemCount:
                                                                    item.itemCount
                                                            }
                                                        )}
                                                    </div>
                                                    {item.canceledCount > 0 ? (
                                                        <div className="whitespace-nowrap">
                                                            {t(
                                                                '{deliveredCount} item(s) delivered, {canceledCount} item(s) canceled',
                                                                {
                                                                    deliveredCount:
                                                                        item.deliveredCount,
                                                                    canceledCount:
                                                                        item.canceledCount
                                                                }
                                                            )}
                                                        </div>
                                                    ) : (
                                                        <div className="whitespace-nowrap">
                                                            {t(
                                                                '{deliveredCount} item(s) delivered, {returnedCount} item(s) returned',
                                                                {
                                                                    deliveredCount:
                                                                        item.deliveredCount,
                                                                    returnedCount:
                                                                        item.returnedCount
                                                                }
                                                            )}
                                                        </div>
                                                    )}
                                                </div>

                                                <div className="mt-3.5 flex overflow-hidden rounded-lg">
                                                    <div
                                                        className="h-2 bg-green-500"
                                                        style={{
                                                            width: `${
                                                                (item.deliveredCount /
                                                                    item.itemCount) *
                                                                100
                                                            }%`
                                                        }}
                                                    />
                                                    {item.canceledCount > 0 ? (
                                                        <div
                                                            className="h-2 bg-red-500"
                                                            style={{
                                                                width: `${
                                                                    (item.canceledCount /
                                                                        item.itemCount) *
                                                                    100
                                                                }%`
                                                            }}
                                                        />
                                                    ) : (
                                                        <div
                                                            className="h-2 bg-red-500"
                                                            style={{
                                                                width: `${
                                                                    (item.returnedCount /
                                                                        item.itemCount) *
                                                                    100
                                                                }%`
                                                            }}
                                                        />
                                                    )}
                                                    <div
                                                        className="h-2 bg-gray-200"
                                                        style={{
                                                            width: `${
                                                                (item.waitingCount /
                                                                    item.itemCount) *
                                                                100
                                                            }%`
                                                        }}
                                                    />
                                                </div>
                                            </div>

                                            <div className="mt-8 flex flex-col items-end justify-center xl:mr-8 xl:mt-0">
                                                <Price
                                                    className="text-md font-medium leading-4"
                                                    price={item.grandTotal}
                                                />
                                                {item.paymentMethod && (
                                                    <div className="mt-3 text-xs leading-3 text-muted">
                                                        {t(
                                                            item.paymentMethod
                                                                ?.name as string
                                                        )}
                                                    </div>
                                                )}
                                            </div>

                                            <div className="hidden items-center justify-end xl:flex">
                                                <div className="flex h-9 w-9 cursor-pointer items-center justify-center rounded-full bg-gray-100 text-muted transition group-hover:bg-primary-600 group-hover:text-white">
                                                    <ArrowRightIcon className="h-4 w-4" />
                                                </div>
                                            </div>

                                            <div className="absolute bottom-6 hidden">
                                                <div className="hidden h-9 w-9 cursor-pointer items-center justify-center rounded-full bg-gray-100 text-muted transition group-hover:bg-primary-600 group-hover:text-white sm:flex xl:hidden">
                                                    {index + 1}
                                                </div>
                                            </div>
                                        </UiLink>
                                    ))}
                            </div>
                        </div>
                    ))}
                </div>
            ) : (
                <div className="card-container flex flex-1 flex-col items-center justify-center py-12 xl:px-12 xl:py-24">
                    <div className="flex h-24 w-24 items-center justify-center rounded-lg border border-dashed border-gray-500 text-muted">
                        <BoxOpenIcon className="h-8 w-8" />
                    </div>

                    <h2 className="pt-8 text-center text-2xl font-semibold">
                        {t('No orders found!')}
                    </h2>

                    <p className="px-10 pt-2 text-center text-muted">
                        {t(
                            'There are no orders in your account. After shopping on our site, you can access the order details from this page.'
                        )}
                    </p>
                </div>
            )}
        </>
    );
});

if (isDev) {
    PreviousOrders.displayName = 'MyOrders';
}

export default PreviousOrders;

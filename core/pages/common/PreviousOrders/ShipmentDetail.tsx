import {FC, memo, useCallback, useEffect, useMemo, useState} from 'react';
import {CartItem, Order} from '@core/types';
import {cls, isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiDisclosure, UiImage} from '@core/components/ui';
import {CheckCircleIcon} from '@core/icons/solid';

type ShipmentDetailProps = {
    item: CartItem;
    order: Order;
};

const ShipmentDetail: FC<ShipmentDetailProps> = memo(({item, order}) => {
    const t = useTrans();
    const {locale} = useStore();

    const activeStatusOptionIndex = useMemo(
        () =>
            item.shipmentStatuses?.findIndex(
                ss => ss.value === item.shipmentStatus
            ) ?? -1,
        [item]
    );

    const [isCopied, setIsCopied] = useState(false);
    useEffect(() => {
        if (isCopied)
            setTimeout(() => {
                setIsCopied(false);
            }, 1000);
    }, [isCopied]);

    const formatDate = useCallback(
        (date: Date) => {
            return new Intl.DateTimeFormat(locale, {
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            }).format(date);
        },
        [locale]
    );

    const records = useMemo(
        () =>
            (item.shipmentRecords ?? []).map(record => {
                // @ts-ignore
                record.date = new Date(Date.parse(record.date));

                return record;
            }),
        [item]
    );

    return (
        <section className="mt-2 flex flex-col px-8 pb-8">
            <div className="flex flex-col items-center">
                <div>
                    <UiImage
                        src={item.carrierLogo ?? '/site-logo.png'}
                        alt={t('Carrier logo')}
                        className="h-6 w-auto"
                        raw
                    />
                </div>
                <p className="mt-4 font-medium text-muted">
                    {t('Shipment Tracking Number')}
                </p>
                <p
                    onClick={() => {
                        setIsCopied(true);
                        navigator.clipboard.writeText(
                            item.shipmentTackingCode ?? ''
                        );
                    }}
                    className="mt-2 w-60 cursor-pointer select-none rounded-full bg-gray-200 px-6 py-2 text-center text-lg tracking-wider text-gray-700"
                >
                    {t(isCopied ? t('Copied') : item.shipmentTackingCode ?? '')}
                </p>
            </div>

            <div className="mt-12 flex flex-col xl:flex-row xl:divide-x">
                <div className="flex flex-1 xl:pr-6">
                    <div className="relative">
                        <div
                            className={cls('shipping-vertical-status-road', {
                                'is-on-road':
                                    item.shipmentStatus !== 'order-created' &&
                                    item.shipmentStatus !== 'delivered'
                            })}
                        ></div>

                        <div className="z-1 absolute left-0 top-0 flex h-full flex-col gap-5">
                            {item.shipmentStatuses?.map((status, index) => (
                                <div
                                    key={status.value}
                                    className="flex h-8 items-center"
                                >
                                    {status.isActive && (
                                        <svg
                                            className="absolute left-0.5 z-10 rotate-180"
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="16"
                                            height="32"
                                        >
                                            <g fill="none">
                                                <path
                                                    fill="#FFF"
                                                    d="M4 8 3 4l5.5-1.5 5 1.5-1 4z"
                                                />
                                                <path
                                                    className="fill-primary-500"
                                                    d="M3.702 11.45H1.36v20.155h.488V32h12.438v-.395h.488V11.45h-2.476z"
                                                />
                                                <path
                                                    className="fill-primary-600"
                                                    d="M3.032.77c-.987.563-.804 2.558-.804 2.558l.235 7.601h11.074l.235-7.608s.214-1.991-.774-2.555C12.521.495 11.186 0 8.03 0 4.807 0 3.51.498 3.032.77zm-.34 9.22c.038-2.844-.009-4.932-.009-4.932h.27c.469 1.957.2 4.99.2 4.99l-.461-.059zm10.666.015-.34.043s-.268-3.033.202-4.99h.268s-.168 2.103-.13 4.947zm-.575-5.795c.135.293-.302 3-.302 3-3.223-.195-4.365-.13-4.365-.13s-1.172-.065-4.395.13c0 0-.421-2.707-.287-3 .134-.294 1.727-.457 4.683-.457 2.853 0 4.532.163 4.666.457z"
                                                />
                                                <path
                                                    className="fill-primary-400"
                                                    d="M3 30V13h1v17zm3 0V13h1v17zm3 0V13h1v17zm3 0V13h1v17zM3.702 10.93v.52h8.596v-.52z"
                                                />
                                                <path
                                                    className="fill-primary-500"
                                                    d="M.5 30v-4h1v4zm14 0v-4h1v4zm-14-5v-4h1v4zm14 0v-4h1v4zM1.5 7V3h1v4zm12 0V3h1v4z"
                                                />
                                            </g>
                                        </svg>
                                    )}

                                    <div className="h-7 w-7 rounded-full bg-gray-200"></div>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="ml-6 flex flex-1 flex-col gap-5">
                        {item.shipmentStatuses?.map((status, index) => (
                            <div
                                key={status.value}
                                className="flex h-8 items-center"
                            >
                                {activeStatusOptionIndex >= index ? (
                                    <CheckCircleIcon
                                        className="mr-4 h-7 w-7 text-success-600"
                                        aria-hidden="true"
                                    />
                                ) : (
                                    <CheckCircleIcon
                                        className="mr-4 h-7 w-7 text-gray-300"
                                        aria-hidden="true"
                                    />
                                )}

                                <div className="text-sm font-semibold">
                                    {t(status.label)}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                <div className="mt-8 flex flex-1 flex-col divide-y xl:mt-0 xl:pl-6">
                    <dl className="pb-4">
                        <dt className="text-sm font-medium text-gray-900">
                            {t('Delivery address')}
                        </dt>
                        <dd className="mt-2 text-sm text-gray-700">
                            <address className="not-italic">
                                <div>{order.deliveryAddress?.street}</div>
                                {!!order.deliveryAddress?.street2 && (
                                    <div>{order.deliveryAddress?.street2}</div>
                                )}
                                <div className="flex items-center space-x-2">
                                    {order.deliveryAddress?.district && (
                                        <span>
                                            {order.deliveryAddress?.district}
                                        </span>
                                    )}
                                    <span>{order.deliveryAddress?.city}</span>
                                    {order.deliveryAddress?.state && (
                                        <span>
                                            {order.deliveryAddress?.state}
                                        </span>
                                    )}
                                </div>
                                <div className="flex items-center space-x-2">
                                    <span>
                                        {order.deliveryAddress?.postalCode}
                                    </span>
                                    <span>
                                        {order.deliveryAddress?.countryName}
                                    </span>
                                </div>
                            </address>
                            <p className="mt-1 text-sm">
                                {`${order.firstName ?? ''} ${
                                    order.lastName ?? ''
                                }`}
                            </p>
                        </dd>
                    </dl>

                    <dl className="py-4">
                        <dt className="text-sm font-medium text-gray-900">
                            {t('Arrival Store Information')}
                        </dt>
                        <dd className="mt-2 text-sm text-gray-700">
                            <p className="text-sm">
                                {t(item.carrierName ?? 'hepsiJet')}
                            </p>
                            <p className="text-sm">
                                {/*@ts-ignore*/}
                                {item.shipmentRecords[0].locationAddress}
                            </p>
                            <p className="text-sm text-primary-600">
                                {/*@ts-ignore*/}
                                {item.shipmentRecords[0].locationPhone}
                            </p>
                        </dd>
                    </dl>
                </div>
            </div>

            <UiDisclosure>
                <UiDisclosure.Button className="mb-2 mt-8 text-left font-medium text-primary-600 xl:mt-12">
                    {t('Show Shipment Details')}
                </UiDisclosure.Button>

                {records.map(record => (
                    <UiDisclosure.Panel
                        key={record.status}
                        className="mt-2 flex items-center justify-between gap-6 rounded-lg bg-gray-100 p-4 text-muted"
                    >
                        <div className="text-xs">
                            <p className="font-semibold">
                                {t(record.description)}
                            </p>
                            <p>{formatDate(record.date)}</p>
                        </div>
                        <div>
                            <p className="text-right text-xs font-medium text-primary-600">
                                {record.locationAddress}
                            </p>
                        </div>
                    </UiDisclosure.Panel>
                ))}
            </UiDisclosure>
        </section>
    );
});

if (isDev) {
    ShipmentDetail.displayName = 'ShipmentDetailDrawer';
}

export default ShipmentDetail;

import React, {FC, memo, useCallback, useMemo, useRef, useState} from 'react';
import {useForm, FormProvider} from 'react-hook-form';
import {cls, isDev, jsonRequest, toLower} from '@core/helpers';
import {CartItem, Order} from '@core/types';
import {useStore, useTrans, useUI} from '@core/hooks';
import {
    UiAlert,
    UiForm,
    UiTextarea,
    UiButton,
    UiImage
} from '@core/components/ui';
import storeConfig from '~/store.config';
import Price from '@components/common/Price';

type ReturnFormProps = {
    item: CartItem;
    order: Order;
    onClose: (
        item: CartItem,
        result: {
            returnShipmentTackingCode: string;
            carrierName: string;
        }
    ) => void;
};

const ReturnForm: FC<ReturnFormProps> = memo(({item, order, onClose}) => {
    const methods = useForm({
        defaultValues: {
            reasonId: '',
            description: ''
        }
    });
    const {
        register,
        formState: {errors},
        watch
    } = methods;
    const {locale} = useStore();
    const t = useTrans();
    const {closeSideBar} = useUI();
    const [errorMessage, setErrorMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [returnOrderResult, setReturnOrderResult] = useState<{
        returnShipmentTackingCode: string;
        carrierName: string;
    }>();
    const inProgress = useRef(false);

    const reasonId = watch('reasonId');
    const isDescriptionShown = useMemo(() => {
        const option = order.returnReasonOptions.find(
            option => option.value === reasonId
        );

        return !!option && toLower(option.label) === toLower(t('Other'));
    }, [order.returnReasonOptions, t, reasonId]);

    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgress.current) return;

            inProgress.current = true;
            setIsLoading(true);

            try {
                const result = await jsonRequest({
                    url: '/api/customers/create-return-order',
                    method: 'POST',
                    data: {
                        locale,
                        shipmentTackingCode: item.shipmentTackingCode,
                        productId: item.productId,
                        quantity: item.quantity,
                        reasonId: data.reasonId,
                        description: data.description
                    }
                });

                setReturnOrderResult(result);

                // closeSideBar();
            } catch (error: any) {
                setErrorMessage(error.message);
            }

            setIsLoading(false);
            inProgress.current = false;
        },
        [item.productId, item.quantity, item.shipmentTackingCode, locale]
    );

    return (
        <>
            {returnOrderResult ? (
                <div className="my-4 flex flex-col px-4 pb-4 xl:my-2 xl:px-6 xl:pb-6">
                    <div className="mb-2 font-semibold">
                        {t('Return Request Created')}
                    </div>
                    <div className="mb-4">
                        {t(
                            'Your return request has been successfully created. You can deliver the product you want to return to the nearest {carrierName} branch with the cargo order code {returnShipmentTackingCode}.',
                            {
                                returnShipmentTackingCode:
                                    returnOrderResult.returnShipmentTackingCode,
                                carrierName: returnOrderResult.carrierName
                            }
                        )}
                    </div>
                    <UiButton
                        className="mt-2 w-full"
                        variant="solid"
                        color="primary"
                        size="xl"
                        onClick={() => {
                            onClose(item, returnOrderResult);
                            closeSideBar();
                        }}
                    >
                        {t('Close')}
                    </UiButton>
                </div>
            ) : (
                <FormProvider {...methods}>
                    <UiForm
                        className="my-4 flex flex-col px-4 pb-4 xl:my-2 xl:px-6 xl:pb-6"
                        onSubmit={methods.handleSubmit(onSubmit)}
                    >
                        {!!errorMessage && (
                            <UiAlert className="mb-6" color="danger">
                                {t(errorMessage)}
                            </UiAlert>
                        )}

                        <div className="flex">
                            <div
                                className={cls(
                                    'h-12 flex-shrink-0 overflow-hidden rounded',
                                    {
                                        'w-9':
                                            storeConfig.catalog
                                                .productImageShape ===
                                            'rectangle',
                                        'w-12':
                                            storeConfig.catalog
                                                .productImageShape !==
                                            'rectangle'
                                    }
                                )}
                            >
                                <UiImage
                                    className="h-full w-full rounded"
                                    src={item.productImage}
                                    alt={item.productName}
                                    width={
                                        storeConfig.catalog
                                            .productImageShape === 'rectangle'
                                            ? 72
                                            : 96
                                    }
                                    height={96}
                                    fit="cover"
                                    position="center"
                                    quality={75}
                                />
                            </div>

                            <div className="ml-4 flex flex-1 flex-col justify-between">
                                <h3 className="-mt-1 text-sm">
                                    {item.productName}
                                </h3>

                                <Price className="text-sm" price={item.price} />
                            </div>
                        </div>

                        <div className="mt-4 border-t pt-4">
                            <UiForm.Field
                                label={t('Return reason')}
                                error={
                                    errors.reasonId &&
                                    errors.reasonId.type === 'required'
                                        ? t('{label} is required', {
                                              label: t('Return reason')
                                          })
                                        : undefined
                                }
                                {...register('reasonId', {
                                    required: true
                                })}
                                defaultValue="other"
                                selection
                            >
                                {order.returnReasonOptions.map(option => (
                                    <option
                                        key={option.value}
                                        value={option.value}
                                    >
                                        {option.label}
                                    </option>
                                ))}
                            </UiForm.Field>

                            {isDescriptionShown && (
                                <UiForm.Control className="mt-3">
                                    <UiTextarea
                                        placeholder={t('Description')}
                                        className="h-24"
                                        maxLength={1000}
                                        {...register('description', {
                                            required: false,
                                            maxLength: 1000
                                        })}
                                    />
                                </UiForm.Control>
                            )}

                            <UiButton
                                className="mt-2 w-full"
                                variant="solid"
                                color="primary"
                                size="xl"
                                loading={isLoading}
                                disabled={isLoading}
                            >
                                {t('Request Return')}
                            </UiButton>
                        </div>
                    </UiForm>
                </FormProvider>
            )}
        </>
    );
});

if (isDev) {
    ReturnForm.displayName = 'ReturnForm';
}

export default ReturnForm;

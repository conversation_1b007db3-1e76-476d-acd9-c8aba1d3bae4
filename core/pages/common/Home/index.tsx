import {memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {Page} from '@core/types';
import MainSlider from '@components/common/MainSlider';
import FeaturedProductSlider from '@components/common/FeaturedProductSlider';
import {useStore, useTrans} from '@core/hooks';
import Seo from '@components/common/Seo';
import OrderedGroupedItems from '@core/types/OrderedGroupedItems';

type HomePageProps = {
    productCatalogMap: any;
};

const Home: Page<HomePageProps> = memo((props: HomePageProps) => {
    const t = useTrans();
    const {productCatalogMap} = props;
    const {navigation} = useStore();

    const orderedGroupedItems = useMemo(() => {
        const subItems = navigation.filter(
            item => item.depth === 0 && !item.showInMainMenu
        );

        const items: OrderedGroupedItems[] = [];

        let index = 0;

        for (const subItem of subItems) {
            if (subItem.type !== 'product-catalog') {
                const lastItem =
                    items.length > 0 ? items[items.length - 1] : undefined;

                if (typeof lastItem !== 'undefined') {
                    if (lastItem.type === subItem.type) {
                        lastItem.items.push(subItem);
                    } else {
                        items.push({
                            id: `${subItem.type}-${index}`,
                            type: subItem.type,
                            items: [subItem]
                        });
                    }
                } else {
                    items.push({
                        id: `${subItem.type}-${index}`,
                        type: subItem.type,
                        items: [subItem]
                    });
                }
            } else {
                items.push({
                    id: `${subItem.type}-${index}`,
                    type: subItem.type,
                    items: [subItem],
                    products: productCatalogMap[subItem.id]?.products,
                    detailPageLink:
                        productCatalogMap[subItem.id]?.detailPageLink,
                    catalogName: productCatalogMap[subItem.id]?.catalogName
                });
            }

            index++;
        }

        return items;
    }, [navigation, productCatalogMap]);

    return (
        <>
            <Seo title={t('Home')} />

            {orderedGroupedItems.map(item => {
                if (item.type === 'slide') {
                    return <MainSlider key={item.id} />;
                } else if (item.type === 'product-catalog') {
                    return (
                        <FeaturedProductSlider
                            key={item.id}
                            products={item.products ?? []}
                            title={item.catalogName ?? ''}
                        />
                    );
                }
            })}
        </>
    );
});

if (isDev) {
    Home.displayName = 'Home';
}

export default Home;

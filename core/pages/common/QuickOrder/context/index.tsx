import {
    createContext,
    FC,
    memo,
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {base64, isDev, jsonRequest, slugifyProduct} from '@core/helpers';
import {useRouter} from 'next/router';
import {Campaign, Product, ProductOption, SelectedProduct} from '@core/types';
import {useCart} from '@core/hooks';
import {
    getInitialSelectedProduct,
    getProductOptions,
    getSelectedProduct,
    prepareCartItem
} from './utils';
import {QuickOrderContext as ContextType, Status} from './types';

export const QuickOrderContext = createContext<ContextType>(null as any);

export const QuickOrderProvider: FC<{children: React.ReactNode}> = memo(
    ({children}) => {
        const [product, setProduct] = useState<Product>();
        const [categoryProducts, setCategoryProducts] = useState<Product[]>([]);
        const [selectedProduct, setSelectedProduct] =
            useState<SelectedProduct>();
        const [campaigns, setCampaigns] = useState<Campaign[]>([]);
        const [status, setStatus] = useState<Status>('pending');
        const [categoryStatus, setCategoryStatus] = useState<Status>('pending');
        const [isAddToCartInProgress, setIsAddToCartInProgress] =
            useState(false);
        const [isVariantChange, setIsVariantChange] = useState(false);
        const [productOptions, setProductOptions] = useState<
            ProductOption[] | undefined
        >(undefined);

        const {cart, addItem, updateItem} = useCart();

        const router = useRouter();

        const {processedSlug, selectedAttributes} = slugifyProduct(
            (router.query.product as string) ?? ''
        );

        useEffect(() => {
            (async () => {
                try {
                    if (isVariantChange) return;

                    if ('product' in router.query) {
                        const result = await jsonRequest({
                            url: '/api/catalog/product',
                            method: 'POST',
                            data: {slug: processedSlug}
                        });

                        setProduct(result.product);
                        setCampaigns(result.campaigns);
                        setCategoryStatus('resolved');
                    } else {
                        const initialProducts = await jsonRequest({
                            url: '/api/catalog/products',
                            method: 'POST',
                            data: {limit: 10}
                        });
                        setCategoryProducts(initialProducts.products);
                        setProduct(undefined);
                        setCampaigns([]);
                        setCategoryStatus('resolved');
                    }
                    setStatus('resolved');
                } catch (err: any) {
                    setStatus('rejected');
                    setCategoryStatus('rejected');
                }
            })();
        }, [router.query, processedSlug, isVariantChange]);

        const availableQuantity = useMemo(() => {
            if (selectedProduct === undefined) return 0;

            let availableQuantity = selectedProduct.availableQuantity;

            for (const cartItem of cart.items ?? []) {
                if (cartItem.productId === selectedProduct.productId) {
                    availableQuantity -= cartItem.quantity;
                }
            }

            return availableQuantity;
        }, [cart, selectedProduct]);

        const inStock = useMemo(() => {
            if (availableQuantity === undefined) return false;
            return availableQuantity > 0;
        }, [availableQuantity]);

        const setQuantity = useCallback(
            (quantity: number) => {
                quantity = parseFloat(quantity as any);

                if (selectedProduct === undefined) return;

                if (!isNaN(quantity)) {
                    setSelectedProduct({
                        ...selectedProduct,
                        quantity: Math.max(
                            1,
                            Math.min(
                                quantity,
                                selectedProduct?.availableQuantity
                            )
                        )
                    });
                }
            },
            [selectedProduct]
        );

        useEffect(() => {
            if (product === undefined) return;

            setSelectedProduct(getInitialSelectedProduct(product));

            setProductOptions(() => {
                const initialSelectedProduct = getInitialSelectedProduct(
                    product,
                    selectedAttributes
                );

                let productOptions = [] as ProductOption[];

                if (
                    Array.isArray(product.options) &&
                    product.options.length > 0
                ) {
                    const firstOption = product.options[0];

                    if (firstOption.type === 'color') {
                        productOptions = getProductOptions(
                            product,
                            firstOption.code,
                            initialSelectedProduct.attributes![firstOption.code]
                        );
                    } else {
                        productOptions = getProductOptions(product);
                    }
                }

                return productOptions;
            });
            // eslint-disable-next-line
        }, [product]);

        const setAttribute = useCallback(
            (code: string, value: string) => {
                if (product === undefined) return;
                setIsVariantChange(true);

                const newAttributes = {
                    ...selectedProduct?.attributes,
                    [code]: value
                };
                const variants = (product?.variants ?? []).filter(variant => {
                    return variant.attributes[code] === value;
                });

                let variant = variants.find(variant => {
                    for (const newAttributesCode of Object.keys(
                        newAttributes
                    )) {
                        if (
                            newAttributes[newAttributesCode] !==
                            variant.attributes[newAttributesCode]
                        ) {
                            return false;
                        }
                    }

                    return true;
                });
                if (!variant && variants.length > 0) {
                    variant = variants[0];
                }

                if (!!variant) {
                    const option = product?.options.find(
                        option => option.code == code
                    );

                    setSelectedProduct(
                        getSelectedProduct({
                            product,
                            variant,
                            selectedProduct
                        })
                    );

                    if (option?.type === 'color') {
                        setProductOptions(
                            getProductOptions(product, code, value)
                        );

                        router.push({
                            query: {
                                product: `${product.slug}-${base64.encode(
                                    new URLSearchParams({
                                        ...newAttributes,
                                        _es: 'true'
                                    }).toString()
                                )}`
                            }
                        });
                    } else {
                        router.push({
                            query: {
                                product: `${product.slug}-${base64.encode(
                                    new URLSearchParams({
                                        ...newAttributes,
                                        _es: 'true'
                                    }).toString()
                                )}`
                            }
                        });
                    }
                }
            },
            [product, selectedProduct, router]
        );

        const addToCart = useCallback(() => {
            if (isAddToCartInProgress || selectedProduct === undefined) {
                return;
            }

            setIsAddToCartInProgress(true);

            (async () => {
                const cartItem = prepareCartItem({
                    selectedProduct,
                    productOptions
                });

                const inCartProduct = cart.items.find(
                    cartItem => cartItem.productId === selectedProduct.productId
                );

                if (inCartProduct) {
                    await updateItem({
                        ...cartItem,
                        quantity: cartItem.quantity + inCartProduct.quantity
                    });
                } else {
                    await addItem(cartItem);
                }

                setQuantity(1);
                setIsAddToCartInProgress(false);
            })();
        }, [
            isAddToCartInProgress,
            selectedProduct,
            setQuantity,
            addItem,
            updateItem,
            cart,
            productOptions
        ]);

        const prevSlug = usePrevious(processedSlug);

        useEffect(() => {
            if (prevSlug !== processedSlug) {
                setIsVariantChange(false);
            }
        }, [prevSlug, processedSlug]);

        const seo = useMemo(() => {
            const seo: Record<string, any> = {};

            if (selectedProduct === undefined || product === undefined)
                return {};

            seo.params = {title: selectedProduct.name};

            return seo;
        }, [product, selectedProduct]);

        const value = useMemo(
            () => ({
                product,
                categoryProducts,
                status,
                selectedProduct,
                isAddToCartInProgress,
                availableQuantity,
                inStock,
                seo,
                productOptions,
                categoryStatus,
                campaigns,

                addToCart,
                setQuantity,
                setStatus,
                setProduct,
                setCategoryProducts,
                setAttribute,
                setIsVariantChange,
                setCategoryStatus
            }),
            [
                product,
                categoryProducts,
                status,
                selectedProduct,
                isAddToCartInProgress,
                availableQuantity,
                inStock,
                seo,
                productOptions,
                categoryStatus,
                campaigns,

                addToCart,
                setQuantity,
                setAttribute
            ]
        );

        return (
            <QuickOrderContext.Provider value={value}>
                {children}
            </QuickOrderContext.Provider>
        );
    }
);

if (isDev) {
    QuickOrderProvider.displayName = 'OrderProvider';
}

export function useQuickOrder() {
    const quickOrderContext = useContext(QuickOrderContext);

    if (quickOrderContext === undefined) {
        throw new Error(
            'useQuickOrder must be used inside QuickOrderProvider.'
        );
    }

    return quickOrderContext;
}

function usePrevious<T>(value: T) {
    const ref = useRef<T>();
    useEffect(() => {
        ref.current = value;
    });
    return ref.current;
}

import {Campaign, Product, ProductOption, SelectedProduct} from '@core/types';
import {Dispatch, SetStateAction} from 'react';

export type Status = 'pending' | 'resolved' | 'rejected';

export type QuickOrderContext = {
    product: Product | undefined;
    setProduct: Dispatch<SetStateAction<Product | undefined>>;
    categoryProducts: Product[];
    setCategoryProducts: Dispatch<SetStateAction<Product[]>>;
    status: Status;
    setStatus: Dispatch<SetStateAction<Status>>;
    categoryStatus: Status;
    setCategoryStatus: Dispatch<SetStateAction<Status>>;
    selectedProduct: SelectedProduct | undefined;
    isAddToCartInProgress: boolean;
    availableQuantity: number;
    inStock: boolean;
    seo: Record<string, any>;
    addToCart: () => void;
    setQuantity: (quantity: number) => void;
    setAttribute: (code: string, value: string) => void;
    productOptions: ProductOption[] | undefined;
    setIsVariantChange: Dispatch<SetStateAction<boolean>>;
    campaigns: Campaign[];
};

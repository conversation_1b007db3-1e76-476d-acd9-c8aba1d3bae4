import {FC, Fragment, memo, useMemo} from 'react';
import {cls, isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiListBox, UiTransition} from '@core/components/ui';
import {ChevronDownIcon, ChevronUpIcon} from '@core/icons/solid';
import {useQuickOrder} from '../context';

type OtherOptionsProps = {
    code: string;
    label: string;
    selections: {
        value: string;
        color?: string;
        inStock?: boolean;
    }[];
};

const OtherOptions: FC<OtherOptionsProps> = memo(props => {
    const {code, label, selections} = props;
    const t = useTrans();

    const {selectedProduct, setAttribute} = useQuickOrder();

    const value = useMemo(
        () => (selectedProduct?.attributes ?? {})[code],
        [selectedProduct, code]
    );

    const options = useMemo(
        () =>
            selections.map(selection => ({
                ...selection,
                value: selection.value,
                label: selection.value
            })),
        [selections]
    );

    const selectedLabel = useMemo(() => {
        const option = options.find(option => option.value === value);

        if (!!option) {
            return option.label;
        }

        return '';
    }, [options, value]);

    return (
        <>
            <h3 className="text-sm font-medium text-default">{label}</h3>

            <div className="mt-4 flex items-center space-x-3">
                <UiListBox
                    value={value}
                    onChange={(value: string) => setAttribute(code, value)}
                    as="div"
                    className="relative space-y-1"
                    style={{minWidth: '180px'}}
                >
                    {({open}) => (
                        <>
                            <UiListBox.Button
                                className={cls(
                                    'relative inline-flex h-8 w-full cursor-pointer appearance-none items-center rounded border bg-gray-100 px-3 py-0 pr-6 text-sm shadow-sm transition focus:outline-none',
                                    {
                                        'border-primary-600 !bg-white ring-1 ring-primary-600':
                                            open
                                    }
                                )}
                            >
                                {!value && (
                                    <span className="truncate text-sm text-muted">
                                        {t('Choose a sort criteria.')}
                                    </span>
                                )}
                                {!!value && (
                                    <span className="truncate text-sm">
                                        {selectedLabel}
                                    </span>
                                )}
                                <span className="pointer-events-none absolute right-2 ml-3 flex items-center">
                                    {open ? (
                                        <ChevronUpIcon
                                            className="h-3 w-3 text-primary-600"
                                            aria-hidden="true"
                                        />
                                    ) : (
                                        <ChevronDownIcon
                                            className="h-3 w-3 text-muted"
                                            aria-hidden="true"
                                        />
                                    )}
                                </span>
                            </UiListBox.Button>

                            <UiTransition
                                show={open}
                                as={Fragment}
                                enter="transition duration-150 ease-in-out"
                                enterFrom="transform scale-95 opacity-0"
                                enterTo="transform scale-100 opacity-100"
                                leave="transition duration-150 ease-in-out"
                                leaveFrom="transform scale-100 opacity-100"
                                leaveTo="transform scale-95 opacity-0"
                            >
                                <UiListBox.Options
                                    static
                                    className="absolute left-0 z-40 mt-2 max-h-64 w-full origin-top-left overflow-auto rounded border bg-white p-1.5 shadow-sm outline-none"
                                >
                                    {options.map(option => (
                                        <UiListBox.Option
                                            className="relative"
                                            key={option.value}
                                            value={option.value}
                                        >
                                            {({active, selected, disabled}) => (
                                                <button
                                                    disabled={disabled}
                                                    aria-disabled={disabled}
                                                    className={cls(
                                                        'flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal focus:outline-none',
                                                        active && 'bg-gray-100'
                                                    )}
                                                >
                                                    <span
                                                        className={cls(
                                                            'block flex-1 truncate',
                                                            selected
                                                                ? 'font-medium'
                                                                : 'font-normal'
                                                        )}
                                                    >
                                                        {t(option.label)}
                                                    </span>
                                                    {selected && (
                                                        <span
                                                            className="absolute -left-1 h-6 rounded-full bg-primary-600"
                                                            style={{
                                                                width: 2
                                                            }}
                                                        ></span>
                                                    )}
                                                </button>
                                            )}
                                        </UiListBox.Option>
                                    ))}
                                </UiListBox.Options>
                            </UiTransition>
                        </>
                    )}
                </UiListBox>
            </div>
        </>
    );
});

if (isDev) {
    OtherOptions.displayName = 'OtherOptions';
}

export default OtherOptions;

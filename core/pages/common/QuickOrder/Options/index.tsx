import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useQuickOrder} from '../context';

import ImageOptions from './ImageOptions';
import ColorOptions from './ColorOptions';
import SizeOptions from './SizeOptions';
import OtherOptions from './OtherOptions';

const Options: FC = memo(() => {
    const {productOptions} = useQuickOrder();

    return Array.isArray(productOptions) && productOptions.length > 0 ? (
        <div className="mb-4 space-y-8 border-b pb-4">
            {productOptions?.map(option => (
                <div key={option.code}>
                    {option.type === 'color' && option.showVariantImage && (
                        <ImageOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                    {option.type === 'color' && !option.showVariantImage && (
                        <ColorOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                    {option.type === 'size' && (
                        <SizeOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                    {option.type === 'other' && (
                        <OtherOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                </div>
            ))}
        </div>
    ) : null;
});

if (isDev) {
    Options.displayName = 'Options';
}

export default Options;

import {FC, memo, useMemo, useRef} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiImage} from '@core/components/ui';
import {useQuickOrder} from '../context';

type ImageOptionsProps = {
    code: string;
    label: string;
    selections: {
        value: string;
        color?: string;
        image?: string;
        inStock?: boolean;
    }[];
};

const ImageOptions: FC<ImageOptionsProps> = memo(props => {
    const {code, label, selections} = props;

    const {selectedProduct, setAttribute} = useQuickOrder();

    const value = useMemo(
        () => (selectedProduct?.attributes ?? {})[code],
        [selectedProduct, code]
    );

    const currentValue = useRef(value);

    function selectionChangeHandler(value: string) {
        setAttribute(code, value);
        currentValue.current = value;

        const container = document.querySelector(
            '.mobile-product-content-wrapper'
        );
        if (container === null || container === undefined) return;

        container.scrollTo({top: 0, behavior: 'smooth'});
    }

    return (
        <>
            <div className="flex items-center">
                <h3 className="text-sm font-medium text-default">{label}:</h3>
                <div className="ml-1.5 text-sm text-muted">{value}</div>
            </div>

            <div className="-m-1.5 mt-2.5">
                <div className="flex select-none flex-wrap items-center">
                    {selections.map(selection => (
                        <button
                            key={selection.value}
                            onClick={() => {
                                selectionChangeHandler(selection.value);
                            }}
                            className={cls('relative m-1.5 h-20 w-12 rounded', {
                                'ring-1 ring-primary-600':
                                    selection.value === value ||
                                    currentValue.current === selection.value
                            })}
                        >
                            <UiImage
                                className="rounded"
                                src={`${selection.image}?w=180&q=50`}
                                fit="cover"
                                position="center"
                                alt=""
                                fill
                            />
                        </button>
                    ))}
                </div>
            </div>
        </>
    );
});

if (isDev) {
    ImageOptions.displayName = 'ImageOptions';
}

export default ImageOptions;

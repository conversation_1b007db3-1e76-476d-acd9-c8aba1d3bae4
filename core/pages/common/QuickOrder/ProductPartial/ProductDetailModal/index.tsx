import {useMobile, useTrans, useUI} from '@core/hooks';
import {TagsIcon} from '@core/icons/solid';
import {useQuickOrder} from '../../context';
import Details from './Details';

const ProductDetailModal = () => {
    const {selectedProduct} = useQuickOrder();
    const t = useTrans();
    const {openSideBar, openModal} = useUI();
    const {isMobile} = useMobile();

    if (selectedProduct === undefined) return null;

    const productDetailHandler = () => {
        if (isMobile) {
            openSideBar(
                t('Product Detail'),
                <Details selectedProduct={selectedProduct} />
            );
        } else {
            openModal(
                t('Product Detail'),
                <Details selectedProduct={selectedProduct} />
            );
        }
    };

    const hasDetail =
        (selectedProduct.description?.length ?? 0) !== 0 &&
        selectedProduct.features.length !== 0;

    return hasDetail ? (
        <div className="relative">
            <button
                onClick={productDetailHandler}
                className="featured-button absolute -top-2 right-0 gap-1.5 text-xs leading-3"
            >
                <TagsIcon className="h-3.5 w-3.5" />
                <span className="font-medium">{t('Details')}</span>
            </button>
        </div>
    ) : null;
};

export default ProductDetailModal;

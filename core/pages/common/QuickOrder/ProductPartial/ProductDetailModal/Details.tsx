import {FC, memo} from 'react';
import {SelectedProduct} from '@core/types';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';

type DetailsProps = {
    selectedProduct: SelectedProduct;
};

const Details: FC<DetailsProps> = memo(({selectedProduct}) => {
    const t = useTrans();

    return (
        <div className="grid gap-4 border-t p-4">
            <div className="flex items-center justify-between gap-4">
                <p className="text-xl font-medium">{selectedProduct.name}</p>
            </div>
            <div
                className="prose max-w-none text-sm"
                dangerouslySetInnerHTML={{
                    __html: selectedProduct.description ?? ''
                }}
            />
            {selectedProduct.features.length > 0 && (
                <>
                    <p className="font-medium">{t('Product Features')}</p>
                    <div className="grid gap-2.5 md:grid-cols-2 md:gap-4">
                        {selectedProduct.features.map((feature, index) => (
                            <div
                                className="button-primary flex cursor-default justify-between px-4 py-2.5 outline outline-1 outline-transparent transition-all hover:border-primary-600 hover:bg-secondary-100 hover:outline-primary-600"
                                key={feature.code + index}
                            >
                                <div className="font-medium">
                                    {feature.label}
                                </div>
                                <div className="text-muted">
                                    {feature.value}
                                </div>
                            </div>
                        ))}
                    </div>
                </>
            )}
        </div>
    );
});

if (isDev) {
    Details.displayName = 'Details';
}

export default Details;

import {useCallback} from 'react';
import {UiCheckbox} from '@core/components/ui';
import {useCart, useTrans} from '@core/hooks';
import {TrashCanIcon} from '@core/icons/solid';
import {CartItem} from '@core/types';
import Price from '@components/common/Price';
import {BagIcon} from '@core/icons/solid';

const Cart = () => {
    const {cart, updateItem, removeItem} = useCart();

    const t = useTrans();

    const onSelectionChange = useCallback(
        (item: CartItem) => updateItem({...item, selected: !item.selected}),
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );
    const onRemoveItem = useCallback(
        (item: CartItem) => removeItem({...item}),
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    return (
        <div className="grid gap-4 px-4">
            <div className="inline-block min-w-full rounded-lg border bg-white">
                <div className="block max-w-full overflow-hidden rounded-lg">
                    <table className="w-full text-left text-xs text-gray-700">
                        <caption className="py-2 font-semibold text-base text-primary-600">
                            {t('My Order List')}
                        </caption>
                        <thead className="bg-secondary-100">
                            <tr className="divide-x">
                                <td className="w-10 select-none p-2 text-xs font-semibold text-gray-700"></td>
                                <td className="select-none p-2 text-xs font-semibold text-gray-700">
                                    {t('Product Name')}
                                </td>
                                <td className="select-none p-2 text-xs font-semibold text-gray-700">
                                    {t('Quantity')}
                                </td>
                                <td className="select-none p-2 text-xs font-semibold text-gray-700">
                                    {t('Price')}
                                </td>
                                <td className="w-10 select-none p-2 text-xs font-semibold text-gray-700"></td>
                            </tr>
                        </thead>
                        <tbody className="relative divide-y bg-white">
                            {Array.isArray(cart.items) &&
                            cart.items.length > 0 ? (
                                cart.items.map(cartItem => (
                                    <tr key={cartItem.productId}>
                                        <td>
                                            <div className="flex items-center justify-center">
                                                <UiCheckbox
                                                    checked={cartItem.selected}
                                                    onChange={() =>
                                                        onSelectionChange(
                                                            cartItem
                                                        )
                                                    }
                                                />
                                            </div>
                                        </td>
                                        <td className="p-2">
                                            {cartItem.productName}
                                        </td>
                                        <td className="p-2">
                                            {cartItem.quantity}
                                        </td>
                                        <td className="p-2">
                                            <Price price={cartItem.price} />
                                        </td>
                                        <td>
                                            <div
                                                className="flex cursor-pointer select-none items-center justify-center text-muted transition hover:text-danger-600"
                                                onClick={() =>
                                                    onRemoveItem(cartItem)
                                                }
                                            >
                                                <TrashCanIcon className="h-4 w-4" />
                                            </div>
                                        </td>
                                    </tr>
                                ))
                            ) : (
                                <tr>
                                    <td colSpan={6}>
                                        <div className="grid place-items-center p-6">
                                            <div className="flex h-20 w-20 items-center justify-center rounded-lg border border-dashed text-primary-600">
                                                <BagIcon className="h-7 w-7" />
                                            </div>

                                            <h2 className="mt-2 text-center text-lg font-semibold">
                                                {t(
                                                    'There are no items in your cart!'
                                                )}
                                            </h2>

                                            <p className="mt-2 px-10 text-center text-sm text-muted">
                                                {t(
                                                    'You can add products to your cart from the detail page of the products on our site.'
                                                )}
                                            </p>
                                        </div>
                                    </td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
};

export default Cart;

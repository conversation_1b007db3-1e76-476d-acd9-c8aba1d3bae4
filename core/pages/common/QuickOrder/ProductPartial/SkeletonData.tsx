const SkeletonData = () => {
    return (
        <div className="flex w-full flex-col items-start justify-center gap-4 px-4 [&>*]:w-full">
            <div className="skeleton-card h-7"></div>
            <div className="grid gap-4">
                <div className="flex flex-col gap-4 md:flex-row">
                    <div className="skeleton-card aspect-1 h-48 xl:w-1/2"></div>
                    <div className="flex w-full flex-col gap-1.5">
                        <div className="skeleton-card h-5 w-36"></div>
                        <div className="skeleton-card h-5 w-28"></div>
                        <div className="skeleton-card h-5 w-24"></div>
                        <div className="skeleton-card h-5 w-36"></div>
                        <div className="skeleton-card h-5 w-40"></div>
                        <div className="skeleton-card h-5 w-28"></div>
                        <div className="skeleton-card h-5 w-36"></div>
                        <div className="skeleton-card h-5 w-40"></div>
                    </div>
                </div>
            </div>
            <div className="skeleton-card mt-4 h-28"></div>
        </div>
    );
};

export default SkeletonData;

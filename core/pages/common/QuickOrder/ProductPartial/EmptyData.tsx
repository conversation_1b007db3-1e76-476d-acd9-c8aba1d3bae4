import {useTrans} from '@core/hooks';
import {InboxIcon} from '@core/icons/solid';

const EmptyData = () => {
    const t = useTrans();

    return (
        <div className="grid place-items-center px-4 py-12 xl:py-24">
            <div className="flex h-24 w-24 items-center justify-center rounded-lg border border-dashed border-gray-500 text-muted">
                <InboxIcon className="h-8 w-8" />
            </div>

            <h2 className="pt-8 text-center text-2xl font-semibold">
                {t('No products found!')}
            </h2>

            <p className="px-10 pt-2 text-center text-muted">
                {t(
                    'Please change your search criteria and try again. If still not finding anything relevant, please visit the Home page and try out some of our bestsellers!'
                )}
            </p>
        </div>
    );
};

export default EmptyData;

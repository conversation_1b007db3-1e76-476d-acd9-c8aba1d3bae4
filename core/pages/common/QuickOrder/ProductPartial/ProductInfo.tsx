import {useTrans} from '@core/hooks';
import Price from '@components/common/Price';
import Options from '../Options';
import ImageGallery from './ImageGallery';
import {useQuickOrder} from '../context';
import ProductDetailModal from './ProductDetailModal';

const ProductInfo = () => {
    const {product, selectedProduct} = useQuickOrder();

    const t = useTrans();

    if (product === undefined || selectedProduct === undefined) return null;

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-start justify-between border-b px-4 pb-2">
                <h2 className="font-medium">{product.name}</h2>
                <ProductDetailModal />
            </div>

            <div className="grid grid-cols-12 gap-4 px-4">
                <div className="relative col-span-12 aspect-1 md:col-span-4">
                    <ImageGallery />
                </div>

                <div className="col-span-12 md:col-span-8">
                    <Options />

                    {typeof selectedProduct.salesPriceAtCart === 'number' &&
                        selectedProduct.salesPriceAtCart > 0 && (
                            <div className="mt-4 flex w-fit flex-col items-center gap-1 rounded border-2 border-primary-600 p-1.5 font-medium">
                                <div className="flex items-center gap-2 rounded bg-primary-100 px-12 py-1 text-sm text-primary-600">
                                    <svg
                                        className="h-[10px] w-4 stroke-primary-600 stroke-[1px] [&>path]:fill-primary-600"
                                        viewBox="0 0 16 10"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path d="M10.278 9.469h4.607a.526.526 0 0 0 .218-.044.621.621 0 0 0 .37-.577v-5.83h-1.17v4.297L9.86 2.438a.55.55 0 0 0-.807-.024L5.97 5.504 1.47.681l-.822.884L5.53 6.809a.55.55 0 0 0 .81.02l3.108-3.085 4.074 4.479h-3.244v1.246Z" />
                                    </svg>
                                    <p>{t('Price on Cart')}</p>
                                </div>
                                <Price
                                    price={selectedProduct.salesPriceAtCart}
                                    dontWrapDiscountedPrice
                                    className="text-xl [&>span]:text-primary-600"
                                />
                            </div>
                        )}

                    <div
                        className="prose max-w-none text-sm"
                        dangerouslySetInnerHTML={{
                            __html: selectedProduct.shortDescription ?? ''
                        }}
                    />
                </div>
            </div>
        </div>
    );
};

export default ProductInfo;

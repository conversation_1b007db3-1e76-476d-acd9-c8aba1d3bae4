import {useQuickOrder} from '../context';
import EmptyData from './EmptyData';
import SkeletonData from './SkeletonData';
import ProductInfo from './ProductInfo';
import Cart from './Cart';

const ProductPartial = () => {
    const {status, product} = useQuickOrder();

    return (
        <section className="card-container col-span-12 flex flex-1 items-start bg-white py-4 xl:col-span-6 [&>*]:w-full">
            {status === 'pending' && <SkeletonData />}
            {status === 'rejected' && <EmptyData />}
            {status === 'resolved' &&
                (product === undefined ? (
                    <EmptyData />
                ) : (
                    <div className="grid gap-8">
                        <ProductInfo />
                        <Cart />
                    </div>
                ))}
        </section>
    );
};

export default ProductPartial;

import {useQuickOrder} from '../context';
import {useTrans} from '@core/hooks';
import {UiButton} from '@core/components/ui';
import {BagIcon} from '@core/icons/solid';
import Price from '@components/common/Price';
import Quantity from '@components/common/Quantity';

const ProductCartSidebar = () => {
    const {
        addToCart,
        setQuantity,
        isAddToCartInProgress,
        selectedProduct,
        availableQuantity,
        inStock
    } = useQuickOrder();

    const t = useTrans();

    if (selectedProduct === undefined) return null;

    return (
        <div className="card-container p-4">
            <div className="grid gap-2">
                <div className="flex gap-2">
                    <Quantity
                        availableQuantity={availableQuantity}
                        quantity={selectedProduct.quantity}
                        onChange={quantity => setQuantity(quantity)}
                        size="sm"
                    />
                </div>

                <UiButton
                    className="w-full"
                    variant="solid"
                    color="primary"
                    size="md"
                    leftIcon={<BagIcon className="mr-3 h-5 w-5" />}
                    loading={isAddToCartInProgress}
                    disabled={!inStock}
                    onClick={() => addToCart()}
                >
                    {t('ADD TO CART')}
                </UiButton>
            </div>

            <ul className="mt-4 select-none divide-y text-sm [&>*:last-child]:pb-0 [&>li]:flex [&>li]:justify-between [&>li]:py-2">
                <li>
                    {t('Undiscounted Sales Price')}:
                    <Price
                        className="font-semibold"
                        price={selectedProduct.unDiscountedSalesPrice ?? 0}
                    />
                </li>
                <li>
                    {t('Sales Price at Cart')}:
                    <Price
                        className="font-semibold"
                        price={selectedProduct.salesPriceAtCart ?? 0}
                    />
                </li>
                <li>
                    {t('Sales Price')}:
                    <Price
                        className="font-semibold"
                        price={selectedProduct.salesPrice}
                    />
                </li>
            </ul>
        </div>
    );
};

export default ProductCartSidebar;

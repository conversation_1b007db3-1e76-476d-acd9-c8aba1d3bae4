import {useMemo} from 'react';
import {useCart, useTrans} from '@core/hooks';
import Price from '@components/common/Price';
import {useRouter} from 'next/router';
import {UiButton} from '@core/components/ui';
import {ArrowRightIcon} from '@core/icons/solid';

const CartSummary = () => {
    const t = useTrans();
    const router = useRouter();
    const {cart} = useCart();

    const isProceedToCheckOutEnabled = useMemo(
        () =>
            cart.items.filter(
                item =>
                    item.selected &&
                    !item.removed &&
                    item.quantity > 0 &&
                    item.productStockQuantity >= item.quantity
            ).length > 0 && cart.subTotal > 0,
        [cart]
    );

    return (
        <>
            <div className="card-container select-none p-4">
                <h2 id="summary-heading" className="font-medium">
                    {t('Order Summary')}
                </h2>

                <div className="mt-4 divide-y">
                    <div className="flex items-center justify-between pb-2 text-sm">
                        <div>{t('Products total')}</div>
                        <Price className="font-medium" price={cart.subTotal} />
                    </div>
                    <div className="flex items-center justify-between py-2 text-sm">
                        <div>{t('Tax estimate')}</div>
                        <Price className="font-medium" price={cart.taxTotal} />
                    </div>
                    <div className="flex items-center justify-between py-2 text-sm">
                        <div>{t('Delivery estimate')}</div>
                        <Price
                            className="font-medium"
                            price={cart.deliveryTotal}
                        />
                    </div>
                    <div className="flex items-center justify-between pt-2 text-base">
                        {/* {typeof cart.discountTotalIncludingProductDiscounts ===
                            'number' &&
                            cart.discountTotalIncludingProductDiscounts > 0 && (
                                <div className="flex flex-col items-center justify-center rounded-md bg-primary-100 px-2 py-1.5 text-xs font-medium text-primary-600">
                                    <p className="text-muted">
                                        {t('Total Discount')}
                                    </p>
                                    <Price
                                        price={
                                            cart.discountTotalIncludingProductDiscounts
                                        }
                                    />
                                </div>
                            )} */}
                        <Price
                            className="ml-auto font-semibold"
                            price={cart.grandTotal}
                        />
                    </div>
                </div>
            </div>

            <UiButton
                className="w-full"
                variant="solid"
                color="primary"
                size="md"
                disabled={!isProceedToCheckOutEnabled}
                leftIcon={<ArrowRightIcon className="mr-2 h-5 w-5" />}
                onClick={() => router.push(`/checkout?t=${Date.now()}`)}
            >
                {t('Proceed to Checkout')}
            </UiButton>
        </>
    );
};

export default CartSummary;

import {useQuickOrder} from '../context';
import CartSummary from './CartSummary';
import EmptyData from './EmptyData';
import SkeletonData from './SkeletonData';
import ProductCartSidebar from './ProductCartSidebar';

const SidebarPartial = () => {
    const {status, product} = useQuickOrder();

    return (
        <aside className="col-span-12 grid gap-4 xl:col-span-3">
            {status === 'pending' && <SkeletonData />}
            {status === 'rejected' && <EmptyData />}
            {status === 'resolved' &&
                (product === undefined ? (
                    <EmptyData />
                ) : (
                    <>
                        <ProductCartSidebar />
                        <CartSummary />
                    </>
                ))}
        </aside>
    );
};

export default SidebarPartial;

import Price from '@components/common/Price';
import Quantity from '@components/common/Quantity';
import {UiButton} from '@core/components/ui';
import {useTrans} from '@core/hooks';
import {BagIcon} from '@core/icons/solid';

const EmptyData = () => {
    const t = useTrans();

    return (
        <div className="card-container grid gap-2 p-4">
            <Quantity
                availableQuantity={0}
                quantity={1}
                onChange={() => {}}
                size="sm"
            />

            <UiButton
                className="w-full"
                variant="solid"
                color="primary"
                size="md"
                leftIcon={<BagIcon className="mr-3 h-5 w-5" />}
                disabled
            >
                {t('ADD TO CART')}
            </UiButton>

            <ul className="select-none divide-y text-sm [&>*:last-child]:pb-0 [&>li]:flex [&>li]:justify-between [&>li]:py-2">
                <li>
                    {t('Undiscounted Sales Price')}:
                    <Price price={0} />
                </li>
                <li>
                    {t('Sales Price at Cart')}:
                    <Price price={0} />
                </li>
                <li>
                    {t('Sales Price')}:
                    <Price price={0} />
                </li>
            </ul>
        </div>
    );
};

export default EmptyData;

const SkeletonData = () => {
    return (
        <div className="grid gap-4">
            <div className="card-container grid gap-2 p-4">
                <div className="grid grid-cols-2 gap-2">
                    <div className="skeleton-card h-9"></div>
                    <div className="skeleton-card h-9"></div>
                </div>
                <div className="skeleton-card h-9"></div>
                <ul>
                    {new Array(4).fill(Boolean).map((_, index) => (
                        <li key={index} className="skeleton-card my-2 h-7"></li>
                    ))}
                </ul>
            </div>
            <div className="card-container grid gap-2 p-4">
                <div className="skeleton-card h-7"></div>
                <ul>
                    {new Array(3).fill(Boolean).map((_, index) => (
                        <li key={index} className="skeleton-card my-2 h-7"></li>
                    ))}
                </ul>
                <div className="skeleton-card ml-auto h-7 w-20"></div>
            </div>
            <div className="skeleton-card h-8"></div>
        </div>
    );
};

export default SkeletonData;

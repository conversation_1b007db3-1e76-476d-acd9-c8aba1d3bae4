import {
    ChangeEvent<PERSON><PERSON><PERSON>,
    FC,
    FocusEventHandler,
    Fragment,
    KeyboardEventHandler,
    memo,
    useCallback,
    useEffect,
    useRef,
    useState
} from 'react';
import {cls, isDev, debounce, jsonRequest, trim} from '@core/helpers';
import {useElementSize, useTrans} from '@core/hooks';
import {ProductSearchResultItem} from '@core/types';
import {
    UiClickOutside,
    UiInput,
    UiLink,
    UiSpinner,
    UiTransition
} from '@core/components/ui';
import {SearchIcon} from '@core/icons/regular';
import {useQuickOrder} from '../context';
import {QrCodeIcon} from '@core/icons/solid';

const SearchBar: FC = memo(() => {
    const t = useTrans();
    const [searchResult, setSearchResult] = useState<ProductSearchResultItem[]>(
        []
    );
    const [searchQuery, setSearchQuery] = useState('');
    const [isResultShown, setIsResultShown] = useState(false);
    const [isBlurInProgress, setIsBlurInProgress] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const {ref: containerRef, width: containerWidth} = useElementSize();
    const blurTimeoutIdx = useRef<any>();
    const inputRef = useRef<HTMLInputElement>(null);

    const {setCategoryProducts, setCategoryStatus, setStatus} = useQuickOrder();

    const searchDebounced = useRef(
        debounce(
            async (query: string) => {
                query = trim(query);

                try {
                    if (!(query === '' || query.length < 2)) {
                        const result = await jsonRequest({
                            url: '/api/catalog/search',
                            method: 'POST',
                            data: {searchQuery: query}
                        });

                        setSearchResult(() =>
                            result.map((item: ProductSearchResultItem) => ({
                                ...item,
                                name: !!item.brandName
                                    ? `${item.brandName} ${item.name}`
                                    : item.name
                            }))
                        );
                    } else {
                        setSearchResult([]);
                    }
                } catch (err) {}
                setIsLoading(false);
            },
            300,
            {leading: false, trailing: true}
        )
    );
    const onSearch: ChangeEventHandler<HTMLInputElement> = useCallback(e => {
        const query = e.target.value;

        setIsResultShown(true);
        setIsLoading(true);
        searchDebounced.current(query);
        setSearchQuery(query);
    }, []);
    const onFocus: FocusEventHandler<HTMLInputElement> = () => {
        setIsBlurInProgress(false);
        clearTimeout(blurTimeoutIdx.current);
    };
    const onBlur: FocusEventHandler<HTMLInputElement> = () => {
        setIsBlurInProgress(true);
    };
    useEffect(() => {
        if (isBlurInProgress) {
            blurTimeoutIdx.current = setTimeout(() => {
                setIsBlurInProgress(false);
            }, 200);
        }

        return () => {
            clearTimeout(blurTimeoutIdx.current);
        };
    }, [isBlurInProgress]);
    const onSearchItemClick = useCallback(() => {
        if (trim(searchQuery).length > 2) {
            jsonRequest({
                url: '/api/catalog/save-popular-search',
                method: 'POST',
                data: {searchQuery}
            });
        }

        setStatus('pending');
        setIsResultShown(false);
        setSearchResult([]);
        setIsLoading(false);
        setSearchQuery('');
        setIsBlurInProgress(false);
        clearTimeout(blurTimeoutIdx.current);

        if (!!inputRef.current) {
            inputRef.current.blur();
        }
    }, [searchQuery, setStatus]);
    const onGoToSearchDetail = useCallback(async () => {
        setCategoryStatus('pending');
        try {
            if (trim(searchQuery).length > 0) {
                const searchedProducts = await jsonRequest({
                    url: '/api/catalog/products',
                    method: 'POST',
                    data: {search: searchQuery}
                });

                setCategoryProducts(searchedProducts.products);
                setCategoryStatus('resolved');
            }
        } catch (err) {
            setCategoryStatus('rejected');
        }

        setIsResultShown(false);
        setSearchResult([]);
        setIsLoading(false);
        setSearchQuery('');
        setIsBlurInProgress(false);
        clearTimeout(blurTimeoutIdx.current);

        if (!!inputRef.current) {
            inputRef.current.blur();
        }
    }, [searchQuery, setCategoryProducts, setCategoryStatus]);
    const onKeyDown: KeyboardEventHandler<HTMLInputElement> = useCallback(
        e => {
            if (e.code === 'Enter' || e.code === 'NumpadAdd') {
                onGoToSearchDetail();
            }
        },
        [onGoToSearchDetail]
    );

    return (
        <>
            <div className="flex w-full max-w-xl">
                <div
                    ref={containerRef}
                    className="flex w-full items-center bg-white"
                >
                    <UiClickOutside
                        active={isResultShown}
                        onClick={() => setIsResultShown(false)}
                    >
                        <div
                            className={cls(
                                'relative w-full rounded-lg bg-white pb-1',
                                {
                                    'z-dropdown rounded-b-none':
                                        isResultShown || isBlurInProgress
                                }
                            )}
                        >
                            <UiInput.Group size="md" className="group w-full">
                                <UiInput.LeftElement>
                                    <SearchIcon className="h-3 w-3 stroke-gray-700 stroke-[24px] text-gray-700" />
                                </UiInput.LeftElement>

                                <UiInput
                                    placeholder={t('Search product')}
                                    className="shadow-small rounded-lg bg-secondary-100 pl-12 text-sm transition placeholder:text-xs focus:!border-primary-600 focus:bg-secondary-100"
                                    value={searchQuery}
                                    onChange={onSearch}
                                    onFocus={onFocus}
                                    onBlur={onBlur}
                                    onKeyDown={onKeyDown}
                                />

                                {isLoading ? (
                                    <UiInput.RightElement>
                                        <UiSpinner size="sm" />
                                    </UiInput.RightElement>
                                ) : (
                                    <UiInput.RightElement>
                                        <QrCodeIcon className="h-5 w-5 fill-primary-600" />
                                    </UiInput.RightElement>
                                )}
                            </UiInput.Group>

                            <UiTransition
                                as={Fragment}
                                enter="transition duration-200 ease-in-out"
                                enterFrom="opacity-0"
                                enterTo="opacity-100"
                                leave="transition duration-200 ease-in-out"
                                leaveFrom="opacity-100"
                                leaveTo="opacity-0"
                                show={isResultShown}
                            >
                                <div
                                    className="absolute left-0 right-0 top-full z-dropdown flex origin-top flex-col rounded-b-lg bg-white shadow-[0_5px_11px_0_#202a3533]"
                                    style={{
                                        width: `${containerWidth}px`
                                    }}
                                >
                                    {searchResult.length > 0 ? (
                                        <div className="scroller max-h-96 w-full overflow-y-auto p-2">
                                            {searchResult.map(item => {
                                                if (item.type !== 'product')
                                                    return;

                                                return (
                                                    <UiLink
                                                        key={item.id}
                                                        className="hover:shadow-small group flex flex-shrink-0 cursor-pointer rounded-md border-0 p-2 transition hover:bg-secondary-100 focus:outline-none"
                                                        href={{
                                                            pathname: '',
                                                            query: {
                                                                product:
                                                                    item.slug
                                                            }
                                                        }}
                                                        onClick={
                                                            onSearchItemClick
                                                        }
                                                    >
                                                        <div className="ml-3 flex flex-1 flex-col justify-center">
                                                            <div className="mb-1 text-sm font-medium">
                                                                {item.name}
                                                            </div>
                                                        </div>
                                                    </UiLink>
                                                );
                                            })}
                                        </div>
                                    ) : (
                                        <div className="flex w-full flex-col items-center justify-center p-6">
                                            <div className="flex h-12 w-12 items-center justify-center rounded-lg border border-dashed border-gray-500 text-muted">
                                                <SearchIcon className="h-3 w-3" />
                                            </div>

                                            <h2 className="pt-2.5 text-center text-sm font-semibold">
                                                {t('No result found!')}
                                            </h2>

                                            <p className="pt-2.5 text-center text-xs text-muted">
                                                {t(
                                                    'Please change your search criteria and try again.'
                                                )}
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </UiTransition>
                        </div>
                    </UiClickOutside>
                </div>
            </div>
        </>
    );
});

if (isDev) {
    SearchBar.displayName = 'SearchBar';
}

export default SearchBar;

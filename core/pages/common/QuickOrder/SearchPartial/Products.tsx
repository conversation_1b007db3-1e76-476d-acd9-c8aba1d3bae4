import {useRouter} from 'next/router';
import {
    flexRender,
    getCoreRowModel,
    getPaginationRowModel,
    useReactTable
} from '@tanstack/react-table';
import Price from '@components/common/Price';
import {SkeletonRows, PaginationButtons} from '@components/common/DataTable';
import {
    UiTable,
    UiTableHeader,
    UiTableRow,
    UiTableHead,
    UiTableBody,
    UiTableCell
} from '@core/components/ui';
import {XCircleIcon} from '@core/icons/solid';
import {CheckCircleIcon} from '@core/icons/solid';
import {useTrans} from '@core/hooks';
import {useQuickOrder} from '../context';
import useColumns from './useColumns';

const Products = () => {
    const {product, categoryProducts, categoryStatus, setStatus} =
        useQuickOrder();

    const router = useRouter();

    const t = useTrans();

    const columns = useColumns();

    const table = useReactTable({
        data: categoryProducts,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        initialState: {
            pagination: {
                pageSize: 5
            }
        }
    });

    return (
        <>
            <div className="cursor-default overflow-hidden rounded-lg border bg-white">
                <UiTable>
                    <UiTableHeader>
                        {table.getHeaderGroups().map(headerGroup => (
                            <UiTableRow key={headerGroup.id}>
                                {headerGroup.headers.map(header => (
                                    <UiTableHead
                                        key={header.id}
                                        className="!h-auto whitespace-nowrap bg-gray-50 !p-2 text-xs font-semibold text-gray-700"
                                    >
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                  header.column.columnDef
                                                      .header,
                                                  header.getContext()
                                              )}
                                    </UiTableHead>
                                ))}
                            </UiTableRow>
                        ))}
                    </UiTableHeader>
                    <UiTableBody>
                        {categoryStatus === 'pending' && (
                            <SkeletonRows
                                rowSize={table.initialState.pagination.pageSize}
                            />
                        )}
                        {categoryStatus === 'rejected' && (
                            <UiTableRow>
                                <UiTableCell
                                    className="!p-2 text-center"
                                    colSpan={table.getAllColumns().length}
                                >
                                    {t('Something went wrong.')}
                                </UiTableCell>
                            </UiTableRow>
                        )}
                        {categoryStatus === 'resolved' &&
                            (table.getRowModel().rows?.length > 0 ? (
                                table.getRowModel().rows.map(row => (
                                    <UiTableRow
                                        key={row.id}
                                        className="cursor-pointer transition hover:bg-secondary-100"
                                        onClick={() => {
                                            setStatus('pending');
                                            router.push({
                                                pathname: '',
                                                query: {
                                                    product: row.original.slug
                                                }
                                            });
                                        }}
                                    >
                                        {row.getVisibleCells().map(cell => (
                                            <UiTableCell
                                                key={cell.id}
                                                className="!p-2 text-xs"
                                            >
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </UiTableCell>
                                        ))}
                                    </UiTableRow>
                                ))
                            ) : product === undefined ? (
                                <UiTableRow>
                                    <UiTableCell
                                        className="!p-2 text-center"
                                        colSpan={table.getAllColumns().length}
                                    >
                                        {t('No products found!')}
                                    </UiTableCell>
                                </UiTableRow>
                            ) : (
                                <UiTableRow className="cursor-default">
                                    <UiTableCell className="!p-2 text-xs">
                                        {product.quantity > 0 ? (
                                            <CheckCircleIcon className="h-4 w-4 text-primary-600" />
                                        ) : (
                                            <XCircleIcon className="h-4 w-4 text-red-600" />
                                        )}
                                    </UiTableCell>
                                    <UiTableCell className="!p-2 text-xs">
                                        {product.name}
                                    </UiTableCell>
                                    <UiTableCell className="!p-2 text-xs">
                                        <Price price={product.salesPrice} />
                                    </UiTableCell>
                                </UiTableRow>
                            ))}
                    </UiTableBody>
                </UiTable>
            </div>
            {categoryStatus === 'pending' ? (
                <div className="skeleton-card h-8 w-20"></div>
            ) : (
                Array.isArray(categoryProducts) &&
                categoryProducts.length > 0 && (
                    <PaginationButtons
                        hasNextPage={table.getCanNextPage()}
                        hasPreviousPage={table.getCanPreviousPage()}
                        nextPage={table.nextPage}
                        pageCount={table.getPageCount()}
                        pageIndex={table.getState().pagination.pageIndex}
                        previousPage={table.previousPage}
                    />
                )
            )}
        </>
    );
};

export default Products;

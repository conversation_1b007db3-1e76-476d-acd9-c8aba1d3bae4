import {useMemo} from 'react';
import {ColumnDef} from '@tanstack/react-table';
import {useTrans} from '@core/hooks';
import {CheckCircleIcon} from '@core/icons/solid';
import {XCircleIcon} from '@core/icons/solid';
import {Product} from '@core/types';
import Price from '@components/common/Price';

const useColumns = () => {
    const t = useTrans();

    const columns = useMemo<ColumnDef<Product>[]>(
        () => [
            {
                header: t('St'),
                accessorKey: 'quantity',
                cell: ({row}) => {
                    return row.original.quantity > 0 ? (
                        <CheckCircleIcon className="h-4 w-4 text-primary-600" />
                    ) : (
                        <XCircleIcon className="h-4 w-4 text-red-600" />
                    );
                }
            },
            {
                header: t('Product Name'),
                accessorKey: 'name'
            },
            {
                header: t('Price'),
                accessorKey: 'salesPrice',
                cell: ({row}) => <Price price={row.original.salesPrice} />
            }
        ],
        // eslint-disable-next-line
        []
    );

    return columns;
};

export default useColumns;

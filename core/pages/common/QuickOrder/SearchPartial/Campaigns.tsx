import {useTrans} from '@core/hooks';
import {useQuickOrder} from '../context';

const Campaigns = () => {
    const {campaigns, product, status} = useQuickOrder();

    const t = useTrans();

    if (status === 'pending') {
        return (
            <div className="card-container mt-4 grid gap-2 p-4">
                <div className="skeleton-card mb-4 h-8"></div>
                <div className="skeleton-card h-8"></div>
                <div className="skeleton-card h-8"></div>
            </div>
        );
    }

    return product !== undefined ? (
        <div className="card-container mt-4 grid gap-4 py-4 font-medium">
            <p className="border-b px-4 pb-2">{t('Campaigns')}</p>
            {Array.isArray(campaigns) && campaigns.length > 0 ? (
                <div className="grid gap-2 px-4">
                    {campaigns.map(campaign => (
                        <div
                            key={campaign.id}
                            className="flex items-center gap-2 rounded-md bg-primary-100 px-4 py-2 text-sm"
                        >
                            <svg
                                className="h-5 w-5"
                                viewBox="0 0 18 18"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <g mask="url(#a)" className="fill-primary-600">
                                    <path
                                        d="M17.765 3.087a1.993 1.993 0 0 0-1.779-1.089h-5.16a2 2 0 0 0-1.42.592L2.59 9.475c0 .002 0 .002-.002.002a2.033 2.033 0 0 0-.5.874L.74 8.048a1.998 1.998 0 0 1 .712-2.735L9.815.424A1.988 1.988 0 0 1 11.34.22l4.985 1.335a1.998 1.998 0 0 1 1.439 1.532Z"
                                        fillOpacity=".55"
                                    />
                                    <path d="M10.826 1.998h5.16a1.998 1.998 0 0 1 1.999 1.998v5.168c0 .53-.21 1.039-.585 1.413l-6.812 6.812a1.999 1.999 0 0 1-2.818.01l-5.162-5.096a2 2 0 0 1-.02-2.825c.002 0 .003 0 .003-.002l6.814-6.884a1.998 1.998 0 0 1 1.421-.594Zm2.663 5.995a1.5 1.5 0 1 0-.001-3 1.5 1.5 0 0 0 0 3Z" />
                                </g>
                            </svg>
                            <p className="font-medium text-primary-600">
                                {campaign.description}
                            </p>
                        </div>
                    ))}
                </div>
            ) : (
                <div className="grid place-items-center gap-2 font-medium">
                    <div className="flex h-16 w-16 items-center justify-center rounded-lg border border-dashed text-primary-600">
                        <svg
                            className="h-5 w-5"
                            viewBox="0 0 18 18"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <g mask="url(#a)" className="fill-primary-600">
                                <path
                                    d="M17.765 3.087a1.993 1.993 0 0 0-1.779-1.089h-5.16a2 2 0 0 0-1.42.592L2.59 9.475c0 .002 0 .002-.002.002a2.033 2.033 0 0 0-.5.874L.74 8.048a1.998 1.998 0 0 1 .712-2.735L9.815.424A1.988 1.988 0 0 1 11.34.22l4.985 1.335a1.998 1.998 0 0 1 1.439 1.532Z"
                                    fillOpacity=".55"
                                />
                                <path d="M10.826 1.998h5.16a1.998 1.998 0 0 1 1.999 1.998v5.168c0 .53-.21 1.039-.585 1.413l-6.812 6.812a1.999 1.999 0 0 1-2.818.01l-5.162-5.096a2 2 0 0 1-.02-2.825c.002 0 .003 0 .003-.002l6.814-6.884a1.998 1.998 0 0 1 1.421-.594Zm2.663 5.995a1.5 1.5 0 1 0-.001-3 1.5 1.5 0 0 0 0 3Z" />
                            </g>
                        </svg>
                    </div>

                    <h2 className="text-center text-sm font-medium">
                        {t('No campaign found!')}
                    </h2>
                </div>
            )}
        </div>
    ) : null;
};

export default Campaigns;

import {useTrans} from '@core/hooks';
import SearchBar from './SearchBar';
import Products from './Products';
import Campaigns from './Campaigns';

const SearchPartial = () => {
    const t = useTrans();

    return (
        <aside className="col-span-12 xl:col-span-3">
            <div className="card-container grid gap-4 py-4 font-medium">
                <p className="border-b px-4 pb-2">{t('Quick Order')}</p>
                <div className="grid gap-4 px-4 font-medium">
                    <SearchBar />
                    <Products />
                </div>
            </div>

            <Campaigns />
        </aside>
    );
};

export default SearchPartial;

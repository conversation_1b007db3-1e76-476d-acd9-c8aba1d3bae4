import {useTrans} from '@core/hooks';
import {useRouter} from 'next/router';
import {useMemo} from 'react';
import useLedger from './useLedger';

const Balance = () => {
    const {ledger, isLoading} = useLedger();

    const t = useTrans();

    const router = useRouter();

    const priceFormatter = useMemo(() => {
        return Intl.NumberFormat(router.locale, {
            style: 'decimal',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }, [router.locale]);

    return isLoading ? (
        <div className="skeleton-card h-8 w-96"></div>
    ) : (
        <div className="grid gap-2 text-sm max-md:w-full md:grid-cols-3 md:gap-4">
            <div className="cursor-default rounded-md border p-2 leading-4">
                <span className="font-semibold">{t('Debit')}</span>:{' '}
                {priceFormatter.format(ledger.debit)} TL
            </div>
            <div className="cursor-default rounded-md border p-2 leading-4">
                <span className="font-semibold">{t('Credit')}</span>:{' '}
                {priceFormatter.format(ledger.credit)} TL
            </div>
            <div className="cursor-default rounded-md border p-2 leading-4">
                <span className="font-semibold">{t('Balance')}</span>:{' '}
                {priceFormatter.format(ledger.balance)} TL
            </div>
        </div>
    );
};

export default Balance;

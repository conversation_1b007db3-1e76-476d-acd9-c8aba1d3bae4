import {jsonRequest} from '@core/helpers';
import {useEffect, useState} from 'react';
import {Ledger, Transaction} from './types';

const useLedger = (limit = 10, skip = 0) => {
    const [transactions, setTransactions] = useState<Transaction[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [ledger, setLedger] = useState<Ledger>({
        balance: 0,
        credit: 0,
        debit: 0
    });

    useEffect(() => {
        setIsLoading(true);
        (async () => {
            try {
                const ledgers = await jsonRequest({
                    url: '/api/customers/ledger',
                    method: 'POST',
                    data: {limit, skip}
                });

                if (
                    Array.isArray(ledgers.transactions) &&
                    ledgers.transactions.length > 0
                ) {
                    setTransactions(ledgers.transactions);
                    setLedger({
                        balance: ledgers.balance,
                        credit: ledgers.credit,
                        debit: ledgers.debit
                    });
                } else {
                    setTransactions([]);
                    setLedger(prev => ({...prev}));
                }
            } catch (err) {
                console.error(err);
            } finally {
                setIsLoading(false);
            }
        })();
    }, [limit, skip]);

    return {transactions, ledger, isLoading};
};

export default useLedger;

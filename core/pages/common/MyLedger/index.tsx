import {useState} from 'react';
import {
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getPaginationRowModel,
    useReactTable
} from '@tanstack/react-table';
import {
    UiTable,
    UiTableBody,
    UiTableCell,
    UiTableHead,
    UiTableHeader,
    UiTableRow
} from '@core/components/ui';
import {
    SkeletonRows,
    EmptyTableRow,
    ColumnToggle,
    PaginationButtons
} from '@components/common/DataTable';
import Seo from '@components/common/Seo';
import {useTrans} from '@core/hooks';
import {ReportsIcon} from '@core/icons/solid';
import useColumns from './useColumns';
import useLedger from './useLedger';
import Balance from './Balance';

const Payment = () => {
    const [page, setPage] = useState(1);
    const [initialSkip, setInitialSkip] = useState(0);
    const {transactions, isLoading} = useLedger(10, initialSkip);

    const columns = useColumns();

    const t = useTrans();

    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
        projectCode: false,
        projectName: false,
        documentNo: false,
        documentType: false
    });
    const [globalFilter, setGlobalFilter] = useState('');

    const table = useReactTable({
        data: transactions,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onGlobalFilterChange: setGlobalFilter,
        state: {
            columnVisibility,
            globalFilter,
            pagination: {pageIndex: page - 1, pageSize: 10}
        },
        initialState: {
            pagination: {pageIndex: page - 1, pageSize: 10}
        }
    });

    return (
        <>
            <Seo title={t('My Ledger')} />

            <p className="mb-4 hidden text-xl font-medium xl:block">
                {t('My Ledger')}
            </p>

            <div className="card-container mb-4 flex flex-col justify-between gap-2 p-3 md:flex-row md:items-center md:gap-4">
                <Balance />
                <div className="flex items-center gap-3 max-md:justify-end lg:flex-row-reverse">
                    <PaginationButtons
                        hasNextPage={table.getRowModel().rows.length > 0}
                        hasPreviousPage={initialSkip > 1}
                        nextPage={() => setInitialSkip(prev => prev + 10)}
                        previousPage={() =>
                            setInitialSkip(prev => Math.max(prev - 10))
                        }
                        pageIndex={initialSkip / 10}
                    />
                    <ColumnToggle tableColumns={table.getAllColumns()} />
                </div>
            </div>

            <div className="cursor-default overflow-hidden rounded-lg border bg-white">
                <UiTable>
                    <UiTableHeader>
                        {table.getHeaderGroups().map(headerGroup => (
                            <UiTableRow key={headerGroup.id}>
                                {headerGroup.headers.map(header => (
                                    <UiTableHead
                                        key={header.id}
                                        className="whitespace-nowrap bg-gray-50 text-xs font-semibold text-gray-700"
                                    >
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                  header.column.columnDef
                                                      .header,
                                                  header.getContext()
                                              )}
                                    </UiTableHead>
                                ))}
                            </UiTableRow>
                        ))}
                    </UiTableHeader>

                    <UiTableBody>
                        {isLoading ? (
                            <SkeletonRows
                                rowSize={table.getAllColumns().length}
                            />
                        ) : table.getRowModel().rows?.length > 0 &&
                          table.getRowModel().rows.length > 0 ? (
                            table.getRowModel().rows.map(row => {
                                return (
                                    <UiTableRow
                                        key={row.id}
                                        className="transition hover:bg-secondary-100"
                                    >
                                        {row.getVisibleCells().map(cell => (
                                            <UiTableCell key={cell.id}>
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </UiTableCell>
                                        ))}
                                    </UiTableRow>
                                );
                            })
                        ) : (
                            table.getRowModel().rows.length < 1 && (
                                <EmptyTableRow
                                    colSize={table.getAllColumns().length}
                                    icon={<ReportsIcon className="h-8 w-8" />}
                                    title="No ledger found!"
                                    description="There are no ledger in your account. After shopping on our site, you can access the ledger details from this page."
                                />
                            )
                        )}
                    </UiTableBody>
                </UiTable>
            </div>

            <PaginationButtons
                hasNextPage={table.getRowModel().rows.length > 0}
                hasPreviousPage={initialSkip > 1}
                nextPage={() => setInitialSkip(prev => prev + 10)}
                previousPage={() => setInitialSkip(prev => Math.max(prev - 10))}
                pageIndex={initialSkip / 10}
                className="card-container ml-auto mt-4 w-fit justify-end p-2 md:hidden"
            />
        </>
    );
};

export default Payment;

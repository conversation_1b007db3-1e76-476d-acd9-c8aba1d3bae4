import {useEffect, useState} from 'react';
import {useTrans} from '@core/hooks';
import {jsonRequest} from '@core/helpers';
import {notification} from '@core/components/ui';

const useDownload = () => {
    const [entryId, setEntryId] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const t = useTrans();

    useEffect(() => {
        if (!entryId) return;

        (async () => {
            try {
                setIsLoading(true);

                const {receiptUrl} = await jsonRequest({
                    url: '/api/customers/download-receipt',
                    method: 'POST',
                    data: {entryId}
                });

                const blobData = await (await fetch(receiptUrl)).blob();

                if (blobData.type !== 'application/pdf') {
                    throw new Error('Receipt download error');
                }

                const fileURL = URL.createObjectURL(blobData);
                let link = document.createElement('a');
                link.href = fileURL;
                link.download = `Tahsilat-${entryId}.pdf`;
                link.click();
                URL.revokeObjectURL(link.href);
            } catch (err) {
                notification({
                    title: t('Error'),
                    description: t(
                        'An error occurred while downloading the receipt!'
                    ),
                    status: 'error'
                });
                console.error(err);
            } finally {
                setIsLoading(false);
                setEntryId('');
            }
        })();
        // eslint-disable-next-line
    }, [entryId]);

    return {setEntryId, isLoading, entryId};
};

export default useDownload;

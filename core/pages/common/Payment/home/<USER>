import {useMemo} from 'react';
import {useRouter} from 'next/router';
import {ColumnDef, FilterFn} from '@tanstack/react-table';
import {cls} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {Receipt} from './types';
import useDownload from './useDownload';
import {UiSpinner} from '@core/components/ui';
import {ReceiptIcon} from '@core/icons/outline';

const dateFilter: FilterFn<any> = (row, columnId, value) => {
    const originalDate = row.getValue(columnId);

    if (Array.isArray(value) && value.length === 0) return row.original;

    let startDate = new Date(value[0]);
    let endDate = new Date(value[1]);
    const time = new Date(originalDate as string);
    return time >= startDate && time <= endDate;
};

const useColumns = () => {
    const router = useRouter();

    const priceFormatter = useMemo(() => {
        return Intl.NumberFormat(router.locale, {
            style: 'decimal',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }, [router.locale]);

    const t = useTrans();

    const {setEntryId, isLoading, entryId} = useDownload();

    const columns = useMemo<ColumnDef<Receipt>[]>(() => {
        return [
            {
                header: t('Download'),
                accessorKey: 'id',
                cell: ({row}) => (
                    <span
                        onClick={() => setEntryId(row.original.id)}
                        className="cursor-pointer transition hover:opacity-70"
                    >
                        {isLoading && row.original.id === entryId ? (
                            <UiSpinner size="sm" />
                        ) : (
                            <ReceiptIcon className="h-4 w-4" />
                        )}
                    </span>
                )
            },
            {
                accessorKey: 'code',
                header: t('Code')
            },
            {
                accessorKey: 'documentNo',
                header: t('Document No')
            },
            {
                accessorKey: 'issueDate',
                header: t('Issue Date'),
                cell: ({row}) => {
                    const issueDate = new Date(row.original.issueDate);

                    return (
                        <span>
                            {issueDate.toLocaleDateString(router.locale)}
                        </span>
                    );
                }
            },
            {
                accessorKey: 'dueDate',
                header: t('Due Date'),
                cell: ({row}) => {
                    const dueDate = new Date(row.original.dueDate);

                    return (
                        <span>{dueDate.toLocaleDateString(router.locale)}</span>
                    );
                },
                filterFn: dateFilter
            },
            {
                accessorKey: 'projectCode',
                header: t('Project Code')
            },
            {
                accessorKey: 'projectName',
                header: t('Project Name')
            },
            {
                accessorKey: 'documentType',
                header: t('Document Type')
            },
            {
                accessorKey: 'installmentCount',
                header: t('Installment Count')
            },
            {
                accessorKey: 'installmentAmount',
                header: t('Installment Amount')
            },
            {
                accessorKey: 'plusInstallmentCount',
                header: t('Plus Installment')
            },
            {
                accessorKey: 'dueDifference',
                header: t('Due Difference'),
                cell: ({row}) => {
                    const amount = row.original.dueDifference;

                    return (
                        <div className="whitespace-nowrap text-right font-medium text-red-700">
                            {priceFormatter.format(amount)}{' '}
                            {row.original.currencyName}
                        </div>
                    );
                }
            },
            {
                accessorKey: 'total',
                header: t('Total'),
                cell: ({row}) => {
                    const total = row.original.total;

                    return (
                        <div className="whitespace-nowrap text-right font-medium text-green-600">
                            {priceFormatter.format(total)}{' '}
                            {row.original.currencyName}
                        </div>
                    );
                }
            },
            {
                accessorKey: 'amount',
                header: t('Amount'),
                cell: ({row}) => {
                    const amount = row.original.amount;

                    return (
                        <div className="whitespace-nowrap text-right font-medium text-green-600">
                            {priceFormatter.format(amount)}{' '}
                            {row.original.currencyName}
                        </div>
                    );
                }
            },
            {
                accessorKey: 'status',
                header: t('Status'),
                cell: ({row}) => {
                    const status = row.original.status;

                    return (
                        <span
                            className={cls(
                                'whitespace-nowrap rounded-full px-3 py-1.5 text-xs font-medium',
                                t('Approved') === status &&
                                    'bg-green-200 text-green-600',
                                t('Draft') === status &&
                                    'bg-yellow-200 text-yellow-600',
                                t('Canceled') === status &&
                                    'bg-red-200 text-red-600',
                                t('Payment Planned') === status &&
                                    'bg-blue-200 text-blue-600'
                            )}
                        >
                            {status}
                        </span>
                    );
                }
            }
        ];
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isLoading]);

    return columns;
};

export default useColumns;

import {useState} from 'react';
import {useRouter} from 'next/router';
import {
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable
} from '@tanstack/react-table';
import {
    UiButton,
    UiTable,
    UiTableBody,
    UiTableCell,
    UiTableHead,
    UiTableHeader,
    UiTableRow
} from '@core/components/ui';
import {
    SkeletonRows,
    EmptyTableRow,
    ColumnToggle,
    SearchFilter,
    PaginationButtons,
    DatePicker
} from '@components/common/DataTable';
import Seo from '@components/common/Seo';
import {useTrans} from '@core/hooks';
import {PaymentIcon} from '@core/icons/solid';
import useColumns from './useColumns';
import useReceipts from './useReceipts';

const Payment = () => {
    const [page, setPage] = useState(1);
    const [initialSkip, setInitialSkip] = useState(0);
    const {receipts, isLoading} = useReceipts(10, initialSkip);

    const columns = useColumns();

    const t = useTrans();

    const router = useRouter();

    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
        projectCode: false,
        projectName: false,
        documentNo: false,
        documentType: false
    });
    const [globalFilter, setGlobalFilter] = useState('');

    const table = useReactTable({
        data: receipts,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onGlobalFilterChange: setGlobalFilter,
        state: {
            columnVisibility,
            globalFilter,
            pagination: {pageIndex: page - 1, pageSize: 10}
        },
        initialState: {
            pagination: {pageIndex: page - 1, pageSize: 10}
        }
    });

    return (
        <>
            <Seo title={t('Payment')} />

            <div className="mb-3.5 flex items-start justify-between">
                <p className="hidden text-xl font-medium xl:block">
                    {t('Payment')}
                </p>
                <UiButton
                    variant="solid"
                    color="primary"
                    size="sm"
                    className="ml-auto"
                    onClick={() => router.push('/payment/new-payment')}
                >
                    {t('Create New Payment')}
                </UiButton>
            </div>

            <div className="card-container mb-4 p-3 max-md:space-y-2 md:flex md:items-center md:justify-end md:gap-3">
                <div className="mr-auto flex items-center justify-between gap-3">
                    <DatePicker
                        filteredColumn={table.getColumn('dueDate')}
                        className="max-md:w-full"
                    />
                    <ColumnToggle
                        className="md:hidden"
                        tableColumns={table.getAllColumns()}
                    />
                </div>
                <div className="flex items-center gap-3">
                    <SearchFilter
                        onChange={e => table.setGlobalFilter(e.target.value)}
                        value={table.getState().globalFilter}
                    />
                    <ColumnToggle
                        className="hidden md:inline-block"
                        tableColumns={table.getAllColumns()}
                    />
                    <PaginationButtons
                        hasNextPage={table.getRowModel().rows.length > 0}
                        hasPreviousPage={initialSkip > 1}
                        nextPage={() => setInitialSkip(prev => prev + 10)}
                        previousPage={() =>
                            setInitialSkip(prev => Math.max(prev - 10))
                        }
                        pageIndex={initialSkip / 10}
                    />
                </div>
            </div>

            <div className="cursor-default overflow-hidden rounded-lg border bg-white">
                <UiTable>
                    <UiTableHeader>
                        {table.getHeaderGroups().map(headerGroup => (
                            <UiTableRow key={headerGroup.id}>
                                {headerGroup.headers.map(header => (
                                    <UiTableHead
                                        key={header.id}
                                        className="whitespace-nowrap bg-gray-50 text-xs font-semibold text-gray-700"
                                    >
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                  header.column.columnDef
                                                      .header,
                                                  header.getContext()
                                              )}
                                    </UiTableHead>
                                ))}
                            </UiTableRow>
                        ))}
                    </UiTableHeader>
                    <UiTableBody>
                        {isLoading ? (
                            <SkeletonRows
                                rowSize={table.getAllColumns().length}
                            />
                        ) : table.getRowModel().rows?.length > 0 &&
                          table
                              .getRowModel()
                              .rows.filter(
                                  row => row.original.status === 'Onaylandı'
                              ).length > 0 ? (
                            table.getRowModel().rows.map(row => {
                                if (row.original.status === 'Onaylandı') {
                                    return (
                                        <UiTableRow
                                            key={row.id}
                                            className="transition hover:bg-secondary-100"
                                        >
                                            {row.getVisibleCells().map(cell => (
                                                <UiTableCell key={cell.id}>
                                                    {flexRender(
                                                        cell.column.columnDef
                                                            .cell,
                                                        cell.getContext()
                                                    )}
                                                </UiTableCell>
                                            ))}
                                        </UiTableRow>
                                    );
                                }
                            })
                        ) : (
                            receipts.length < 1 &&
                            table.getRowModel().rows.length < 1 && (
                                <EmptyTableRow
                                    colSize={table.getAllColumns().length}
                                    icon={<PaymentIcon className="h-8 w-8" />}
                                    title="No payment found!"
                                    description="There are no payment in your account. After create a new payment on our site, you can access the previous payments details from this page."
                                />
                            )
                        )}
                    </UiTableBody>
                </UiTable>
            </div>

            <PaginationButtons
                hasNextPage={table.getRowModel().rows.length > 0}
                hasPreviousPage={initialSkip > 1}
                nextPage={() => setInitialSkip(prev => prev + 10)}
                previousPage={() => setInitialSkip(prev => Math.max(prev - 10))}
                pageIndex={initialSkip / 10}
                className="card-container ml-auto mt-4 w-fit justify-end p-2 md:hidden"
            />
        </>
    );
};

export default Payment;

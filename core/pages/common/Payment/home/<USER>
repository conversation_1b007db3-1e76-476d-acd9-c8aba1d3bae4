import {jsonRequest} from '@core/helpers';
import {useEffect, useState} from 'react';
import {Receipt} from './types';

const useReceipts = (limit = 10, skip = 10) => {
    const [receipts, setReceipts] = useState<Receipt[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    const total = Math.ceil(receipts.length / limit);

    useEffect(() => {
        (async () => {
            setIsLoading(true);
            try {
                const receiptsData = await jsonRequest({
                    url: '/api/customers/receipts',
                    method: 'POST',
                    data: {limit, skip}
                });
                setReceipts(receiptsData);
            } catch (err) {
                console.error(err);
            } finally {
                setIsLoading(false);
            }
        })();
    }, [limit, skip]);

    return {receipts, isLoading, total};
};

export default useReceipts;

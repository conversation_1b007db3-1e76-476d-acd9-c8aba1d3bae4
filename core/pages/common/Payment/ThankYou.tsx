import {useEffect, useState} from 'react';
import {useRouter} from 'next/router';
import {useTrans} from '@core/hooks';
import {UiImage, UiLink} from '@core/components/ui';
import {CheckIcon} from '@core/icons/solid';
import Price from '@components/common/Price';

type Payload = {
    code: string;
    total: number;
    cardBrand: string;
    cardHolder: string;
    installmentCount: number;
    plusInstallmentCount: number;
    installmentAmount: number;
    cardBrandLogo: string;
};

const ThankYou = () => {
    const [payload, setPayload] = useState<Payload | null>(null);
    const router = useRouter();
    const t = useTrans();

    useEffect(() => {
        try {
            const payload = JSON.parse(localStorage.getItem('payment-result')!);

            if (payload !== null) {
                setPayload(payload);
            } else {
                router.replace('/');
            }
        } catch (error: any) {
            router.replace('/');
        }

        localStorage.removeItem('payment-result');
        // eslint-disable-next-line
    }, []);

    return (
        <div className="card-container relative flex flex-col items-center py-12 xl:py-24">
            <div className="flex h-full w-full max-w-lg flex-col items-center justify-center print:max-w-none max-xl:mt-8">
                <div className="flex h-20 w-20 items-center justify-center rounded-full bg-green-600 text-white">
                    <CheckIcon className="h-10 w-10" />
                </div>

                <p className="mt-10 text-3xl font-extrabold">
                    {t('Thank You!')}
                </p>

                <p className="mt-4 px-4 text-center text-base text-gray-500 xl:mt-3 xl:px-0">
                    {payload?.cardHolder}
                    {', '}
                    {t(
                        'Your payment #{code} has been paid successfully. You can click the View Payments button to print your payment statement.',
                        {
                            code: payload?.code ?? ''
                        }
                    )}
                </p>

                {payload?.cardBrandLogo &&
                    payload.cardBrandLogo.startsWith('https') && (
                        <UiImage
                            src={payload.cardBrandLogo}
                            alt=""
                            width={128}
                            height={64}
                            className="mt-4"
                            priority
                        />
                    )}

                <div className="w-full max-w-lg space-y-4 pt-4 text-sm font-medium text-gray-900 max-xl:px-8 xl:pt-10">
                    <div className="flex justify-between">
                        <p>{t('Installment')}</p>
                        <p className="font-bold">
                            {payload?.installmentCount ?? '-'}
                        </p>
                    </div>
                    <div className="flex justify-between">
                        <p>{t('Plus Installment')}</p>
                        <p className="font-bold">
                            {payload?.plusInstallmentCount ?? '-'}
                        </p>
                    </div>
                    <div className="flex justify-between">
                        <p>{t('Installment Amount')}</p>
                        <Price
                            className="!font-bold"
                            price={payload?.installmentAmount ?? 0}
                        />
                    </div>
                    <div className="flex justify-between">
                        <p>{t('Total')}</p>
                        <Price
                            className="!font-bold"
                            price={payload?.total ?? 0}
                        />
                    </div>
                </div>
            </div>

            <div className="mt-4 flex w-full max-w-lg flex-col gap-4 border-t pt-4 max-xl:px-8 xl:mt-10 xl:flex-row xl:justify-between xl:pt-10">
                <UiLink
                    href="/payment"
                    data-color="primary"
                    className="btn btn-xl btn-light"
                >
                    {t('View Your Payments')}
                </UiLink>

                <UiLink
                    href="/"
                    data-color="primary"
                    className="btn btn-xl btn-solid"
                >
                    {t('Continue Shopping')}
                    <span className="ml-3 inline-block"> &rarr;</span>
                </UiLink>
            </div>
        </div>
    );
};

export default ThankYou;

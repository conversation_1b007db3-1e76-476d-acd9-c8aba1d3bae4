import {memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiSelect} from '@core/components/ui';
import usePayment from './context';

const CurrencySelector = memo(() => {
    const t = useTrans();
    const {
        selectedCurrency,
        availableCurrencies,
        setSelectedCurrency,
        setSelectedBalance,
        exchangeRates
    } = usePayment();

    const handleCurrencyChange = (currencyCode: string) => {
        const currency = availableCurrencies.find(c => c.code === currencyCode);
        if (currency) {
            const updatedCurrency = {
                ...currency,
                rate: exchangeRates?.rates[currency.code] || currency.rate
            };
            setSelectedCurrency(updatedCurrency);
            // Para birimi değiştiğinde seçilen bakiyeyi sıfırla
            setSelectedBalance(0);
        }
    };

    return (
        <div className="mb-4">
            <label className="mb-2 block text-sm font-medium text-gray-700">
                {t('Currency')}
            </label>
            <UiSelect
                value={selectedCurrency.code}
                onChange={e => handleCurrencyChange(e.target.value)}
                className="w-full rounded-lg border-gray-300 focus:border-primary-600 focus:ring-2 focus:ring-primary-600"
            >
                {availableCurrencies.map(currency => (
                    <option key={currency.code} value={currency.code}>
                        {currency.symbol} {currency.name} ({currency.code})
                        {exchangeRates &&
                            currency.code !== 'TL' &&
                            ` - ${(
                                exchangeRates.rates[currency.code] ||
                                currency.rate
                            ).toFixed(2)} TL`}
                    </option>
                ))}
            </UiSelect>
            {exchangeRates && (
                <p className="mt-1 text-xs text-gray-500">
                    {t('Last updated')}:{' '}
                    {new Date(exchangeRates.lastUpdated).toLocaleString()}
                </p>
            )}
        </div>
    );
});

if (isDev) {
    CurrencySelector.displayName = 'CurrencySelector';
}

export default CurrencySelector;

import {UiCheckbox, UiImage} from '@core/components/ui';
import Price from '@components/common/Price';
import {useTrans} from '@core/hooks';
import {cls} from '@core/helpers';
import {cardColorMapper} from './utils';
import usePayment from '../context';

const CardDetails = () => {
    const {installments, setInstallments, cardBrandCode} = usePayment();

    const t = useTrans();

    function installmentRateSelectHandler(id: string) {
        const modifiedInstallments = installments.map(item => ({
            ...item,
            installments: item.installments.map(i => ({
                ...i,
                isChecked: i.id === id
            }))
        }));

        setInstallments(modifiedInstallments);
    }

    return (
        <div className="my-4 grid gap-4 overflow-auto md:grid-cols-2 xl:grid-cols-3">
            {installments.map(installment => (
                <div
                    key={installment.cardBrandCode}
                    className="rounded-md border text-xs"
                >
                    <div className="flex h-12 items-center justify-center">
                        {installment.cardBrandLogo.length > 0 ? (
                            <UiImage
                                src={installment.cardBrandLogo}
                                alt=""
                                width={96}
                                height={24}
                                className="mx-auto"
                            />
                        ) : (
                            <p className="text-center font-semibold text-base">
                                {installment.cardBrandName}
                            </p>
                        )}
                    </div>
                    <table className="w-full">
                        <thead>
                            <tr className="divide-x border-y align-top font-semibold [&>td]:p-2 [&>td]:text-center">
                                <td>{t('Select')}</td>
                                <td>{t('Installment')}</td>
                                <td>{t('Plus Installment')}</td>
                                <td>{t('Installment Amount')}</td>
                                <td>{t('Net Amount')}</td>
                            </tr>
                        </thead>
                        <tbody className="[&_tr:last-child]:border-0">
                            {installment.installments.map(rate => {
                                const isDisabled =
                                    rate.total <= 0 ||
                                    (cardBrandCode !==
                                        installment.cardBrandCode &&
                                        rate.installmentCount !== 1);

                                return (
                                    <tr
                                        key={rate.id}
                                        onClick={() => {
                                            if (isDisabled) return;
                                            installmentRateSelectHandler(
                                                rate.id
                                            );
                                        }}
                                        className={cls(
                                            'divide-x border-b [&>td]:py-2.5 [&>td]:text-center [&>td]:font-medium',
                                            isDisabled
                                                ? 'cursor-not-allowed'
                                                : 'cursor-pointer',
                                            rate.isChecked
                                                ? `bg-white outline outline-1 ${
                                                      cardColorMapper[
                                                          installment
                                                              .cardBrandCode
                                                      ]?.outlineColor
                                                  }`
                                                : cardColorMapper[
                                                      installment.cardBrandCode
                                                  ]?.bgColor
                                        )}
                                    >
                                        <td className="w-12">
                                            <UiCheckbox
                                                className="cursor-pointer rounded-full"
                                                value={rate.id}
                                                disabled={isDisabled}
                                                onChange={() =>
                                                    installmentRateSelectHandler(
                                                        rate.id
                                                    )
                                                }
                                                checked={rate.isChecked}
                                            />
                                        </td>
                                        <td className="w-12">
                                            {rate.installmentCount}
                                        </td>
                                        <td className="w-18">
                                            {rate.plusInstallmentCount > 0 ? (
                                                <span
                                                    className={cls(
                                                        'rounded-md px-3 py-1 text-[10px] text-white',
                                                        cardColorMapper[
                                                            installment
                                                                .cardBrandCode
                                                        ]?.plusBgColor
                                                    )}
                                                >
                                                    +{rate.plusInstallmentCount}
                                                </span>
                                            ) : (
                                                '-'
                                            )}
                                        </td>
                                        <td>
                                            {rate.installmentAmount > 0 ? (
                                                <Price
                                                    price={
                                                        rate.installmentAmount
                                                    }
                                                />
                                            ) : (
                                                '-'
                                            )}
                                        </td>
                                        <td className="text-primary-600">
                                            {rate.total > 0 ? (
                                                <Price price={rate.total} />
                                            ) : (
                                                '-'
                                            )}
                                        </td>
                                    </tr>
                                );
                            })}
                        </tbody>
                    </table>
                </div>
            ))}
        </div>
    );
};

export default CardDetails;

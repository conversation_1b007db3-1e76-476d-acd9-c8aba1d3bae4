import {useState} from 'react';
import {cls} from '@core/helpers';
import {ChevronDownIcon} from '@core/icons/solid';
import {useTrans} from '@core/hooks';
import CardDetails from './CardDetails';

const CardInstallmentRates = () => {
    const [toggleAccordion, setToggleAccordion] = useState(true);

    const t = useTrans();

    return (
        <div className="card-container mt-4">
            <h2>
                <button
                    className="flex w-full items-center justify-between gap-2 p-4 text-left font-medium"
                    onClick={() => setToggleAccordion(prev => !prev)}
                >
                    <span>{t('Installment Rates')}</span>
                    <ChevronDownIcon
                        className={cls(
                            'h-5 w-5 stroke-black stroke-[20px] transition duration-300',
                            {
                                '-rotate-180': toggleAccordion
                            }
                        )}
                    />
                </button>
            </h2>
            <div
                className={cls(
                    'grid px-4 transition-[grid-template-rows] duration-300',
                    toggleAccordion
                        ? 'grid-rows-[1fr] border-t'
                        : 'grid-rows-[0fr] border-t border-transparent'
                )}
            >
                <div className="overflow-hidden">
                    <CardDetails />
                </div>
            </div>
        </div>
    );
};

export default CardInstallmentRates;

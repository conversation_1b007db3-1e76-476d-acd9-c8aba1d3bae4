type ColorMap = {
    bgColor: string;
    plusBgColor: string;
    outlineColor: string;
};

export const cardColorMapper: Record<string, ColorMap> = {
    axess: {
        bgColor: 'bg-amber-100',
        plusBgColor: 'bg-amber-500',
        outlineColor: 'outline-amber-500'
    },
    world: {
        bgColor: 'bg-purple-100',
        plusBgColor: 'bg-purple-500',
        outlineColor: 'outline-purple-500'
    },
    bonus: {
        bgColor: 'bg-green-100',
        plusBgColor: 'bg-green-500',
        outlineColor: 'outline-green-500'
    },
    maximum: {
        bgColor: 'bg-pink-100',
        plusBgColor: 'bg-pink-500',
        outlineColor: 'outline-pink-500'
    },
    cardfinans: {
        bgColor: 'bg-blue-100',
        plusBgColor: 'bg-blue-500',
        outlineColor: 'outline-blue-500'
    },
    paraf: {
        bgColor: 'bg-cyan-100',
        plusBgColor: 'bg-cyan-500',
        outlineColor: 'outline-cyan-500'
    },
    advantage: {
        bgColor: 'bg-orange-100',
        plusBgColor: 'bg-orange-500',
        outlineColor: 'outline-orange-500'
    },
    combo: {
        bgColor: 'bg-red-100',
        plusBgColor: 'bg-red-500',
        outlineColor: 'outline-red-500'
    },
    saglamkart: {
        bgColor: 'bg-emerald-100',
        plusBgColor: 'bg-emerald-500',
        outlineColor: 'outline-emerald-500'
    },
    bankkart: {
        bgColor: 'bg-rose-100',
        plusBgColor: 'bg-rose-500',
        outlineColor: 'outline-rose-500'
    }
};

import {
    createContext,
    Dispatch,
    memo,
    ReactNode,
    SetStateAction,
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {isDev, jsonRequest} from '@core/helpers';
import {useStore, useTrans, useUI} from '@core/hooks';
import IFrameRedirection from './PaymentCreditCard/IFrameRedirection';
import {useRouter} from 'next/router';

type ProcessPayment = () => Promise<Record<string, any>>;

export type Installment = {
    cardBrandCode: string;
    cardBrandName: string;
    cardBrandLogo: string;
    installments: {
        id: string;
        isChecked: boolean;
        installmentCount: number;
        plusInstallmentCount: number;
        installmentRate: number;
        installmentAmount: number;
        total: number;
    }[];
};

export type Currency = {
    code: string;
    name: string;
    symbol: string;
    rate: number;
};

export type ExchangeRates = {
    rates: Record<string, number>;
    baseCurrency: string;
    lastUpdated: string;
};

type PaymentContextType = {
    errorMessage: string;
    isLoading: boolean;
    salesContractText: string;
    preliminaryInformationForm: string;
    balance: number;
    selectedBalance: number;
    selectedCurrency: Currency;
    exchangeRates: ExchangeRates | null;
    availableCurrencies: Currency[];
    convertedBalance: number;
    setSelectedBalance: Dispatch<SetStateAction<number>>;
    setSelectedCurrency: Dispatch<SetStateAction<Currency>>;
    setErrorMessage: Dispatch<SetStateAction<string>>;
    setIsLoading: Dispatch<SetStateAction<boolean>>;
    setProcessPayment: (fn: ProcessPayment | undefined) => void;
    approvePayment: () => Promise<void>;
    setInstallments: Dispatch<SetStateAction<Installment[]>>;
    setCardBrandCode: Dispatch<SetStateAction<string>>;
    setIsApproved: Dispatch<SetStateAction<boolean>>;
    setTriedWithoutApprove: Dispatch<SetStateAction<boolean>>;
    isApproved: boolean;
    triedWithoutApprove: boolean;
    cardBrandCode: string;
    installments: Installment[];
    convertAmount: (
        amount: number,
        fromCurrency: string,
        toCurrency: string
    ) => number;
};

export const PaymentContext = createContext<PaymentContextType>(null as any);

export const PaymentProvider = memo(({children}: {children: ReactNode}) => {
    const {locale, paymentOnly} = useStore();
    const {openModal} = useUI();
    const router = useRouter();
    const t = useTrans();
    const processPayment = useRef(async () => ({}));
    const [errorMessage, setErrorMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const inProgress = useRef(false);
    const [salesContractText, setSalesContractText] = useState('');
    const [preliminaryInformationForm, setPreliminaryInformationForm] =
        useState('');
    const [balance, setBalance] = useState(0);
    const [selectedBalance, setSelectedBalance] = useState(0);
    const [installments, setInstallments] = useState<Installment[]>([]);
    const [cardBrandCode, setCardBrandCode] = useState('');
    const [triedWithoutApprove, setTriedWithoutApprove] = useState(false);
    const [isApproved, setIsApproved] = useState(!!paymentOnly);
    const [exchangeRates, setExchangeRates] = useState<ExchangeRates | null>(
        null
    );
    const [selectedCurrency, setSelectedCurrency] = useState<Currency>({
        code: 'TL',
        name: 'Turkish Lira',
        symbol: '₺',
        rate: 1
    });

    const availableCurrencies: Currency[] = [
        {code: 'TL', name: 'Turkish Lira', symbol: '₺', rate: 1},
        {code: 'USD', name: 'US Dollar', symbol: '$', rate: 27.5},
        {code: 'EUR', name: 'Euro', symbol: '€', rate: 30.2},
        {code: 'GBP', name: 'British Pound', symbol: '£', rate: 35.1}
    ];

    // Para birimi dönüşümü fonksiyonu
    const convertAmount = useCallback(
        (amount: number, fromCurrency: string, toCurrency: string): number => {
            if (!exchangeRates || fromCurrency === toCurrency) return amount;

            const fromRate = exchangeRates.rates[fromCurrency] || 1;
            const toRate = exchangeRates.rates[toCurrency] || 1;

            // TL'ye çevir, sonra hedef para birimine çevir
            const tlAmount = amount / fromRate;
            return tlAmount * toRate;
        },
        [exchangeRates]
    );

    // Seçilen para birimine göre bakiye hesaplama
    const convertedBalance = useMemo(() => {
        return convertAmount(balance, 'TL', selectedCurrency.code);
    }, [balance, selectedCurrency.code, convertAmount]);

    useEffect(() => {
        /* (async () => {
            const {salesContractText: sct, preliminaryInformationForm: pif} =
                await jsonRequest({
                    url: '/api/checkout/get-texts',
                    method: 'POST',
                    data: {locale}
                });
        })(); */
        setSalesContractText('');
        setPreliminaryInformationForm('');
    }, [locale]);

    // Döviz kurlarını yükle
    useEffect(() => {
        (async () => {
            try {
                const rates = await jsonRequest({
                    url: '/api/customers/exchangeRates',
                    method: 'POST'
                });
                setExchangeRates(rates);

                // Mevcut para birimlerinin kurlarını güncelle
                const updatedCurrencies = availableCurrencies.map(currency => ({
                    ...currency,
                    rate: rates.rates[currency.code] || currency.rate
                }));

                // Seçili para biriminin kurunu güncelle
                setSelectedCurrency(prev => ({
                    ...prev,
                    rate: rates.rates[prev.code] || prev.rate
                }));
            } catch (err) {
                console.error('Failed to load exchange rates:', err);
            }
        })();
    }, []);

    useEffect(() => {
        (async () => {
            setIsLoading(true);

            try {
                const {balance} = await jsonRequest({
                    url: '/api/customers/ledger',
                    method: 'POST'
                });

                if (balance > 0) {
                    setBalance(balance);
                } else {
                    setBalance(0);
                }
            } catch (err) {
                console.error(err);
            } finally {
                setIsLoading(false);
            }
        })();
    }, []);

    useEffect(() => {
        (async () => {
            try {
                const installmentsData: Installment[] = await jsonRequest({
                    url: '/api/checkout/installments',
                    method: 'POST',
                    data: {amount: selectedBalance}
                });

                const foundedPos = installmentsData.filter(
                    i => i.cardBrandCode === cardBrandCode
                );
                setInstallments(
                    foundedPos.length === 1 ? foundedPos : installmentsData
                );
            } catch (err) {
                console.error(err);
            }
        })();
    }, [selectedBalance, cardBrandCode]);

    const setProcessPayment = useCallback((fn: ProcessPayment) => {
        processPayment.current = fn;
    }, []);

    const approvePayment = useCallback(async () => {
        if (inProgress.current) return;

        inProgress.current = true;

        setIsLoading(true);

        try {
            if (!!processPayment.current) {
                const payload = await processPayment.current();

                const result = await jsonRequest({
                    url: '/api/customers/make-payment',
                    method: 'POST',
                    data: {payload}
                });

                if (result.status === 'redirect') {
                    await new Promise((resolve, reject) => {
                        openModal(t('3D Validation'), IFrameRedirection, {
                            url: result.url,
                            isClosable: false,
                            onSuccess: () => {
                                resolve(true);
                            },
                            onError: (errorMessage: string) => {
                                reject(new Error(t(errorMessage)));
                            }
                        });
                    });
                }

                localStorage.setItem(
                    'payment-result',
                    JSON.stringify({
                        ...payload,
                        code: result.code,
                        plusInstallmentCount: result.plusInstallmentCount,
                        installmentAmount: result.installmentAmount,
                        cardBrandLogo: result.cardBrandLogo,
                        total: result.total
                    })
                );

                await router.replace('/payment/thank-you');

                setErrorMessage('');
                setIsLoading(false);
                inProgress.current = false;
            } else {
                throw new Error('Oops, something went wrong.');
            }
        } catch (error: any) {
            inProgress.current = false;
            setIsLoading(false);
            throw error;
        }
        // eslint-disable-next-line
    }, []);

    const value: any = useMemo(
        () => ({
            cardBrandCode,
            errorMessage,
            isLoading,
            salesContractText,
            preliminaryInformationForm,
            balance,
            installments,
            selectedBalance,
            selectedCurrency,
            exchangeRates,
            availableCurrencies,
            convertedBalance,
            triedWithoutApprove,
            isApproved,
            setSelectedBalance,
            setSelectedCurrency,
            setErrorMessage,
            setIsLoading,
            setProcessPayment,
            setInstallments,
            setCardBrandCode,
            setIsApproved,
            setTriedWithoutApprove,
            approvePayment,
            convertAmount
        }),
        [
            cardBrandCode,
            errorMessage,
            isLoading,
            salesContractText,
            preliminaryInformationForm,
            balance,
            installments,
            selectedBalance,
            selectedCurrency,
            exchangeRates,
            availableCurrencies,
            convertedBalance,
            triedWithoutApprove,
            isApproved,
            setProcessPayment,
            approvePayment,
            convertAmount
        ]
    );

    return (
        <PaymentContext.Provider value={value}>
            {children}
        </PaymentContext.Provider>
    );
});

if (isDev) {
    PaymentProvider.displayName = 'PaymentProvider';
}

export default function usePayment() {
    const context = useContext(PaymentContext);

    if (context === undefined) {
        throw new Error(`usePayment must be used within a PaymentProvider!`);
    }

    return context;
}

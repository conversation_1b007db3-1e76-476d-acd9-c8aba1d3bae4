import {useTrans} from '@core/hooks';
import Seo from '@components/common/Seo';
import BalanceGroup from './BalanceGroup';
import PaymentCreditCard from './PaymentCreditCard';
import PaymentSummary from './PaymentSummary';
import CardInstallmentRates from './CardInstallmentRates';
import ApprovePayment from './PaymentCreditCard/ApprovePayment';
import {PaymentProvider} from './context';

const NewPayment = () => {
    const t = useTrans();

    return (
        <PaymentProvider>
            <Seo title={t('New Payment')} />

            <p className="mb-4 hidden text-xl font-medium xl:block">
                {t('New Payment')}
            </p>

            <BalanceGroup />

            <div className="grid grid-cols-12 gap-4">
                <PaymentSummary />
                <PaymentCreditCard />
            </div>

            <CardInstallmentRates />

            <ApprovePayment />
        </PaymentProvider>
    );
};

export default NewPayment;

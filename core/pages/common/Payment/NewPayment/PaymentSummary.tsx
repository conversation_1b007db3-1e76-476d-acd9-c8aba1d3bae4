import {UiImage} from '@core/components/ui';
import {useTrans} from '@core/hooks';
import Price from '@components/common/Price';
import usePayment, {Installment} from './context';

const PaymentSummary = () => {
    const {installments} = usePayment();

    const t = useTrans();

    let selectedPaymentPlan: Installment['installments'][0] | undefined;
    const selectedCard = installments.find(
        installment =>
            (selectedPaymentPlan = installment.installments.find(
                i => i.isChecked
            ))
    );

    return (
        <aside className="card-container col-span-12 mt-4 p-4 lg:col-span-3 lg:p-8">
            <div className="mt-2 flex h-12 items-center justify-between gap-4 border-b pb-1">
                <p className="font-semibold">{t('Payment Summary')}</p>
                {selectedCard?.cardBrandLogo &&
                    selectedCard.cardBrandLogo.length > 0 && (
                        <UiImage
                            src={selectedCard.cardBrandLogo}
                            alt=""
                            width={96}
                            height={24}
                        />
                    )}
            </div>

            <ul className="mt-4 text-sm [&>li]:flex [&>li]:items-center [&>li]:justify-between">
                <li className="border-b pb-2">
                    <p>{t('Installment Count')}</p>
                    <p className="text-primary-600">
                        {selectedPaymentPlan?.installmentCount ?? '-'}
                    </p>
                </li>
                <li className="border-b py-2">
                    <p>{t('Plus Installment')}</p>
                    <p className="text-primary-600">
                        {selectedPaymentPlan?.plusInstallmentCount ?? '-'}
                    </p>
                </li>
                <li className="border-b py-2">
                    <p>{t('Total Installment')}</p>
                    <p className="text-primary-600">
                        {(selectedPaymentPlan?.plusInstallmentCount &&
                        selectedPaymentPlan?.installmentCount
                            ? selectedPaymentPlan?.plusInstallmentCount +
                              selectedPaymentPlan?.installmentCount
                            : selectedPaymentPlan?.installmentCount) ?? '-'}
                    </p>
                </li>
                <li className="py-2">
                    <div>{t('Installment Amount')}</div>
                    <Price
                        className="font-medium text-primary-600"
                        price={selectedPaymentPlan?.installmentAmount ?? 0}
                    />
                </li>
                <li className="relative mb-4 mt-8 w-full border-t border-dashed">
                    <span className="absolute -left-7 h-5 w-5 rounded-full border-r bg-secondary-100 lg:-left-[42px]"></span>
                    <span className="absolute -right-7 h-5 w-5 rounded-full border-l bg-secondary-100 lg:-right-[42px]"></span>
                </li>
                <li className="mb-5 mt-8 text-base lg:mb-0 lg:mt-11">
                    <div className="font-bold">{t('Total')}</div>
                    <Price
                        className="font-medium text-primary-600"
                        price={selectedPaymentPlan?.total ?? 0}
                    />
                </li>
            </ul>
        </aside>
    );
};

export default PaymentSummary;

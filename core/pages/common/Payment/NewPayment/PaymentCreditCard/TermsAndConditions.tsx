import {MouseEventHand<PERSON>, useCallback} from 'react';
import {UiCheckbox} from '@core/components/ui';
import {cls} from '@core/helpers';
import {useTrans, useStore, useUI} from '@core/hooks';
import usePayment from '../context';
import StaticContent from './StaticContent';

const TermsAndConditions = () => {
    const t = useTrans();
    const {paymentOnly} = useStore();
    const {openSideBar} = useUI();
    const {
        preliminaryInformationForm,
        salesContractText,
        triedWithoutApprove,
        setIsApproved
    } = usePayment();

    const onPaymentApproveTextClick: MouseEventHandler<HTMLDivElement> =
        useCallback(
            e => {
                // @ts-ignore
                const id = e.target.id;

                if (id === 'preliminaryInformationForm') {
                    e.preventDefault();

                    openSideBar(
                        t('Preliminary Information Form'),
                        <StaticContent content={preliminaryInformationForm} />,
                        'large'
                    );
                } else if (id === 'salesContractText') {
                    e.preventDefault();

                    openSideBar(
                        t('Distance Sales Contract'),
                        <StaticContent content={salesContractText} />,
                        'large'
                    );
                }
            },
            [preliminaryInformationForm, salesContractText, t, openSideBar]
        );

    return (
        !paymentOnly && (
            <div
                className={cls(
                    'shadow-small relative mt-4 rounded-lg border bg-white p-4 focus:outline-none',
                    {
                        'border-red-600 bg-red-50': triedWithoutApprove,
                        'border-secondary-300 bg-white': !triedWithoutApprove
                    }
                )}
            >
                <UiCheckbox
                    className="mt-0.5 self-start"
                    onChange={e => setIsApproved(!!e.currentTarget.checked)}
                    // @ts-ignore
                    invalid={triedWithoutApprove ? 'true' : 'false'}
                >
                    <div
                        onClick={onPaymentApproveTextClick}
                        dangerouslySetInnerHTML={{
                            __html: t(
                                'I have read and accept the <span id="preliminaryInformationForm" class="text-primary-600">preliminary information form/span> and <span id="salesContractText" class="text-primary-600">distance sales contract/span>.'
                            )
                        }}
                    ></div>
                </UiCheckbox>
            </div>
        )
    );
};

export default TermsAndConditions;

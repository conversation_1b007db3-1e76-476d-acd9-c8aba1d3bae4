import {useCallback} from 'react';
import {UiButton} from '@core/components/ui';
import {useTrans, useUI} from '@core/hooks';
import {CheckIcon} from '@core/icons/solid';
import usePayment from '../context';
import ErrorModal from './ErrorModal';

const ApprovePayment = () => {
    const t = useTrans();
    const {openModal} = useUI();
    const {
        isLoading,
        setIsLoading,
        setErrorMessage,
        approvePayment,
        selectedBalance,
        isApproved,
        setTriedWithoutApprove
    } = usePayment();

    const onApprovePayment = useCallback(
        async () => {
            if (!isApproved) {
                setTriedWithoutApprove(true);
                openModal(
                    t('Error'),
                    <ErrorModal
                        message={t(
                            'You have not approved preliminary information form and distance sales contract.'
                        )}
                    />
                );
                return;
            }

            setIsLoading(true);
            setTriedWithoutApprove(false);

            try {
                await approvePayment();
            } catch (error: any) {
                openModal(
                    t('Error'),
                    <ErrorModal message={t(error.message)} />
                );
                setErrorMessage(error.message);
            }

            setIsLoading(false);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [isApproved]
    );

    return (
        <UiButton
            type="submit"
            variant="solid"
            color="primary"
            className="mt-4 w-full"
            size="lg"
            leftIcon={<CheckIcon className="mr-2 h-4 w-4" />}
            loading={isLoading}
            onClick={onApprovePayment}
            disabled={
                selectedBalance === undefined ||
                selectedBalance === 0 ||
                isNaN(selectedBalance)
            }
        >
            {t('Approve Payment')}
        </UiButton>
    );
};

export default ApprovePayment;

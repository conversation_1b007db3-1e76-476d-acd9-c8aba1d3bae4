import {useState} from 'react';
import {
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable
} from '@tanstack/react-table';
import {
    UiTable,
    UiTableBody,
    UiTableCell,
    UiTableHead,
    UiTableHeader,
    UiTableRow
} from '@core/components/ui';
import {
    SkeletonRows,
    EmptyTableRow,
    ColumnToggle,
    SearchFilter,
    PaginationButtons,
    DatePicker
} from '@components/common/DataTable';
import Seo from '@components/common/Seo';
import {InvoicesIcon} from '@core/icons/solid';
import {useTrans} from '@core/hooks';
import useColumns from './useColumns';
import useInvoices from './useInvoices';

const Payment = () => {
    const [page, setPage] = useState(1);
    const [initialSkip, setInitialSkip] = useState(0);
    const {invoices, isLoading} = useInvoices(10, initialSkip);

    const columns = useColumns();

    const t = useTrans();

    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
        projectCode: false,
        projectName: false,
        documentNo: false,
        documentType: false
    });
    const [globalFilter, setGlobalFilter] = useState('');

    const table = useReactTable({
        data: invoices,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onGlobalFilterChange: setGlobalFilter,
        state: {
            columnVisibility,
            globalFilter,
            pagination: {pageIndex: page - 1, pageSize: 10}
        },
        initialState: {
            pagination: {pageIndex: page - 1, pageSize: 10}
        }
    });

    return (
        <>
            <Seo title={t('My Invoices')} />

            <p className="mb-4 hidden text-xl font-medium xl:block">
                {t('My Invoices')}
            </p>

            <div className="card-container mb-4 p-3 max-md:space-y-2 md:flex md:items-center md:justify-end md:gap-3">
                <div className="mr-auto flex items-center justify-between gap-3">
                    <DatePicker
                        filteredColumn={table.getColumn('dueDate')}
                        className="max-md:w-full"
                    />
                    <ColumnToggle
                        className="md:hidden"
                        tableColumns={table.getAllColumns()}
                    />
                </div>
                <div className="flex items-center gap-3">
                    <SearchFilter
                        onChange={e => table.setGlobalFilter(e.target.value)}
                        value={table.getState().globalFilter}
                    />
                    <ColumnToggle
                        className="hidden md:inline-block"
                        tableColumns={table.getAllColumns()}
                    />
                    <PaginationButtons
                        hasNextPage={table.getRowModel().rows.length > 0}
                        hasPreviousPage={initialSkip > 1}
                        nextPage={() => setInitialSkip(prev => prev + 10)}
                        previousPage={() =>
                            setInitialSkip(prev => Math.max(prev - 10))
                        }
                        pageIndex={initialSkip / 10}
                    />
                </div>
            </div>

            <div className="cursor-default overflow-hidden rounded-lg border bg-white">
                <UiTable>
                    <UiTableHeader>
                        {table.getHeaderGroups().map(headerGroup => (
                            <UiTableRow key={headerGroup.id}>
                                {headerGroup.headers.map(header => (
                                    <UiTableHead
                                        key={header.id}
                                        className="whitespace-nowrap bg-gray-50 text-xs font-semibold text-gray-700"
                                    >
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                  header.column.columnDef
                                                      .header,
                                                  header.getContext()
                                              )}
                                    </UiTableHead>
                                ))}
                            </UiTableRow>
                        ))}
                    </UiTableHeader>

                    <UiTableBody>
                        {isLoading ? (
                            <SkeletonRows
                                rowSize={table.getAllColumns().length}
                            />
                        ) : table.getRowModel().rows?.length > 0 &&
                          table.getRowModel().rows.length > 0 ? (
                            table.getRowModel().rows.map(row => {
                                return (
                                    <UiTableRow
                                        key={row.id}
                                        className="transition hover:bg-secondary-100"
                                    >
                                        {row.getVisibleCells().map(cell => (
                                            <UiTableCell key={cell.id}>
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </UiTableCell>
                                        ))}
                                    </UiTableRow>
                                );
                            })
                        ) : (
                            table.getRowModel().rows.length < 1 && (
                                <EmptyTableRow
                                    colSize={table.getAllColumns().length}
                                    icon={<InvoicesIcon className="h-8 w-8" />}
                                    title="No invoices found!"
                                    description="There are no invoices in your account. After shopping on our site, you can access the invoice details from this page."
                                />
                            )
                        )}
                    </UiTableBody>
                </UiTable>
            </div>

            <PaginationButtons
                hasNextPage={table.getRowModel().rows.length > 0}
                hasPreviousPage={initialSkip > 1}
                nextPage={() => setInitialSkip(prev => prev + 10)}
                previousPage={() => setInitialSkip(prev => Math.max(prev - 10))}
                pageIndex={initialSkip / 10}
                className="card-container ml-auto mt-4 w-fit justify-end p-2 md:hidden"
            />
        </>
    );
};

export default Payment;

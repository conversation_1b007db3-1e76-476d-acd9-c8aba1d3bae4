import {useMemo} from 'react';
import {useRouter} from 'next/router';
import {ColumnDef, FilterFn} from '@tanstack/react-table';
import {UiSpinner} from '@core/components/ui';
import {cls} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {ReceiptIcon} from '@core/icons/solid';
import {Invoice} from './types';
import useDownload from './useDownload';

const dateFilter: FilterFn<any> = (row, columnId, value) => {
    const originalDate = row.getValue(columnId);

    if (Array.isArray(value) && value.length === 0) return row.original;

    let startDate = new Date(value[0]);
    let endDate = new Date(value[1]);
    const time = new Date(originalDate as string);
    return time >= startDate && time <= endDate;
};

const useColumns = () => {
    const router = useRouter();

    const priceFormatter = useMemo(() => {
        return Intl.NumberFormat(router.locale, {
            style: 'decimal',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }, [router.locale]);

    const t = useTrans();

    const {setInvoiceId, isLoading, invoiceId} = useDownload();

    const columns = useMemo<ColumnDef<Invoice>[]>(
        () => [
            {
                header: t('Download'),
                accessorKey: 'id',
                cell: ({row}) => (
                    <span
                        onClick={() => setInvoiceId(row.original.id)}
                        className="cursor-pointer transition hover:opacity-70"
                    >
                        {isLoading && row.original.id === invoiceId ? (
                            <UiSpinner size="sm" />
                        ) : (
                            <ReceiptIcon className="h-4 w-4" />
                        )}
                    </span>
                )
            },
            {
                header: t('Code'),
                accessorKey: 'code'
            },
            {
                header: t('Document No'),
                accessorKey: 'documentNo'
            },
            {
                header: t('Issue Date'),
                accessorKey: 'issueDate',
                cell: ({row}) => {
                    const issueDate = new Date(row.original.issueDate);

                    return (
                        <span>
                            {issueDate.toLocaleDateString(router.locale)}
                        </span>
                    );
                }
            },
            {
                header: t('Due Date'),
                accessorKey: 'dueDate',
                cell: ({row}) => {
                    const dueDate = new Date(row.original.dueDate);

                    return (
                        <span>{dueDate.toLocaleDateString(router.locale)}</span>
                    );
                },
                filterFn: dateFilter
            },
            {
                header: t('Sales Person'),
                accessorKey: 'salespersonName'
            },
            {
                header: t('Project Code'),
                accessorKey: 'projectCode'
            },
            {
                header: t('Project Name'),
                accessorKey: 'projectName'
            },
            {
                header: t('Delivery Date'),
                accessorKey: 'deliveryDate',
                cell: ({row}) => {
                    const deliveryDate = new Date(row.original.deliveryDate);

                    return (
                        <span>
                            {deliveryDate.toLocaleDateString(router.locale)}
                        </span>
                    );
                }
            },
            {
                header: t('Sub Total'),
                accessorKey: 'subTotal',
                cell: ({row}) => {
                    const subTotal = row.original.subTotal;

                    return (
                        <p className="whitespace-nowrap text-right font-medium text-red-700">
                            {priceFormatter.format(subTotal)}{' '}
                            {row.original.currencyName}
                        </p>
                    );
                }
            },
            {
                header: t('Tax Total'),
                accessorKey: 'taxTotal',
                cell: ({row}) => {
                    const taxTotal = row.original.taxTotal;

                    return (
                        <p className="whitespace-nowrap text-right font-medium text-green-700">
                            {priceFormatter.format(taxTotal)}{' '}
                            {row.original.currencyName}
                        </p>
                    );
                }
            },
            {
                header: t('Grand Total'),
                accessorKey: 'grandTotal',
                cell: ({row}) => {
                    const grandTotal = row.original.grandTotal;

                    return (
                        <p className="whitespace-nowrap text-right font-medium">
                            {priceFormatter.format(grandTotal)}{' '}
                            {row.original.currencyName}
                        </p>
                    );
                }
            },
            {
                header: t('Payment Status'),
                accessorKey: 'paymentStatus',
                cell: ({row}) => {
                    const paymentStatus = row.original.paymentStatus;

                    return (
                        <span
                            className={cls(
                                'whitespace-nowrap rounded-full px-3 py-1.5 text-xs font-medium',
                                t('Paid') === paymentStatus &&
                                    'bg-green-200 text-green-600',
                                t('Partially paid') === paymentStatus &&
                                    'bg-yellow-200 text-yellow-600',
                                t('Not paid') === paymentStatus &&
                                    'bg-red-200 text-red-600'
                            )}
                        >
                            {paymentStatus}
                        </span>
                    );
                }
            },
            {
                header: t('E-Invoice Status'),
                accessorKey: 'eInvoiceStatus',
                cell: ({row}) => {
                    const eInvoiceStatus = row.original.eInvoiceStatus;

                    return (
                        <span
                            className={cls(
                                'whitespace-nowrap rounded-full px-3 py-1.5 text-xs font-medium',
                                t('None') === eInvoiceStatus &&
                                    'bg-gray-200 text-gray-600',
                                t('Waiting') === eInvoiceStatus &&
                                    'bg-yellow-200 text-yellow-600',
                                t('Being applied') === eInvoiceStatus &&
                                    'bg-blue-200 text-blue-600',
                                t('Approved') === eInvoiceStatus &&
                                    'bg-green-200 text-green-600',
                                t('Refused') === eInvoiceStatus &&
                                    'bg-red-200 text-red-600'
                            )}
                        >
                            {eInvoiceStatus}
                        </span>
                    );
                }
            },
            {
                header: t('Status'),
                accessorKey: 'status',
                cell: ({row}) => {
                    const status = row.original.status;

                    return (
                        <span
                            className={cls(
                                'whitespace-nowrap rounded-full px-3 py-1.5 text-xs font-medium',
                                t('Approved') === status &&
                                    'bg-green-200 text-green-600',
                                t('Draft') === status &&
                                    'bg-yellow-200 text-yellow-600',
                                t('Canceled') === status &&
                                    'bg-red-200 text-red-600',
                                t('Payment Planned') === status &&
                                    'bg-blue-200 text-blue-600'
                            )}
                        >
                            {status}
                        </span>
                    );
                }
            }
        ],
        // eslint-disable-next-line
        [isLoading]
    );

    return columns;
};

export default useColumns;

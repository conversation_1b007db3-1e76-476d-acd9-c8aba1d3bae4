import {jsonRequest} from '@core/helpers';
import {useEffect, useState} from 'react';
import {Invoice} from './types';

const useInvoices = (limit = 10, skip = 0) => {
    const [invoices, setInvoices] = useState<Invoice[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    const total = Math.ceil(invoices.length / 10);

    useEffect(() => {
        (async () => {
            setIsLoading(true);
            try {
                const receiptsData = await jsonRequest({
                    url: '/api/customers/invoices',
                    method: 'POST',
                    data: {limit, skip}
                });

                setInvoices(receiptsData);
            } catch (err) {
                console.error(err);
            } finally {
                setIsLoading(false);
            }
        })();
    }, [limit, skip]);

    return {invoices, isLoading, total};
};

export default useInvoices;

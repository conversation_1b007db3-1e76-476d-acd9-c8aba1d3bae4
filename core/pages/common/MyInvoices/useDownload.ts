import {useEffect, useState} from 'react';
import {useTrans} from '@core/hooks';
import {jsonRequest} from '@core/helpers';
import {notification} from '@core/components/ui';

const useDownload = () => {
    const [invoiceId, setInvoiceId] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const t = useTrans();

    useEffect(() => {
        if (!invoiceId) return;

        (async () => {
            try {
                setIsLoading(true);
                const {invoiceUrl} = await jsonRequest({
                    url: '/api/customers/download-invoice',
                    method: 'POST',
                    data: {invoiceId}
                });

                const blobData = await (await fetch(invoiceUrl)).blob();

                if (blobData.type !== 'application/pdf') {
                    throw new Error('Invoice download error');
                }

                const fileURL = URL.createObjectURL(blobData);
                let link = document.createElement('a');
                link.href = fileURL;
                link.download = `E-Fatura-${invoiceId}.pdf`;
                link.click();
                URL.revokeObjectURL(link.href);
            } catch (err) {
                notification({
                    title: t('Error'),
                    description: t(
                        'An error occurred while downloading the invoice!'
                    ),
                    status: 'error'
                });
                console.error(err);
            } finally {
                setIsLoading(false);
                setInvoiceId('');
            }
        })();
        // eslint-disable-next-line
    }, [invoiceId]);

    return {setInvoiceId, isLoading, invoiceId};
};

export default useDownload;

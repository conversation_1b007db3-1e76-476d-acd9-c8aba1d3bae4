import {memo} from 'react';
import {isDev} from '@core/helpers';
import {Page} from '@core/types';
import {useTrans} from '@core/hooks';
import Seo from '@components/common/Seo';
import MiniCart from '@components/common/MiniCart';

const MobileMyCartPage: Page = memo(() => {
    const t = useTrans();

    return (
        <>
            <Seo title={t('My Cart')} />
            <MiniCart />
        </>
    );
});

if (isDev) {
    MobileMyCartPage.displayName = 'MobileMyCartPage';
}

export default MobileMyCartPage;

import {FC, memo, useCallback, useMemo} from 'react';
import {useRouter} from 'next/router';
import {isDev} from '@core/helpers';
import {NavigationItem} from '@core/types';
import {useMobileViewportDifference, useStore, useTrans} from '@core/hooks';
import Seo from '@components/common/Seo';
import {ChevronRightIcon} from '@core/icons/solid';
import {UiButton, UiLink} from '@core/components/ui';

type MobileCategoriesProps = {
    currentId?: string;
};

const MobileCategories: FC<MobileCategoriesProps> = memo(props => {
    const {currentId} = props;
    const router = useRouter();
    const t = useTrans();
    const {navigation} = useStore();
    const currentItem = useMemo(
        () =>
            navigation.find(navigationItem => navigationItem.id === currentId),
        [currentId, navigation]
    );
    const items = useMemo(() => {
        if (typeof currentItem === 'undefined') {
            return navigation.filter(
                navigationItem =>
                    navigationItem.showInMainMenu &&
                    navigationItem.slug.split('/').length == 1
            );
        }

        return navigation
            .filter(navigationItem => navigationItem.showInMainMenu)
            .filter(
                navigationItem =>
                    navigationItem.slug.includes(currentItem.slug) &&
                    navigationItem.slug.startsWith(currentItem.slug) &&
                    navigationItem.id !== currentItem.id &&
                    navigationItem.slug.split('/').length ==
                        currentItem.slug.split('/').length + 1
            );
    }, [navigation, currentItem]);

    const hasSubItems = useCallback(
        (item: NavigationItem) =>
            navigation.some(
                navigationItem =>
                    navigationItem.showInMainMenu &&
                    navigationItem.slug.includes(item.slug) &&
                    navigationItem.slug !== item.slug
            ),
        [navigation]
    );

    const viewportDifference = useMobileViewportDifference();

    return (
        <>
            {currentItem ? (
                <Seo title={currentItem.name} />
            ) : (
                <Seo title={t('Categories')} />
            )}

            <div
                className="container overflow-y-auto bg-secondary-100"
                style={{
                    height: `calc(100vh - ${viewportDifference}px)`
                }}
            >
                <div className="card-container mt-4 divide-y">
                    {items.map(item => {
                        if (
                            item.type === 'collection' ||
                            item.type === 'story' ||
                            item.type === 'slide'
                        ) {
                            return;
                        }

                        return (
                            <UiLink
                                className="flex w-full items-center justify-between px-4 py-2.5"
                                key={item.id}
                                href={
                                    hasSubItems(item)
                                        ? `/mobile/categories/${item.id}`
                                        : item.href
                                }
                            >
                                <div className="flex items-center gap-3">
                                    {item.svgIcon && (
                                        <div
                                            className="text-primary-600"
                                            dangerouslySetInnerHTML={{
                                                __html: item.svgIcon
                                            }}
                                        />
                                    )}
                                    <p>{item.name}</p>
                                </div>
                                <ChevronRightIcon className="h-3.5 w-3.5 text-muted" />
                            </UiLink>
                        );
                    })}
                </div>

                {currentItem && currentItem.type == 'product-catalog' && (
                    <UiButton
                        variant="solid"
                        color="primary"
                        className="mt-4 w-full"
                        size="lg"
                        onClick={() => router.push(currentItem.href)}
                    >
                        {t('Show All Products')}
                    </UiButton>
                )}
            </div>
        </>
    );
});

if (isDev) {
    MobileCategories.displayName = 'MobileCategories';
}

export default MobileCategories;

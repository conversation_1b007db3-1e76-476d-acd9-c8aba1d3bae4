import {memo, useCallback, useMemo} from 'react';
import {signOut} from 'next-auth/react';
import storeConfig from '~/store.config';
import {isDev} from '@core/helpers';
import {
    useCustomer,
    useIOSDevice,
    useStore,
    useTrans,
    useUI
} from '@core/hooks';
import {UiButton, UiLink} from '@core/components/ui';
import Seo from '@components/common/Seo';
import {
    BagIcon,
    BookmarkIcon,
    BoxOpenIcon,
    ChevronRightIcon,
    HeartIcon,
    InboxIcon,
    InvoicesIcon,
    LocationIcon,
    OrdersIcon,
    PaymentIcon,
    PowerOffIcon,
    ReceiptIcon,
    ReportsIcon,
    StarIcon,
    StoreIcon,
    TagsIcon,
    UserCircleIcon,
    UserIcon
} from '@core/icons/solid';
import ChangeCustomer from '@components/common/ChangeCustomer';

const MobileMenu = memo(() => {
    const {isIOSDevice} = useIOSDevice();
    const {navigation, paymentOnly} = useStore();
    const {openSideBar} = useUI();
    const customer = useCustomer();
    const t = useTrans();

    const firstProductCatalogLink = useMemo(() => {
        return (
            navigation?.find(item => item.type === 'product-catalog')?.href ??
            ''
        );
        // eslint-disable-next-line
    }, []);

    const onOpenCustomerChange = useCallback(() => {
        openSideBar(t('Change Customer'), <ChangeCustomer />);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <>
            <Seo title={t('Menu')} />

            <div
                className="bg-secondary-100"
                style={{
                    height: `calc(100% - ${
                        isIOSDevice
                            ? storeConfig.theme.iosTabBarHeight
                            : storeConfig.theme.mobileTabBarHeight
                    })`
                }}
            >
                <div className="container pt-4">
                    <p className="font-semibold">{t('Main Menu')}</p>
                    <div className="card-container mt-4 divide-y">
                        <UiLink
                            className="flex h-14 w-full items-center justify-between px-4"
                            href="/"
                        >
                            <div className="inline-flex items-center gap-3">
                                <StoreIcon className="h-5 w-5 text-gray-700" />
                                {t('Home')}
                            </div>
                            <ChevronRightIcon className="h-4 w-4 text-muted" />
                        </UiLink>

                        {!paymentOnly && (
                            <>
                                <UiLink
                                    className="flex h-14 w-full items-center justify-between px-4"
                                    href={firstProductCatalogLink}
                                >
                                    <div className="inline-flex items-center gap-3">
                                        <TagsIcon className="h-5 w-5 text-gray-700" />
                                        {t('Products')}
                                    </div>
                                    <ChevronRightIcon className="h-4 w-4 text-muted" />
                                </UiLink>
                                <UiLink
                                    className="flex h-14 w-full items-center justify-between px-4"
                                    href={
                                        isDev
                                            ? '/mobile/my-cart'
                                            : `/mobile/my-cart?t=${Date.now()}`
                                    }
                                >
                                    <div className="inline-flex items-center gap-3">
                                        <BagIcon className="h-5 w-5 text-gray-700" />
                                        {t('My Cart')}
                                    </div>
                                    <ChevronRightIcon className="h-4 w-4 text-muted" />
                                </UiLink>
                                <UiLink
                                    className="flex h-14 w-full items-center justify-between px-4"
                                    href="/quick-order"
                                >
                                    <div className="inline-flex items-center gap-3">
                                        <InboxIcon className="h-5 w-5 text-gray-700" />
                                        {t('Quick Order')}
                                    </div>
                                    <ChevronRightIcon className="h-4 w-4 text-muted" />
                                </UiLink>
                            </>
                        )}

                        <UiLink
                            className="flex h-14 w-full items-center justify-between px-4"
                            href="/payment"
                        >
                            <div className="inline-flex items-center gap-3">
                                <PaymentIcon className="h-5 w-5 text-gray-700" />
                                {t('Payment')}
                            </div>
                            <ChevronRightIcon className="h-4 w-4 text-muted" />
                        </UiLink>
                        {!paymentOnly && (
                            <UiLink
                                className="flex h-14 w-full items-center justify-between px-4"
                                href="/previous-orders"
                            >
                                <div className="inline-flex items-center gap-3">
                                    <BoxOpenIcon className="h-5 w-5 text-gray-700" />
                                    {t('Previous Orders')}
                                </div>
                                <ChevronRightIcon className="h-4 w-4 text-muted" />
                            </UiLink>
                        )}
                    </div>

                    <p className="mt-4 font-semibold">{t('Reports')}</p>
                    <div className="card-container mt-4 divide-y">
                        <UiLink
                            className="flex h-14 w-full items-center justify-between px-4"
                            href="/my-invoices"
                        >
                            <div className="inline-flex items-center gap-3">
                                <InvoicesIcon className="h-5 w-5 text-gray-700" />
                                {t('My Invoices')}
                            </div>
                            <ChevronRightIcon className="h-4 w-4 text-muted" />
                        </UiLink>
                        <UiLink
                            className="flex h-14 w-full items-center justify-between px-4"
                            href="/my-orders"
                        >
                            <div className="inline-flex items-center gap-3">
                                <OrdersIcon className="h-5 w-5 text-gray-700" />
                                {t('My Orders')}
                            </div>
                            <ChevronRightIcon className="h-4 w-4 text-muted" />
                        </UiLink>
                        <UiLink
                            className="flex h-14 w-full items-center justify-between px-4"
                            href="/my-ledger"
                        >
                            <div className="inline-flex items-center gap-3">
                                <ReportsIcon className="h-5 w-5 text-gray-700" />
                                {t('My Ledger')}
                            </div>
                            <ChevronRightIcon className="h-4 w-4 text-muted" />
                        </UiLink>
                    </div>

                    <p className="mt-4 font-semibold">{t('My Account')}</p>
                    <div className="card-container mt-4 divide-y">
                        {customer?.erpUserId && (
                            <button
                                className="flex h-14 w-full items-center justify-between px-4"
                                onClick={onOpenCustomerChange}
                            >
                                <div className="inline-flex items-center gap-3">
                                    <UserCircleIcon className="h-5 w-5 text-gray-700" />
                                    {t('Change Customer')}
                                </div>
                                <ChevronRightIcon className="h-4 w-4 text-muted" />
                            </button>
                        )}

                        {!paymentOnly && (
                            <>
                                <UiLink
                                    className="flex h-14 w-full items-center justify-between px-4"
                                    href="/account/my-orders"
                                >
                                    <div className="inline-flex items-center gap-3">
                                        <ReceiptIcon className="h-5 w-5 text-gray-700" />
                                        {t('My Orders')}
                                    </div>
                                    <ChevronRightIcon className="h-4 w-4 text-muted" />
                                </UiLink>
                                <UiLink
                                    className="flex h-14 w-full items-center justify-between px-4"
                                    href="/account/my-favorites"
                                >
                                    <div className="inline-flex items-center gap-3">
                                        <HeartIcon className="h-5 w-5 text-gray-700" />
                                        {t('My Favorites')}
                                    </div>
                                    <ChevronRightIcon className="h-4 w-4 text-muted" />
                                </UiLink>
                                <UiLink
                                    className="flex h-14 w-full items-center justify-between px-4"
                                    href="/account/my-collections"
                                >
                                    <div className="inline-flex items-center gap-3">
                                        <BookmarkIcon className="h-5 w-5 text-gray-700" />
                                        {t('My Collections')}
                                    </div>
                                    <ChevronRightIcon className="h-4 w-4 text-muted" />
                                </UiLink>
                                <UiLink
                                    className="flex h-14 w-full items-center justify-between px-4"
                                    href="/account/my-reviews"
                                >
                                    <div className="inline-flex items-center gap-3">
                                        <StarIcon className="h-5 w-5 text-gray-700" />
                                        {t('My Reviews')}
                                    </div>
                                    <ChevronRightIcon className="h-4 w-4 text-muted" />
                                </UiLink>
                            </>
                        )}

                        <UiLink
                            className="flex h-14 w-full items-center justify-between px-4"
                            href="/account/my-addresses"
                        >
                            <div className="inline-flex items-center gap-3">
                                <LocationIcon className="h-5 w-5 text-gray-700" />
                                {t('My Addresses')}
                            </div>
                            <ChevronRightIcon className="h-4 w-4 text-muted" />
                        </UiLink>
                        <UiLink
                            className="flex h-14 w-full items-center justify-between px-4"
                            href="/account/my-account"
                        >
                            <div className="inline-flex items-center gap-3">
                                <UserIcon className="h-5 w-5 text-gray-700" />
                                {t('My Account')}
                            </div>
                            <ChevronRightIcon className="h-4 w-4 text-muted" />
                        </UiLink>
                    </div>

                    <UiButton
                        className="mb-6 mt-4 w-full"
                        variant="solid"
                        color="danger"
                        leftIcon={<PowerOffIcon className="mr-3 h-4 w-4" />}
                        onClick={() => signOut()}
                    >
                        {t('Sign Out')}
                    </UiButton>
                </div>
            </div>
        </>
    );
});

if (isDev) {
    MobileMenu.displayName = 'MobileMenu';
}

export default MobileMenu;

import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiImage} from '@core/components/ui';
import Seo from '@components/common/Seo';
import storeConfig from '~/store.config';
import ResetPassword from '@core/components/auth/ResetPassword';

type ResetPasswordProps = {
    token: string;
};

const ResetPasswordPage: FC<ResetPasswordProps> = memo(props => {
    const {token} = props;
    const t = useTrans();

    return (
        <>
            <Seo title={t('Reset Your Password')} />
            <div className="flex h-full w-full flex-col items-center justify-start gap-6 pt-6 xl:pt-32">
                <UiImage
                    src="/site-logo.png"
                    alt={storeConfig.title}
                    width={parseFloat(
                        storeConfig.theme.logoWidth.replace('px', '')
                    )}
                    height={parseFloat(
                        storeConfig.theme.logoHeight.replace('px', '')
                    )}
                    priority
                />

                <div className="relative flex w-full flex-col justify-center bg-white p-8 xl:min-h-[28rem] xl:w-[34rem] xl:rounded-md xl:p-16 xl:shadow-[0_15px_35px_0_rgba(0,0,0,0.1)]">
                    <h2 className="mb-4 text-2xl font-medium text-muted">
                        {t('Reset Your Password')}
                    </h2>

                    <p className="mb-8 text-sm text-gray-800">
                        {t(
                            'Please enter your new password and password confirmation.'
                        )}
                    </p>

                    <ResetPassword token={token} />
                </div>

                <div>
                    <a
                        className="font-medium text-primary-600"
                        href="https://www.entererp.com"
                        target="_blank"
                        rel="noreferrer noopener"
                    >
                        EnterERP
                    </a>{' '}
                    <span className="text-sm">
                        &copy; {new Date().getFullYear()}{' '}
                        {t('All Rights Reserved')}
                    </span>
                </div>
            </div>
        </>
    );
});

if (isDev) {
    ResetPasswordPage.displayName = 'ResetPasswordPage';
}

export default ResetPasswordPage;

import {FC, memo, useState} from 'react';
import {isDev} from '@core/helpers';
import {useMobileViewportDifference, useTrans} from '@core/hooks';
import {UiButton, UiImage} from '@core/components/ui';
import Seo from '@components/common/Seo';
import SignIn from '@core/components/auth/SignIn';
import ForgotPassword from '@core/components/auth/ForgotPassword';
import storeConfig from '~/store.config';

const Auth: FC = memo(() => {
    const t = useTrans();
    const [isForgotPasswordShown, setIsForgotPasswordShown] = useState(false);

    const viewportDifference = useMobileViewportDifference();

    return (
        <>
            <Seo title={t('Sign In')} />

            <div
                className="flex w-full flex-col items-center justify-center gap-6 pt-6 xl:!h-full xl:justify-start xl:pt-32"
                style={{
                    height: `calc(100vh - ${viewportDifference}px)`
                }}
            >
                <UiImage
                    src="/site-logo.png"
                    alt={storeConfig.title}
                    width={parseFloat(
                        storeConfig.theme.logoWidth.replace('px', '')
                    )}
                    height={parseFloat(
                        storeConfig.theme.logoHeight.replace('px', '')
                    )}
                    priority
                />

                <div className="relative flex w-full flex-col justify-center p-8 md:w-1/2 xl:min-h-[28rem] xl:w-[34rem] xl:rounded-md xl:bg-white xl:p-16 xl:shadow-[0_15px_35px_0_rgba(0,0,0,0.1)]">
                    {!isForgotPasswordShown ? (
                        <>
                            <h2 className="mb-8 text-center text-2xl font-medium text-muted">
                                {t('Sign in to your account')}
                            </h2>

                            <SignIn />

                            <div className="mt-4 select-none text-center text-sm">
                                <span
                                    onClick={() =>
                                        setIsForgotPasswordShown(true)
                                    }
                                    className="cursor-pointer font-medium text-primary-600 transition hover:text-muted"
                                >
                                    {t('Forgot Password')}
                                </span>
                            </div>
                        </>
                    ) : (
                        <>
                            <h2 className="mb-4 text-2xl font-medium text-muted">
                                {t('Forgot Password')}
                            </h2>

                            <p className="mb-8 text-sm text-gray-800">
                                {t(
                                    'We need your e-mail address so we can send you the password reset link.'
                                )}
                            </p>

                            <ForgotPassword
                                setIsForgotPasswordShown={
                                    setIsForgotPasswordShown
                                }
                            />

                            <UiButton
                                className="mt-4 block w-full xl:hidden"
                                variant="outline"
                                color="primary"
                                size="lg"
                                onClick={() => setIsForgotPasswordShown(false)}
                            >
                                {t('Sign In')}
                            </UiButton>

                            <div className="mt-4 select-none text-center text-sm">
                                <span
                                    onClick={() =>
                                        setIsForgotPasswordShown(false)
                                    }
                                    className="cursor-pointer font-medium text-primary-600 transition hover:text-muted"
                                >
                                    {t('Return Sign in')}
                                </span>
                            </div>
                        </>
                    )}
                </div>

                <div>
                    <a
                        className="font-medium text-primary-600 transition hover:text-primary-900 active:text-primary-900"
                        href="https://www.entererp.com"
                        target="_blank"
                        rel="noreferrer noopener"
                    >
                        EnterERP
                    </a>{' '}
                    <span className="text-sm">
                        &copy; {new Date().getFullYear()}{' '}
                        {t('All Rights Reserved')}
                    </span>
                </div>
            </div>
        </>
    );
});

if (isDev) {
    Auth.displayName = 'Auth';
}

export default Auth;

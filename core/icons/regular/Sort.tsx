const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            viewBox="0 0 512 512"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            {...props}
        >
            <path d="M477.5 189.1l-71.1-144.1c-8.123-16.32-34.81-16.32-42.94 0l-71.1 144.1c-5.938 11.86-1.125 26.27 10.75 32.21c11.9 5.941 26.25 1.16 32.19-10.76l5.359-10.73h90.34l5.359 10.73c4.219 8.441 12.69 13.29 21.5 13.29c3.594 0 7.25-.8125 10.69-2.535C478.6 215.3 483.4 200.9 477.5 189.1zM362.8 151.8l21.17-42.38l21.17 42.38H362.8zM439.1 431.1H377.1l81.87-105.3c5.625-7.258 6.656-17.08 2.625-25.3c-4.031-8.258-12.41-13.48-21.56-13.48h-111.1c-13.25 0-23.1 10.76-23.1 24.02s10.75 24.02 23.1 24.02h62.94L309.1 441.2c-5.623 7.258-6.654 17.08-2.625 25.3C310.5 474.8 318.8 480 327.1 480h111.1c13.25 0 23.1-10.76 23.1-24.02S453.2 431.1 439.1 431.1zM145.6 39.37c-9.062-9.82-26.19-9.82-35.25 0L14.38 143.4c-9 9.758-8.406 24.96 1.344 33.94C20.35 181.7 26.19 183.8 32 183.8c6.469 0 12.91-2.594 17.62-7.719L104 117.1v338.9C104 469.2 114.8 480 128 480S152 469.2 152 455.1V117.1l54.37 58.95C215.3 185.8 230.5 186.5 240.3 177.4C250 168.4 250.6 153.2 241.6 143.4L145.6 39.37z" />
        </svg>
    );
};

export default Icon;

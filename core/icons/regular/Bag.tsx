const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            viewBox="0 0 512 512"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            {...props}
        >
            <path d="M112 160V112C112 50.14 162.1 0 224 0C285.9 0 336 50.14 336 112V160H400C426.5 160 448 181.5 448 208V416C448 469 405 512 352 512H96C42.98 512 0 469 0 416V208C0 181.5 21.49 160 48 160H112zM160 160H288V112C288 76.65 259.3 48 224 48C188.7 48 160 76.65 160 112V160zM48 208V416C48 442.5 69.49 464 96 464H352C378.5 464 400 442.5 400 416V208H336V264C336 277.3 325.3 288 312 288C298.7 288 288 277.3 288 264V208H160V264C160 277.3 149.3 288 136 288C122.7 288 112 277.3 112 264V208H48z" />
        </svg>
    );
};

export default Icon;

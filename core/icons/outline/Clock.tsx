const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            viewBox="0 0 512 512"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            {...props}
        >
            <path d="M256 0C114.6 0 0 114.6 0 256c0 141.4 114.6 256 256 256c141.4 0 256-114.6 256-256C512 114.6 397.4 0 256 0zM256 480c-123.5 0-224-100.5-224-224s100.5-224 224-224s224 100.5 224 224S379.5 480 256 480zM354.1 294.1L272 246.8V112C272 103.2 264.8 96 256 96S240 103.2 240 112V256c0 5.719 3.047 11 8 13.86l90.06 52C340.6 323.3 343.3 324 346 324c5.531 0 10.91-2.859 13.88-8C364.3 308.3 361.7 298.6 354.1 294.1z" />
        </svg>
    );
};

export default Icon;

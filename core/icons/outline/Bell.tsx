const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            viewBox="0 0 448 512"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            {...props}
        >
            <path d="M433.4 334.8c-28-26.5-49.38-54.38-49.38-148.9c0-79.63-63.38-144.5-144-152.4V16C240 7.125 232.9 0 224 0S208 7.125 208 16v17.5C127.4 41.38 63.1 106.3 63.1 185.9c0 94.5-21.38 122.4-49.38 148.9c-14 13.38-18.38 33.5-11.25 51.25C10.62 404.3 28.12 416 47.99 416h352c19.88 0 37.38-11.75 44.63-29.1C451.8 368.3 447.4 348.1 433.4 334.8zM400 384H47.99c-14.25 0-21.38-16.5-11.38-25.1c34.88-33.25 59.38-70.38 59.38-172.1C95.1 118.5 153.2 64 224 64s128 54.5 128 121.9c0 101.4 24.25 138.8 59.38 172.1C421.4 367.6 414.1 384 400 384zM272.1 448c-7.438 0-14.36 4.146-16.9 10.88C250.6 471.2 238.3 480 223.1 480s-26.61-8.824-31.25-21.12C190.2 452.1 183.3 448 175.8 448c-10.61 0-18.37 9.998-15.08 19.72C169.4 493.4 194.5 512 223.1 512c29.47 0 54.56-18.63 63.22-44.28C290.5 457.1 282.8 448 272.1 448z" />
        </svg>
    );
};

export default Icon;

const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            viewBox="0 0 448 512"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            {...props}
        >
            <path d="M416 128h-96l.001-32c0-52.94-43.06-96-95.1-96C171.1 0 128 43.06 128 96v32H32C14.33 128 0 142.3 0 160v256c0 53.02 42.98 96 96 96h256c53.02 0 96-42.98 96-96V160C448 142.3 433.7 128 416 128zM160 96c0-35.3 28.7-64 64-64c35.29 0 63.1 28.7 63.1 64v32H160V96zM416 416c0 35.29-28.71 64-64 64H96c-35.29 0-64-28.71-64-64V160h96v80C128 248.8 135.2 256 144 256S160 248.8 160 240V160h127.1v80c0 8.844 7.156 16 16 16s16-7.156 16-16L319.1 160H416V416z" />
        </svg>
    );
};

export default Icon;

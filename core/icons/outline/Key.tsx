const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            viewBox="0 0 512 512"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            {...props}
        >
            <path d="M336 0c-97.13 0-176 78.75-176 176C160 191.2 161.1 206.4 165.8 221L7 379.8C2.5 384.3 0 390.4 0 396.6V488C0 501.3 10.75 512 24 512h112C149.3 512 160 501.3 160 488V448h40C213.3 448 224 437.3 224 424V384h19.38c6.25 0 12.38-2.5 16.88-7l30.75-30.75C305.6 350 320.8 351.1 336 351.1c97.13 0 176-78.75 176-176C512 78.87 433.3 0 336 0zM336 320c-19 0-37.13-3.625-53.63-10.38L240 352H192v64H128v64H32v-80l170.4-170.4C195.6 213.1 192 195 192 176C192 96.5 256.5 32 336 32S480 96.5 480 176S415.5 320 336 320zM384 104c-13.25 0-24 10.74-24 24c0 13.25 10.75 24 24 24S408 141.3 408 128C408 114.7 397.3 104 384 104z" />
        </svg>
    );
};

export default Icon;

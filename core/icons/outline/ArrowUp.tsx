const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            viewBox="0 0 384 512"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            {...props}
        >
            <path d="M379.8 234.8C376.7 238.3 372.3 240 368 240c-3.844 0-7.703-1.375-10.77-4.156L208 100.2V464c0 8.844-7.156 15.1-15.1 15.1S176 472.8 176 464V100.2L26.77 235.8c-6.516 5.938-16.62 5.531-22.61-1.094C-1.781 228.2-1.297 218.1 5.234 212.2l176-160c6.094-5.562 15.44-5.562 21.53 0l176 160C385.3 218.1 385.8 228.2 379.8 234.8z" />
        </svg>
    );
};

export default Icon;

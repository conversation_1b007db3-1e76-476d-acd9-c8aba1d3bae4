const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            viewBox="0 0 576 512"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            {...props}
        >
            <path d="M568.1 130.8c-4.406-2.969-9.953-3.562-14.91-1.625l-117.6 46.8c-6.135 15.5-20.12 42.46-19.93 42.39L544 167.6v257.9l-128 47.8V304C416 295.2 408.8 288 400 288S384 295.2 384 304v170.5L192 419.6V304C192 295.2 184.8 288 176 288S160 295.2 160 304v115l-128 53.02V214.5l106.9-39.91C134.7 163.8 131.9 153.6 130.1 143.7L10.41 188.4C4.15 190.8 0 196.8 0 203.4v292.5c0 5.344 2.672 10.35 7.109 13.32s9.972 3.552 14.89 1.521l152.3-63.08l222.2 63.62C397.9 511.8 399.4 512 400.9 512c1.906 0 3.797-.3438 5.594-1l159.1-59.44C571.8 449.2 576 443.2 576 436.6V144C576 138.7 573.4 133.8 568.1 130.8zM288 320c9.551 0 18.6-4.266 24.68-11.63C343.7 270.7 416 177.6 416 126C416 56.54 358.6 0 288 0S160 56.54 160 126C160 177.6 232.3 270.7 263.3 308.4C269.4 315.7 278.4 320 288 320zM288 32c52.88 0 96 42.11 96 94.03C384 153.9 345.9 217.9 288 288C230.1 217.9 192 153.9 192 126C192 74.11 235.1 32 288 32zM312 128c0-13.26-10.75-24-24-24S264 114.7 264 128c0 13.25 10.75 24 24 24S312 141.3 312 128z" />
        </svg>
    );
};

export default Icon;

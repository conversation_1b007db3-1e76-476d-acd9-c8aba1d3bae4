const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            viewBox="0 0 512 512"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            {...props}
        >
            <path d="M256 448C362 448 448 362 448 256C448 149.1 362 64 256 64C149.1 64 64 149.1 64 256C64 259.9 64.12 263.8 64.35 267.7L1.754 286.1C.5958 276.2 0 266.2 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C245.8 512 235.8 511.4 225.9 510.2L244.3 447.6C248.2 447.9 252.1 448 256 448zM278 332.9C311.5 323.4 336 292.5 336 256C336 211.8 300.2 176 256 176C219.5 176 188.6 200.5 179.1 233.1L112 253.7C113.2 175.2 177.2 111.1 256 111.1C335.5 111.1 400 176.5 400 255.1C400 334.8 336.8 398.8 258.3 399.1L278 332.9zM39.01 308.5L243.9 248.3C256 244.7 267.3 255.1 263.7 268.1L203.5 472.1C199.4 486.9 180.3 488.6 173.8 475.6L145.2 418.3C144.5 416.1 143.6 415.7 142.6 414.6L54.63 502.6C42.13 515.1 21.87 515.1 9.373 502.6C-3.124 490.1-3.124 469.9 9.373 457.4L97.36 369.4C96.26 368.4 95.02 367.5 93.68 366.8L36.37 338.2C23.38 331.7 25.07 312.6 39.01 308.5V308.5z" />
        </svg>
    );
};

export default Icon;

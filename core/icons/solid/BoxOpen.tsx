const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            viewBox="0 0 640 512"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            {...props}
        >
            <path d="M481.4 138.3L320 158.5L312.8 157.6C317.4 148.7 320 138.7 320 128C320 92.65 291.3 64 256 64C220.7 64 192 92.65 192 128C192 133.1 192.6 137.1 193.7 142.7L128.2 134.5C128.1 132.3 128 130.2 128 128C128 57.31 185.3 .0002 256 .0002C325.6 .0002 382.2 55.55 383.1 124.7L419.1 31.15C428.4 6.325 456-6.252 480.9 3.057C505.7 12.36 518.3 40.03 508.9 64.85L481.4 138.3zM576 338.3V421.7C576 436.4 566 449.2 551.8 452.8L335.5 506.8C325.3 509.4 314.7 509.4 304.5 506.8L88.24 452.8C73.99 449.2 64 436.4 64 421.7V338.3L191.6 374.7C219.5 382.7 249.2 370.1 264.1 346.1L320 252.9L375.9 346.1C390.8 370.1 420.6 382.7 448.4 374.7L576 338.3zM75.23 160.1L320 190.7L564.8 160.1C571.5 159.3 578 162.8 581.1 168.8L622.8 252.2C631.7 270.2 622.2 291.8 602.9 297.3L439.6 343.1C425.7 347.1 410.8 342.1 403.4 329.7L320 190.7L236.6 329.7C229.2 342.1 214.3 347.1 200.4 343.1L37.07 297.3C17.81 291.8 8.283 270.2 17.24 252.2L58.94 168.8C61.97 162.8 68.5 159.3 75.23 160.1H75.23z" />
        </svg>
    );
};

export default Icon;

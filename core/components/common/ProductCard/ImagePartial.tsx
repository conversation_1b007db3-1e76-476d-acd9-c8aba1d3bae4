import {useMemo} from 'react';
import dynamic from 'next/dynamic';
import storeConfig from '~/store.config';
import {UiImage} from '@core/components/ui';
import {useProductCard} from './context';

const ColorVariantPicker = dynamic(() => import('./ColorVariantPicker'), {
    ssr: false
});

const ImagePartial = () => {
    const {
        isFake,
        product,
        preloadImage,
        setIsImageLoading,
        hasColorPicker,
        selectedVariant
    } = useProductCard();

    const image = useMemo(() => {
        if (isFake) {
            return '';
        } else if (
            hasColorPicker &&
            selectedVariant &&
            Array.isArray(selectedVariant.images) &&
            selectedVariant.images.length > 0
        ) {
            return `${selectedVariant.images[0]}?w=480&q=60`;
        } else if (Array.isArray(product.images) && product.images.length > 0) {
            return `${product.images[0]}?w=480&q=60`;
        } else {
            return '/no-image.png';
        }
    }, [product, isFake, selectedVariant, hasColorPicker]);

    return (
        <div
            className={
                storeConfig.catalog.productImageShape === 'rectangle'
                    ? 'aspect-h-3 aspect-w-2'
                    : 'aspect-h-1 aspect-w-1'
            }
        >
            <div className="overflow-hidden">
                <div className="relative h-full w-full">
                    {!!image && (
                        <UiImage
                            className="rounded-t-md"
                            src={image}
                            alt={product.name}
                            fit="contain"
                            position="center"
                            priority={preloadImage}
                            onLoadingComplete={() => setIsImageLoading(false)}
                            fill
                        />
                    )}

                    {product.hasDiscount && product.discount > 0 && (
                        <p className="absolute bottom-1 left-1 rounded-md bg-discount px-1 py-0.5 text-xs text-white xl:hidden">
                            %{Math.ceil(product.discount)}
                        </p>
                    )}
                </div>

                {hasColorPicker && <ColorVariantPicker />}
            </div>
        </div>
    );
};

export default ImagePartial;

import dynamic from 'next/dynamic';
import {cls} from '@core/helpers';
import ProductSellingOptions from './ProductSellingOptions';
import PricePartial from './PricePartial';
import NamePartial from './NamePartial';
import ImagePartial from './ImagePartial';
import {useProductCard} from './context';
import Rating from './Rating';

const QuickLook = dynamic(() => import('../QuickLook'), {ssr: false});
const FavoriteAction = dynamic(() => import('./FavoriteAction'), {ssr: false});
const AddToCartAction = dynamic(() => import('./AddToCartAction'), {
    ssr: false
});

const ProductCardContainer = ({className}: {className?: string}) => {
    const {
        isFake,
        product,
        isImageLoading,
        hasQuickLook,
        hasSellingOptions,
        hasAddToCart,
        hasRating,
        setColorVariantPicker
    } = useProductCard();

    return (
        <div
            onMouseLeave={() => setColorVariantPicker(undefined)}
            className={cls(
                'hover:shadow-card group relative flex flex-col rounded-md bg-white transition',
                !(isImageLoading || isFake) && 'border',
                className
            )}
        >
            {(isImageLoading || isFake) && (
                <div className="absolute inset-0 z-20 h-full w-full bg-white">
                    <div className="skeleton-card h-full w-full" />
                </div>
            )}

            {hasSellingOptions && <ProductSellingOptions />}

            <div className="absolute right-1 top-1 isolate z-10 flex flex-col gap-1">
                {hasQuickLook && <QuickLook productSlug={product.slug} />}
                <FavoriteAction />
            </div>

            <ImagePartial />

            <div className="flex flex-1 flex-col justify-start px-3 py-1.5 xl:justify-between xl:py-3">
                <NamePartial />

                {hasRating && <Rating />}

                <PricePartial />

                {hasAddToCart && (
                    <div className="mb-1.5 mt-auto xl:mb-0">
                        {product.hasVariants ? (
                            <QuickLook
                                productSlug={product.slug}
                                variant="button"
                            />
                        ) : (
                            <AddToCartAction product={product} />
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};

export default ProductCardContainer;

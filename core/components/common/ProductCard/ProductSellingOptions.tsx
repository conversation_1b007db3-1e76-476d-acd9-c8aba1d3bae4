import {useTrans} from '@core/hooks';
import {useProductCard} from './context';
import {useMemo} from 'react';

const ProductSellingOptions = () => {
    const {isImageLoading, product} = useProductCard();

    const t = useTrans();

    const hasAlternativeStock = useMemo(() => {
        return (
            (product.warehouseStocks ?? []).some(
                w => w.availableQuantity > 0
            ) && product.quantity < 1
        );
    }, [product.quantity, product.warehouseStocks]);

    return (
        <div className="absolute left-1 top-1 z-[2] hidden select-none flex-col gap-1 text-[8px] xl:flex">
            {product.estimatedDeliveryDuration === 1 && !isImageLoading && (
                <p className="w-14 rounded border border-green-600 bg-green-600 text-center leading-[10px] text-white">
                    {t('FAST SHIPPING')}
                </p>
            )}
            {hasAlternativeStock && !isImageLoading && (
                <p className="w-14 rounded border border-green-600 bg-green-600 text-center leading-[10px] text-white">
                    {t('ALTERNATIVE STOCK')}
                </p>
            )}
        </div>
    );
};

export default ProductSellingOptions;

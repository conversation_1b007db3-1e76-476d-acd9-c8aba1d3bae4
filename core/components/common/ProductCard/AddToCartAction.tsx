import {memo, useEffect, useState} from 'react';
import {useRouter} from 'next/router';
import {
    isDev,
    jsonRequest,
    pushIntoGTMDataLayer,
    slugifyProduct
} from '@core/helpers';
import {useCart, useMobile, useStore, useTrans} from '@core/hooks';
import {CartItem, Product, ProductListItem} from '@core/types';
import {BagIcon, XCircleIcon} from '@core/icons/solid';
import {UiButton, notification} from '@core/components/ui';
import ProductCartSummary from '../ProductCartSummary';

type AddToCartActionProps = {
    product: ProductListItem;
};

const AddToCartAction = memo(({product}: AddToCartActionProps) => {
    const [productSlug, setProductSlug] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const {addItem, cart, updateItem} = useCart();
    const {locale, currency} = useStore();
    const router = useRouter();
    const {isMobile} = useMobile();
    const t = useTrans();

    useEffect(() => {
        if (!productSlug) return;

        const {processedSlug} = slugifyProduct(productSlug);

        (async () => {
            setIsLoading(true);
            try {
                const result = await jsonRequest({
                    url: '/api/catalog/product',
                    method: 'POST',
                    data: {
                        slug: processedSlug
                    }
                });

                const product = result.product as Product;

                const cartItem: CartItem = {
                    productId: product.productId,
                    productSlug: product.slug,
                    productImage: (product.images as string[])[0],
                    productName:
                        typeof product.brandName === 'string' &&
                        product.brandName.length > 0
                            ? `${product.brandName} ${product.name}`
                            : product.name,
                    productStockQuantity: product.quantity,
                    productRating: product.rating,
                    productReviewCount: product.reviewCount,
                    productLink: `/${product.slug}`,
                    price: product.salesPrice,
                    unitId: product.unitId,
                    unitName: product.unitName,
                    warehouseId: product.defaultWarehouseId,
                    quantity: 1,
                    weight: product.weight,
                    width: product.width,
                    height: product.height,
                    depth: product.depth,
                    volumetricWeight: 0,
                    deliveryType: 'standard',
                    deliveryOptionIds: product.deliveryOptionIds ?? [],
                    deliveryPrice: 0,
                    estimatedDeliveryDuration:
                        product.estimatedDeliveryDuration,
                    deliveryAtSpecifiedDate: product.deliveryAtSpecifiedDate,
                    deliveryAtSpecifiedTime: product.deliveryAtSpecifiedTime,
                    selected: true,
                    removed: false
                };

                let availableQuantity = product.quantity;
                for (const cartItem of cart.items ?? []) {
                    if (cartItem.productId === product.productId) {
                        availableQuantity -= cartItem.quantity;
                    }
                }

                if (availableQuantity < 1) {
                    notification({
                        title: t('Error'),
                        description: t('Out Of Stock'),
                        status: 'error'
                    });
                    return;
                }

                const inCartProduct = cart.items.find(
                    cartItem => cartItem.productId === product.productId
                );

                let cartResult;
                if (inCartProduct) {
                    cartResult = await updateItem({
                        ...cartItem,
                        quantity: cartItem.quantity + inCartProduct.quantity
                    });
                } else {
                    cartResult = await addItem(cartItem);
                }

                if (cartResult) {
                    notification({
                        title: t('Added to Cart'),
                        description: t('Product has been added to your cart.'),
                        status: 'success',
                        detailRenderer: closeNotification => (
                            <ProductCartSummary
                                locale={locale}
                                currency={currency}
                                item={cartItem}
                                onDetail={() => {
                                    closeNotification();
                                    if (isMobile) {
                                        router.push(
                                            `/mobile/my-cart?t=${Date.now()}`
                                        );
                                    } else {
                                        router.push(`/cart?t=${Date.now()}`);
                                    }
                                }}
                            />
                        )
                    });
                }

                pushIntoGTMDataLayer({
                    event: 'add_to_cart',
                    data: {
                        currency:
                            currency.name === 'TL' ? 'TRY' : currency.name,
                        value: product.unDiscountedSalesPrice
                            ? product.unDiscountedSalesPrice
                            : product.salesPrice,
                        items: [
                            {
                                item_id: product.code,
                                item_name: product.name,
                                discount:
                                    product.unDiscountedSalesPrice > 0
                                        ? product.unDiscountedSalesPrice -
                                          product.salesPrice
                                        : 0,
                                item_brand: product.brandName,
                                item_category: product.categoryName,
                                price: product.unDiscountedSalesPrice
                                    ? product.unDiscountedSalesPrice
                                    : product.salesPrice,
                                quantity: 1
                            }
                        ]
                    }
                });
            } catch (err) {
                console.error(err);
                notification({
                    title: t('Error'),
                    description: t(
                        'An error occurred while adding the product to the cart!'
                    ),
                    status: 'error'
                });
            } finally {
                setIsLoading(false);
                setProductSlug('');
            }
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [productSlug]);

    return (
        <div
            className="relative isolate z-10 xl:opacity-0 xl:transition xl:group-hover:opacity-100"
            onClick={() => {
                if (product.quantity > 0) setProductSlug(product.slug);
            }}
        >
            <UiButton
                className="inline-flex w-full gap-3 focus:!bg-primary-500 focus-visible:ring-2 focus-visible:ring-offset-2"
                variant="solid"
                color={product.quantity > 0 ? 'primary' : 'outline'}
                size="xs"
                leftIcon={
                    product.quantity > 0 ? (
                        <BagIcon className="hidden h-4 w-4 sm:inline-block" />
                    ) : (
                        <XCircleIcon className="hidden h-4 w-4 sm:inline-block" />
                    )
                }
                loading={isLoading}
                disabled={isLoading || product.quantity < 1}
            >
                {product.quantity > 0 ? t('ADD TO CART') : t('Out Of Stock')}
            </UiButton>
        </div>
    );
});

if (isDev) {
    AddToCartAction.displayName = 'AddToCartAction';
}

export default AddToCartAction;

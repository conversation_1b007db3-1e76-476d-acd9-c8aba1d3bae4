import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {ProductListItem} from '@core/types';
import {ProductCardProvider} from './context';
import ProductCardContainer from './ProductCardContainer';

type ProductCardProps = {
    product: ProductListItem;
    preloadImage?: boolean;
    isFavoriteShown?: boolean;
    isUnDiscountedPriceShown?: boolean;
    isFake?: boolean;
    hasColorPicker?: boolean;
    hasQuickLook?: boolean;
    hasSellingOptions?: boolean;
    hasAddToCart?: boolean;
    hasRating?: boolean;
    onRemove?: (product: ProductListItem) => void | Promise<void>;
    className?: string;
};

const ProductCard: FC<ProductCardProps> = memo(
    ({
        product,
        preloadImage = false,
        isUnDiscountedPriceShown = true,
        isFake = false,
        isFavoriteShown = false,
        hasColorPicker = true,
        hasQuickLook = true,
        hasSellingOptions = true,
        hasAddToCart = true,
        hasRating = true,
        onRemove,
        className
    }) => {
        return (
            <ProductCardProvider
                product={product}
                isFake={isFake}
                isFavoriteShown={isFavoriteShown}
                isUnDiscountedPriceShown={isUnDiscountedPriceShown}
                preloadImage={preloadImage}
                hasColorPicker={hasColorPicker}
                hasQuickLook={hasQuickLook}
                hasAddToCart={hasAddToCart}
                hasSellingOptions={hasSellingOptions}
                hasRating={hasRating}
                onRemove={onRemove}
            >
                <ProductCardContainer className={className} />
            </ProductCardProvider>
        );
    }
);

if (isDev) {
    ProductCard.displayName = 'ProductCard';
}

export default ProductCard;

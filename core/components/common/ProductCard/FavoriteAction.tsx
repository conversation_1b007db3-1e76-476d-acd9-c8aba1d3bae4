import {useState, useEffect, useCallback} from 'react';
import {UiSpinner, notification} from '@core/components/ui';
import {useStore, useTrans} from '@core/hooks';
import {HeartIcon, TrashIcon} from '@core/icons/regular';
import {HeartIcon as HeartSolidIcon} from '@core/icons/solid';
import {useProductCard} from './context';

const FavoriteAction = () => {
    const {product, isFavoriteShown, onRemove} = useProductCard();

    const {addToFavorites, removeFromFavorites} = useStore();

    const t = useTrans();

    const [isFavoriteUpdateInProgress, setIsFavoriteUpdateInProgress] =
        useState(false);

    const [isFavorite, setIsFavorite] = useState(() => !!product.isFavorite);

    useEffect(() => setIsFavorite(!!product.isFavorite), [product]);

    const onFavoriteBtnClick = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        if (!isFavorite) {
            await addToFavorites({
                id: product.productId,
                name: product.name,
                image: product.images![0],
                price: product.salesPrice
            });
            setIsFavorite(true);
        } else {
            await removeFromFavorites({
                id: product.productId,
                name: product.name,
                image: product.images![0],
                price: product.salesPrice
            });
            setIsFavorite(false);
        }

        setIsFavoriteUpdateInProgress(false);
    }, [
        product,
        isFavorite,
        isFavoriteUpdateInProgress,
        addToFavorites,
        removeFromFavorites
    ]);

    const [isRemoveInProgress, setIsRemoveInProgress] = useState(false);
    const onRemoveBtnClick = useCallback(async () => {
        if (isRemoveInProgress) {
            return;
        }

        setIsRemoveInProgress(true);

        try {
            await onRemove!(product);
        } catch (error: any) {
            console.log(error);
            notification({
                title: t('Error'),
                description: error.message,
                status: 'error'
            });
        }

        setIsRemoveInProgress(false);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isRemoveInProgress, product, onRemove]);

    return (
        <>
            {isFavoriteShown && (
                <button
                    className="group/inner button-primary shadow-small inline-flex cursor-pointer items-center justify-center p-1.5 text-primary-600 transition hover:bg-secondary-100"
                    onClick={onFavoriteBtnClick}
                >
                    {!isFavoriteUpdateInProgress ? (
                        isFavorite ? (
                            <HeartSolidIcon className="h-4 w-4" />
                        ) : (
                            <HeartIcon className="h-4 w-4" />
                        )
                    ) : (
                        <UiSpinner size="sm" />
                    )}
                </button>
            )}

            {typeof onRemove !== 'undefined' && (
                <button
                    className="group/inner button-primary shadow-small inline-flex cursor-pointer items-center justify-center p-1.5 text-danger-600 transition hover:bg-secondary-100"
                    onClick={onRemoveBtnClick}
                >
                    {!isRemoveInProgress ? (
                        <TrashIcon className="h-4 w-4" />
                    ) : (
                        <UiSpinner size="sm" />
                    )}
                </button>
            )}
        </>
    );
};

export default FavoriteAction;

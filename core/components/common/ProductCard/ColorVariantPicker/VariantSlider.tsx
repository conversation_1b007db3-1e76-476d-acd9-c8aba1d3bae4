import {useState} from 'react';
import {Navigation} from 'swiper';
import {UiImage, UiLink, UiSlider} from '@core/components/ui';
import {cls} from '@core/helpers';
import {useMobile} from '@core/hooks';
import {useProductCard} from '../context';

type VariantSliderProps = {
    slidesPerViewCount?: number;
};

const VariantSlider = ({slidesPerViewCount}: VariantSliderProps) => {
    const [isVariantImageLoading, setIsVariantImageLoading] = useState(true);

    const {productVariants, setSelectedVariant} = useProductCard();

    const {isMobile} = useMobile();

    return (
        <div className="color-variant-slider">
            <UiSlider
                className={cls('w-full select-none', {
                    'my-2 h-40': isMobile,
                    'mt-1 h-16': !isMobile
                })}
                slidesPerView={isMobile ? 4 : slidesPerViewCount ?? 4}
                spaceBetween={4}
                modules={[Navigation]}
            >
                {productVariants.map(variant => {
                    return (
                        <UiSlider.Slide
                            key={variant.code}
                            className={cls(!isMobile && 'relative h-28')}
                            onMouseEnter={() => {
                                if (isMobile) return;
                                setSelectedVariant(variant);
                            }}
                        >
                            <>
                                <UiLink href={variant.slug ?? ''}>
                                    <UiImage
                                        className="cursor-pointer rounded border hover:border-primary-600"
                                        src={
                                            Array.isArray(variant.images) &&
                                            variant.images.length > 0
                                                ? `${variant.images[0]}?w=180&q=100`
                                                : '/no-image.png'
                                        }
                                        alt=""
                                        fit="cover"
                                        position="center"
                                        onLoadingComplete={() =>
                                            setIsVariantImageLoading(false)
                                        }
                                        fill
                                    />
                                </UiLink>

                                {isVariantImageLoading && (
                                    <div className="absolute inset-0 z-20 h-full w-full bg-white">
                                        <div className="skeleton-card h-full w-full" />
                                    </div>
                                )}
                            </>
                        </UiSlider.Slide>
                    );
                })}
            </UiSlider>
        </div>
    );
};

export default VariantSlider;

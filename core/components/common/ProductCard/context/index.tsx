import React, {FC, memo, useEffect, useMemo, useState} from 'react';
import {isDev, jsonRequest, slugifyProduct} from '@core/helpers';
import {Product, ProductVariant} from '@core/types';
import {
    ColorVariantPicker,
    ProductCardContext as ContextType,
    ProductCardProviderProps,
    Status
} from './types';

const ProductCardContext = React.createContext<ContextType | undefined>(
    undefined
);

export const ProductCardProvider: FC<ProductCardProviderProps> = memo(
    ({
        children,
        product,
        isFake,
        isFavoriteShown,
        isUnDiscountedPriceShown,
        preloadImage,
        hasAddToCart,
        hasColorPicker,
        hasQuickLook,
        hasSellingOptions,
        hasRating,
        onRemove
    }) => {
        const [productItem, setProductItem] = useState<Product>();
        const [status, setStatus] = useState<Status>('resolved');
        const [isImageLoading, setIsImageLoading] = useState(true);
        const [colorVariantPicker, setColorVariantPicker] =
            useState<ColorVariantPicker>(undefined);
        const [selectedVariant, setSelectedVariant] =
            useState<ProductVariant>();
        const [productVariants, setProductVariants] = useState<
            ProductVariant[]
        >([]);

        const {processedSlug} = slugifyProduct(product.slug ?? '');

        useEffect(() => {
            (async () => {
                try {
                    if (!colorVariantPicker) return;
                    setStatus('pending');

                    const result = await jsonRequest({
                        url: '/api/catalog/product',
                        method: 'POST',
                        data: {slug: processedSlug}
                    });
                    setProductItem(result.product as Product);
                    setStatus('resolved');
                } catch (err) {
                    setStatus('rejected');
                }
            })();
        }, [processedSlug, colorVariantPicker]);

        useEffect(() => {
            const newProductVariants: ProductVariant[] = [];
            const colors = new Set<string>();

            productItem?.variants?.forEach(product => {
                if (colors.has(product.attributes.color)) return;
                colors.add(product.attributes.color);
                newProductVariants.push(product);
            });

            setProductVariants(newProductVariants);
        }, [productItem]);

        const value = useMemo(
            () => ({
                product,
                status,
                colorVariantPicker,
                isFake,
                isFavoriteShown,
                isUnDiscountedPriceShown,
                preloadImage,
                productItem,
                isImageLoading,
                hasAddToCart,
                hasColorPicker,
                hasQuickLook,
                hasSellingOptions,
                hasRating,
                selectedVariant,
                productVariants,

                setStatus,
                setProductItem,
                setColorVariantPicker,
                setIsImageLoading,
                setSelectedVariant,
                onRemove
            }),
            [
                product,
                status,
                colorVariantPicker,
                isFake,
                isFavoriteShown,
                isUnDiscountedPriceShown,
                preloadImage,
                productItem,
                isImageLoading,
                hasAddToCart,
                hasColorPicker,
                hasQuickLook,
                hasSellingOptions,
                hasRating,
                selectedVariant,
                productVariants,
                onRemove
            ]
        );

        return (
            <ProductCardContext.Provider value={value}>
                {children}
            </ProductCardContext.Provider>
        );
    }
);

if (isDev) {
    ProductCardProvider.displayName = 'ProductCardProvider';
}

export function useProductCard() {
    const productCardContext = React.useContext(ProductCardContext);

    if (productCardContext === undefined) {
        throw new Error(
            'useProductCard must be used inside ProductCardProvider.'
        );
    }

    return productCardContext;
}

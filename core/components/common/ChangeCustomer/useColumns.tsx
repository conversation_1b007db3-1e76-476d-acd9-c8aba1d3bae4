import {useMemo} from 'react';
import {ColumnDef} from '@tanstack/react-table';
import {useTrans} from '@core/hooks';
import {Customer} from './types';

const useColumns = () => {
    const t = useTrans();

    const columns = useMemo<ColumnDef<Customer>[]>(
        () => [
            {
                header: t('Code'),
                accessorKey: 'code'
            },
            {
                header: t('Name'),
                accessorKey: 'name'
            }
        ],
        // eslint-disable-next-line
        []
    );
    return columns;
};

export default useColumns;

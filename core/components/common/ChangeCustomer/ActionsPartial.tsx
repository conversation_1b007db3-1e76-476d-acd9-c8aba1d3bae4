import {useState} from 'react';
import {Cookies} from 'react-cookie-consent';
import {signOut, signIn} from 'next-auth/react';
import {UiButton, UiPortal, notification} from '@core/components/ui';
import {useCustomer, useTrans, useUI} from '@core/hooks';
import {Customer} from './types';

const ActionsPartial = ({selectedCustomer}: {selectedCustomer?: Customer}) => {
    const [isChangingCustomer, setIsChangingCustomer] = useState(false);

    const {closeModal, closeSideBar} = useUI();
    const customer = useCustomer();
    const t = useTrans();

    return (
        <>
            <div className="mb-4 flex items-center justify-end gap-4">
                <UiButton
                    variant="outline"
                    size="lg"
                    onClick={() => {
                        closeModal();
                        closeSideBar();
                    }}
                >
                    {t('Cancel')}
                </UiButton>
                <UiButton
                    variant="solid"
                    color="primary"
                    size="lg"
                    loading={isChangingCustomer}
                    disabled={
                        isChangingCustomer ||
                        !selectedCustomer ||
                        customer?.id === selectedCustomer._id
                    }
                    onClick={async () => {
                        try {
                            if (!selectedCustomer?.email) {
                                throw new Error(
                                    'Selected customer does not have email!'
                                );
                            } else if (!selectedCustomer?.hashedPassword) {
                                throw new Error(
                                    'Selected customer does not have password!'
                                );
                            }

                            setIsChangingCustomer(true);
                            Cookies.remove('cart-id');
                            await signOut({redirect: false});
                            await signIn('credentials', {
                                erpUserId: customer?.erpUserId,
                                email: selectedCustomer?.email,
                                password: selectedCustomer?.hashedPassword,
                                changeCustomer: true,
                                redirect: true,
                                callbackUrl: '/'
                            });
                        } catch (error: any) {
                            notification({
                                title: t('Error'),
                                description: t(
                                    error.message ?? 'Something went wrong!'
                                ),
                                status: 'error'
                            });
                            setIsChangingCustomer(false);
                        }
                    }}
                >
                    {t('Save')}
                </UiButton>
            </div>

            {isChangingCustomer && (
                <UiPortal>
                    <div className="fixed inset-0 z-50 bg-gray-900 bg-opacity-20 transition-opacity" />
                </UiPortal>
            )}
        </>
    );
};

export default ActionsPartial;

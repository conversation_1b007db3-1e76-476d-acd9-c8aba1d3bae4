import {useEffect, useState} from 'react';
import {
    flexRender,
    getCoreRowModel,
    useReactTable
} from '@tanstack/react-table';
import {
    UiRadio,
    UiSpinner,
    UiTable,
    UiTableBody,
    UiTableCell,
    UiTableHead,
    UiTableHeader,
    UiTableRow
} from '@core/components/ui';
import {useCustomer, useIntersection, useTrans} from '@core/hooks';
import {jsonRequest} from '@core/helpers';
import {ExclamationCircleIcon} from '@core/icons/solid';
import useDebouncedValue from '@core/hooks/useDebouncedValue';
import {SkeletonRows, EmptyTableRow} from '@components/common/DataTable';
import Seo from '@components/common/Seo';
import useColumns from './useColumns';
import {Customer} from './types';
import ActionsPartial from './ActionsPartial';

const PAGE_SIZE = 10;

const ChangeCustomer = () => {
    const [customers, setCustomers] = useState<Customer[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [noCustomers, setNoCustomers] = useState(false);
    const [searchValue, setSearchValue] = useState('');
    const [selectedCustomer, setSelectedCustomer] = useState<Customer>();
    const [skip, setSkip] = useState(0);

    const customer = useCustomer();
    const columns = useColumns();
    const t = useTrans();

    useEffect(() => {
        if (noCustomers) return;
        (async () => {
            try {
                if (skip > 0) setIsLoadingMore(true);
                const allCustomers = await jsonRequest({
                    url: '/api/customers/all-customers',
                    method: 'POST',
                    data: {
                        erpUserId: customer?.erpUserId,
                        limit: PAGE_SIZE,
                        skip: skip * PAGE_SIZE
                    }
                });

                if (Array.isArray(allCustomers) && allCustomers.length === 0) {
                    setNoCustomers(true);
                } else {
                    setCustomers(prev => [...prev, ...allCustomers]);
                }
            } catch (err) {
                console.log(err);
            } finally {
                setIsLoading(false);
                setIsLoadingMore(false);
            }
        })();
    }, [customer, skip, noCustomers]);

    const [debouncedSearch] = useDebouncedValue(searchValue, 300);
    const [searchedCustomers, setSearchedCustomers] = useState<Customer[]>([]);
    const hasSearch = debouncedSearch.length >= 2;

    useEffect(() => {
        if (!hasSearch) return;
        (async () => {
            try {
                setIsLoading(true);
                const foundCustomers = await jsonRequest({
                    url: '/api/customers/all-customers',
                    method: 'POST',
                    data: {
                        erpUserId: customer?.erpUserId,
                        limit: 100,
                        search: debouncedSearch
                    }
                });

                if (
                    Array.isArray(foundCustomers) &&
                    foundCustomers.length === 0
                ) {
                    setSearchedCustomers([]);
                } else {
                    setSearchedCustomers(foundCustomers);
                }
            } catch (err) {
                console.log(err);
            } finally {
                setIsLoading(false);
            }
        })();
    }, [customer, debouncedSearch, hasSearch]);

    const [data, setData] = useState<Customer[]>([]);
    const [isTableChanging, setIsTableChanging] = useState(false);

    useEffect(() => {
        setIsTableChanging(true);
        if (hasSearch) {
            setData(searchedCustomers);
        } else {
            setData(customers);
        }
        setIsTableChanging(false);
    }, [customers, hasSearch, searchedCustomers]);

    const table = useReactTable({
        data,
        // @ts-ignore
        columns,
        manualPagination: true,
        getCoreRowModel: getCoreRowModel(),
        initialState: {
            pagination: {
                pageSize: PAGE_SIZE
            }
        }
    });

    const [ref, observer] = useIntersection({
        threshold: 0
    });

    useEffect(() => {
        const shouldFetchMore =
            !(customers.length < PAGE_SIZE) &&
            !isLoading &&
            !isLoadingMore &&
            !hasSearch;

        if (observer?.isIntersecting && shouldFetchMore) {
            setSkip(prev => prev + 1);
        }
    }, [observer, customers, isLoading, isLoadingMore, hasSearch]);

    return (
        <div className="container">
            <Seo title={t('My Orders')} />

            <div className="mb-4 max-md:space-y-2 md:flex md:items-center md:justify-end md:gap-3">
                <p className="mr-auto hidden text-lg font-medium xl:block">
                    {t('Customers')}
                </p>
                <input
                    onChange={e => {
                        setSearchValue(e.target.value);
                    }}
                    value={searchValue}
                    className="button-primary form-input mr-3 w-full max-w-xs origin-right py-1.5 text-sm text-gray-700 transition duration-300 focus:border-primary-600 focus:!ring-primary-600"
                    placeholder={t('Search...')}
                />
            </div>

            <div className="mb-4 cursor-default overflow-hidden rounded-lg border bg-white">
                <div className="relative max-h-[33rem] overflow-auto">
                    <UiTable className="table-fixed">
                        <UiTableHeader>
                            {table.getHeaderGroups().map(headerGroup => (
                                <UiTableRow
                                    key={headerGroup.id}
                                    className="[&_th:nth-child(2)]:w-48 [&_th:nth-child(3)]:w-96"
                                >
                                    <UiTableHead className="w-16 whitespace-nowrap bg-gray-50 text-xs font-semibold text-gray-700">
                                        {t('Select')}
                                    </UiTableHead>
                                    {headerGroup.headers.map(header => (
                                        <UiTableHead
                                            key={header.id}
                                            className="whitespace-nowrap bg-gray-50 text-xs font-semibold text-gray-700"
                                        >
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                      header.column.columnDef
                                                          .header,
                                                      header.getContext()
                                                  )}
                                        </UiTableHead>
                                    ))}
                                </UiTableRow>
                            ))}
                        </UiTableHeader>
                        <UiTableBody>
                            {isLoading || isTableChanging ? (
                                <SkeletonRows rowSize={PAGE_SIZE} />
                            ) : table.getRowModel().rows?.length > 0 ? (
                                table.getRowModel().rows.map(row => (
                                    <UiTableRow
                                        key={row.id}
                                        className="cursor-pointer font-medium transition hover:bg-secondary-100 [&>td]:align-top"
                                        onClick={() =>
                                            setSelectedCustomer(row.original)
                                        }
                                    >
                                        <UiTableCell className="inline-flex w-16 justify-center">
                                            <UiRadio
                                                readOnly
                                                value={selectedCustomer?._id}
                                                checked={
                                                    selectedCustomer?._id ===
                                                    row.original._id
                                                }
                                                disabled={
                                                    selectedCustomer?._id !==
                                                        row.original._id &&
                                                    selectedCustomer !==
                                                        undefined
                                                }
                                            />
                                        </UiTableCell>
                                        {row.getVisibleCells().map(cell => (
                                            <UiTableCell key={cell.id}>
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </UiTableCell>
                                        ))}
                                    </UiTableRow>
                                ))
                            ) : (
                                <EmptyTableRow
                                    colSize={table.getAllColumns().length + 1}
                                    icon={
                                        <ExclamationCircleIcon className="h-8 w-8" />
                                    }
                                    title="No customers found!"
                                    description="There are no customers related to your account."
                                />
                            )}
                        </UiTableBody>
                    </UiTable>

                    {!isLoading && !isLoadingMore && !noCustomers && (
                        <div ref={ref} className="h-px bg-transparent" />
                    )}
                    {isLoadingMore && (
                        <UiSpinner size="lg" className="mx-auto my-4" />
                    )}
                    {noCustomers &&
                        table.getRowModel().rows?.length > 0 &&
                        !hasSearch && (
                            <div className="relative m-4">
                                <div className="absolute top-1/2 h-px w-full -translate-y-1/2 bg-secondary-300" />
                                <div className="relative mx-auto w-fit bg-white px-2 text-xs font-medium md:px-4 md:text-sm">
                                    {t('No more customers found to show.')}
                                </div>
                            </div>
                        )}
                </div>
            </div>

            <ActionsPartial selectedCustomer={selectedCustomer} />
        </div>
    );
};

export default ChangeCustomer;

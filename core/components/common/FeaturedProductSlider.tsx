import {FC, memo, useMemo, useRef} from 'react';
import {isDev, randomId} from '@core/helpers';
import {ProductListItem} from '@core/types';
import {useTrans} from '@core/hooks';
import {UiSlider} from '@core/components/ui';
import {Autoplay, Navigation, Pagination} from '@core/components/ui/Slider';
import ProductCard from '@components/common/ProductCard';
import SwiperCore from 'swiper';
import {ChevronLeftIcon, ChevronRightIcon} from '@core/icons/solid';

type FeatuedProductSliderProps = {
    title: string;
    products: ProductListItem[];
};

const HOW_MANY_FAKE_PRODUCTS = 5;

const FeatuedProductSlider: FC<FeatuedProductSliderProps> = memo(
    ({products, title}) => {
        const t = useTrans();
        const swiperRef = useRef<SwiperCore>();

        const validatedProducts = useMemo(() => {
            if (Array.isArray(products) && products.length > 0) {
                return products;
            } else {
                return [...Array(HOW_MANY_FAKE_PRODUCTS)].map(
                    () =>
                        ({
                            productId: randomId(16),
                            isFake: true
                        } as any)
                );
            }
        }, [products]);

        return (
            <div className="card-container mt-4 select-none p-4">
                {products?.length === 0 ? (
                    <div className="skeleton-card mb-4 h-9 w-full"></div>
                ) : (
                    <div className="mb-4 flex flex-wrap items-center justify-between gap-4">
                        <p className="text-xl font-medium">{t(title)}</p>

                        <div className="flex items-center gap-3">
                            <button
                                className="featured-button"
                                onClick={() => swiperRef.current?.slidePrev()}
                            >
                                <ChevronLeftIcon className="h-4 w-4" />
                            </button>
                            <button className="swiper-pagination flex cursor-pointer items-center justify-center space-x-1.5"></button>
                            <button
                                className="featured-button"
                                onClick={() => swiperRef.current?.slideNext()}
                            >
                                <ChevronRightIcon className="h-4 w-4" />
                            </button>
                        </div>
                    </div>
                )}

                <UiSlider
                    className="[&>div>div]:h-[initial] [&>div>div]:p-1 [&>div]:items-stretch"
                    modules={[Autoplay, Navigation, Pagination]}
                    autoplay={{
                        delay: 5000
                    }}
                    pagination={{
                        el: '.swiper-pagination',
                        clickable: true,
                        renderBullet: function (_, className) {
                            return '<span class=' + className + '></span>';
                        }
                    }}
                    spaceBetween={8}
                    slidesPerView={2}
                    slidesPerGroup={2}
                    onSwiper={swiper => (swiperRef.current = swiper)}
                    threshold={2}
                    breakpoints={{
                        1280: {
                            spaceBetween: 16,
                            slidesPerView: 5,
                            slidesPerGroup: 5
                        }
                    }}
                >
                    {validatedProducts.map(product => (
                        <UiSlider.Slide key={product.productId}>
                            <ProductCard
                                className="h-full"
                                product={product}
                                isFake={product.isFake}
                            />
                        </UiSlider.Slide>
                    ))}
                </UiSlider>
            </div>
        );
    }
);

if (isDev) {
    FeatuedProductSlider.displayName = 'FeatuedProductSlider';
}

export default FeatuedProductSlider;

import {FC, memo, useEffect, useState} from 'react';
import {useForm} from 'react-hook-form';
import {isDev} from '@core/helpers';
import {useCart, useTrans} from '@core/hooks';
import {UiButton, UiForm, UiInput} from '@core/components/ui';

type CouponApplicationFormProps = {
    disabled?: boolean;
};

const CouponApplicationForm: FC<CouponApplicationFormProps> = memo(
    ({disabled}) => {
        const t = useTrans();
        const {applyCouponCode} = useCart();
        const {
            register,
            handleSubmit,
            formState: {isSubmitting},
            watch,
            reset
        } = useForm();
        const [errorMessage, setErrorMessage] = useState('');
        const couponCode = watch('couponCode');

        useEffect(() => {
            setErrorMessage('');
        }, [couponCode]);

        const onSubmit = async (data: Record<string, any>) => {
            try {
                await applyCouponCode(data.couponCode);

                reset();
            } catch (error: any) {
                setErrorMessage(t(error.message));
            }
        };

        return (
            <>
                <UiForm
                    onSubmit={handleSubmit(onSubmit)}
                    className="mt-4 flex items-center justify-between gap-4"
                >
                    <UiInput
                        className="w-9/12 rounded-md"
                        placeholder={t('Enter your discount code')}
                        {...register('couponCode', {required: true})}
                    />
                    <UiButton
                        className="button-primary"
                        variant="solid"
                        loading={isSubmitting}
                        disabled={isSubmitting || disabled}
                    >
                        {t('Apply')}
                    </UiButton>
                </UiForm>

                {errorMessage && (
                    <div className="my-2 text-xs text-red-600 xl:text-sm">
                        {errorMessage}
                    </div>
                )}
            </>
        );
    }
);

if (isDev) {
    CouponApplicationForm.displayName = 'CouponApplicationForm';
}

export default CouponApplicationForm;

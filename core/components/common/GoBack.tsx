import {useTrans} from '@core/hooks';
import {ChevronLeftIcon} from '@core/icons/solid';
import {useRouter} from 'next/router';

const GoBack = () => {
    const t = useTrans();

    const router = useRouter();

    return (
        <div
            onClick={() => router.back()}
            className="mb-4 hidden cursor-pointer select-none items-center gap-1 text-sm font-semibold text-primary-600 transition hover:opacity-70 xl:flex"
        >
            <ChevronLeftIcon className="h-4 w-4" />
            <p>{t('Go Back')}</p>
        </div>
    );
};

export default GoBack;

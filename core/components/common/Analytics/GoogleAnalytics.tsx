import {useEffect} from 'react';
import {useRouter} from 'next/router';
import Script from 'next/script';

const G_TAG_ID = process.env.NEXT_PUBLIC_G_TAG_ID ?? '';
const G_ANALYTIC_ID = process.env.NEXT_PUBLIC_G_ANALYTIC_ID ?? '';

const GoogleAnalytics = () => {
    const router = useRouter();

    useEffect(() => {
        if (!('gtag' in window)) return;

        window.gtag('config', G_TAG_ID, {
            page_path: router.pathname
        });

        const handleRouteChange = () => {
            window.gtag('config', G_TAG_ID, {
                page_path: router.pathname
            });
        };

        router.events.on('routeChangeComplete', handleRouteChange);

        return () => {
            router.events.off('routeChangeComplete', handleRouteChange);
        };
    }, [router.events, router.pathname]);

    if (G_TAG_ID.length < 6) return null;

    return (
        <>
            <Script
                id="google-tag-manager"
                strategy="afterInteractive"
                src={`https://www.googletagmanager.com/gtag/js?id=${G_TAG_ID}`}
            />

            <Script
                id="google-analytics"
                strategy="afterInteractive"
                dangerouslySetInnerHTML={{
                    __html: `window.dataLayer = window.dataLayer || [];
                            function gtag(){dataLayer.push(arguments);}
                            gtag('js', new Date());
                            gtag('config', '${G_ANALYTIC_ID}');`
                }}
            />
        </>
    );
};

export default GoogleAnalytics;

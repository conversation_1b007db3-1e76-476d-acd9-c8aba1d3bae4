import {FC, memo, useEffect, useMemo, useState} from 'react';
import NextHead from 'next/head';
import Script from 'next/script';
import {DefaultSeo} from 'next-seo';
import {isDev} from '@core/helpers';
import storeConfig from '~/store.config';
import {useViewportSize} from '@core/hooks';

const Head: FC = memo(() => {
    const {title, description, titleTemplate} = storeConfig;

    const {width: viewportWidth} = useViewportSize();
    const [isMobile, setIsMobile] = useState(false);
    const isMobileViewport = useMemo(
        () => viewportWidth > 0 && viewportWidth < 1024,
        [viewportWidth]
    );
    useEffect(() => {
        if (isMobileViewport) {
            setIsMobile(true);
        }
    }, [isMobileViewport]);

    return (
        <>
            <DefaultSeo
                defaultTitle={title}
                titleTemplate={titleTemplate}
                description={description}
                dangerouslySetAllPagesToNoFollow
                dangerouslySetAllPagesToNoIndex
            />

            <NextHead>
                {isMobile ? (
                    <meta
                        name="viewport"
                        content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no, user-scalable=no, viewport-fit=cover"
                    />
                ) : (
                    <meta
                        name="viewport"
                        content="width=device-width, initial-scale=1"
                    />
                )}
                <meta name="robots" content="max-image-preview:large" />
                <meta name="format-detection" content="telephone=no" />

                <meta name="application-name" content={title} />
                <meta name="apple-mobile-web-app-capable" content="yes" />
                <meta
                    name="apple-mobile-web-app-status-bar-style"
                    content="default"
                />
                <meta name="apple-mobile-web-app-title" content={title} />
                <meta name="mobile-web-app-capable" content="yes" />
                <meta name="theme-color" content="#ffffff" />
                <link rel="apple-touch-icon" href="/icons/icon-512x512.png" />
                <link
                    rel="apple-touch-icon"
                    sizes="192x192"
                    href="/icons/icon-192x192.png"
                />
                <link
                    rel="apple-touch-icon"
                    sizes="256x256"
                    href="/icons/icon-256x256.png"
                />
                <link
                    rel="apple-touch-icon"
                    sizes="384x384"
                    href="/icons/icon-384x384.png"
                />
                <link
                    rel="apple-touch-icon"
                    sizes="512x512"
                    href="/icons/icon-512x512.png"
                />
                <link
                    rel="mask-icon"
                    href="/icons/icon-512x512.png"
                    color="#ffffff"
                />
                <link rel="manifest" href="/manifest.json" />
                {/* Tell the browser to never restore the scroll position on load */}
                <Script
                    id="scroll-restoration"
                    dangerouslySetInnerHTML={{
                        __html: `history.scrollRestoration = "manual"`
                    }}
                />
            </NextHead>
        </>
    );
});

if (isDev) {
    Head.displayName = 'Head';
}

export default Head;

import {FC, memo} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiListBox} from '@core/components/ui';
import {useCart, useTrans} from '@core/hooks';
import {ChevronDownIcon, ChevronUpIcon} from '@core/icons/outline';
import {useQuickLook} from '../context';

const WarehouseSelection: FC = memo(() => {
    const t = useTrans();
    const {
        selectedProduct,
        isAddToCartInProgress,
        selectedWarehouseId,
        setSelectedWarehouseId
    } = useQuickLook();
    const {cart} = useCart();

    const warehouseStocks = selectedProduct?.warehouseStocks;

    if (!Array.isArray(warehouseStocks) || warehouseStocks.length < 1) {
        return null;
    }

    const selectedWarehouse = warehouseStocks.find(
        w => w.warehouseId === selectedWarehouseId
    );

    const inCartQuantity =
        cart.items.find(
            item =>
                item.productId === selectedProduct.productId &&
                item.warehouseId === selectedWarehouseId
        )?.quantity ?? 0;

    return (
        <UiListBox
            value={selectedWarehouseId}
            onChange={setSelectedWarehouseId}
            as="div"
            className="relative w-full min-w-full space-y-1 xl:mb-6"
            disabled={isAddToCartInProgress}
        >
            {({open}) => (
                <>
                    <UiListBox.Button
                        className={cls(
                            'relative inline-flex h-9 w-full cursor-pointer appearance-none items-center rounded border border-gray-100 bg-gray-100 px-3 py-0 pr-6 text-sm font-medium shadow-sm transition hover:border-gray-300 focus:outline-none',
                            {
                                'border-primary-600 !bg-white ring-1 ring-primary-600':
                                    open
                            }
                        )}
                    >
                        <span className="truncate text-sm">
                            {selectedWarehouse?.warehouseName ??
                                t('Please select a store')}
                        </span>
                        <span className="ml-auto mr-3">
                            (
                            {Math.max(
                                (selectedWarehouse?.availableQuantity ?? 0) -
                                    inCartQuantity,
                                0
                            )}
                            )
                        </span>
                        <span className="pointer-events-none absolute right-2 ml-3 flex items-center">
                            {open ? (
                                <ChevronUpIcon
                                    className="h-3 w-3 text-primary-600"
                                    aria-hidden="true"
                                />
                            ) : (
                                <ChevronDownIcon
                                    className="h-3 w-3 text-muted"
                                    aria-hidden="true"
                                />
                            )}
                        </span>
                    </UiListBox.Button>
                    <UiListBox.Options className="absolute left-0 top-9 z-40 max-h-64 w-full origin-bottom-left overflow-auto rounded border border-gray-200 bg-white p-1.5 shadow-sm outline-none">
                        {warehouseStocks.map(warehouse => {
                            const inCartQuantity =
                                cart.items.find(
                                    item =>
                                        item.productId ===
                                            selectedProduct.productId &&
                                        item.warehouseId ===
                                            warehouse.warehouseId
                                )?.quantity ?? 0;

                            const availableQuantity = Math.max(
                                warehouse.availableQuantity - inCartQuantity,
                                0
                            );

                            return (
                                <UiListBox.Option
                                    className="relative"
                                    key={warehouse.warehouseId}
                                    value={warehouse.warehouseId}
                                    disabled={availableQuantity === 0}
                                >
                                    {({active, selected, disabled}) => (
                                        <button
                                            disabled={disabled}
                                            aria-disabled={disabled}
                                            className={cls(
                                                'flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal focus:outline-none',
                                                active && 'bg-gray-100',
                                                disabled &&
                                                    '!cursor-not-allowed opacity-40'
                                            )}
                                        >
                                            <span
                                                className={cls(
                                                    'inline-flex flex-1 items-center justify-between truncate',
                                                    selected
                                                        ? 'font-medium'
                                                        : 'font-normal'
                                                )}
                                            >
                                                <span>
                                                    {warehouse.warehouseName}
                                                </span>
                                                <span>
                                                    ({availableQuantity})
                                                </span>
                                            </span>
                                            {selected && (
                                                <span
                                                    className="absolute -left-1 h-6 rounded-full bg-primary-600"
                                                    style={{
                                                        width: 2
                                                    }}
                                                ></span>
                                            )}
                                        </button>
                                    )}
                                </UiListBox.Option>
                            );
                        })}
                    </UiListBox.Options>
                </>
            )}
        </UiListBox>
    );
});

if (isDev) {
    WarehouseSelection.displayName = 'WarehouseSelection';
}

export default WarehouseSelection;

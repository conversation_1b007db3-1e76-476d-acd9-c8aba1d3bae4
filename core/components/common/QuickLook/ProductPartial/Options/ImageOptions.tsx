import {FC, memo, useCallback, useMemo, useRef} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiImage} from '@core/components/ui';
import storeConfig from '~/store.config';
import {useQuickLook} from '../../context';

type ImageOptionsProps = {
    code: string;
    label: string;
    selections: {
        value: string;
        color?: string;
        image?: string;
        inStock?: boolean;
    }[];
};

const ImageOptions: FC<ImageOptionsProps> = memo(props => {
    const {code, label, selections} = props;
    const {selectedProduct, setAttribute} = useQuickLook();
    const value = useMemo(
        () => (selectedProduct.attributes ?? {})[code],
        [selectedProduct, code]
    );
    const currentValue = useRef(value);

    const onClick = useCallback(
        (value: string) => {
            setAttribute(code, value);
            currentValue.current = value;
        },
        [code, setAttribute]
    );
    const onMouseEnter = useCallback((value: string) => {
        // setAttribute(code, value);
    }, []);
    const onMouseLeave = useCallback(() => {
        // setAttribute(code, currentValue.current);
    }, []);

    const scrollToTop = useCallback(() => {
        const container = document.querySelector(
            '.mobile-product-content-wrapper'
        );
        if (container === null || container === undefined) return;

        container.scrollTo({top: 0, behavior: 'smooth'});
    }, []);

    return (
        <>
            <div className="flex items-center">
                <h3 className="text-sm font-medium text-default">{label}:</h3>
                <div className="ml-1.5 text-sm text-muted">{value}</div>
            </div>

            <div className="-m-1.5 mt-2.5">
                <div
                    onMouseLeave={onMouseLeave}
                    className="flex select-none flex-wrap items-center"
                >
                    {selections.map(selection => (
                        <button
                            key={selection.value}
                            onClick={() => {
                                onClick(selection.value);
                                scrollToTop();
                            }}
                            onMouseEnter={() => onMouseEnter(selection.value)}
                            className={cls(
                                'relative m-1.5 h-full w-full rounded',
                                {
                                    'ring-1 ring-primary-600':
                                        selection.value === value ||
                                        currentValue.current === selection.value
                                }
                            )}
                            style={{
                                width:
                                    storeConfig.catalog.productImageShape ===
                                    'rectangle'
                                        ? '48px'
                                        : '48px',
                                height:
                                    storeConfig.catalog.productImageShape ===
                                    'rectangle'
                                        ? '72px'
                                        : '48px'
                            }}
                        >
                            <UiImage
                                className="rounded"
                                src={
                                    `${selection.image}?w=180&q=50` ??
                                    '/no-image.png'
                                }
                                fill
                                fit="cover"
                                position="center"
                                alt=""
                            />
                        </button>
                    ))}
                </div>
            </div>
        </>
    );
});

if (isDev) {
    ImageOptions.displayName = 'ImageOptions';
}

export default ImageOptions;

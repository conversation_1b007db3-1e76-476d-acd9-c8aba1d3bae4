import {UiModal, UiSideBar} from '@core/components/ui';
import {useQuickLook} from '../context';
import {useMobile, useTrans} from '@core/hooks';
import Stats from './Informations/Stats';
import ImageGallery from './ImageGallery';
import SideBar from './Sidebar';
import Actions from './Actions';
import Options from './Options';
import Info from './Informations/Info';
import ProductInformation from './Informations/ProductInformation';
import WarehouseSelection from './WarehouseSelection';

const ProductPartial = () => {
    const {setIsModalActive, isModalActive, status, product, selectedProduct} =
        useQuickLook();
    const {isMobile} = useMobile();
    const t = useTrans();

    return (
        <>
            <UiSideBar
                size="normal"
                title={t('Product Detail')}
                isShown={isModalActive && status === 'resolved' && isMobile}
                onClose={() => setIsModalActive(false)}
            >
                <div className="p-4">
                    <ImageGallery />
                    <Info />
                    {(selectedProduct?.warehouseStocks ?? []).length > 0 && (
                        <WarehouseSelection />
                    )}
                    <Actions />
                    <Stats />
                    <SideBar />
                    <ProductInformation />
                </div>
            </UiSideBar>

            <UiModal
                title={t('Product Detail')}
                isClosable
                isLarge
                isShown={isModalActive && status === 'resolved' && !isMobile}
                onClose={() => setIsModalActive(false)}
            >
                <div className="border-t px-8 py-4">
                    <div className="flex">
                        <div className="flex-1">
                            <div className="grid grid-cols-12 gap-8">
                                <div className="col-span-6">
                                    <ImageGallery />
                                </div>
                                <div className="col-span-6">
                                    <Info />
                                    {(product?.variants ?? []).length > 0 && (
                                        <Options />
                                    )}
                                    {(selectedProduct?.warehouseStocks ?? [])
                                        .length > 0 && <WarehouseSelection />}
                                    <Actions />
                                    <Stats />
                                </div>
                            </div>
                        </div>
                        <div className="ml-8 w-60">
                            <SideBar />
                        </div>
                    </div>

                    <ProductInformation />
                </div>
            </UiModal>
        </>
    );
};

export default ProductPartial;

import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {TruckFastIcon} from '@core/icons/solid';
import {useQuickLook} from '../../context';

const ShipmentOptions: FC = memo(() => {
    const t = useTrans();
    const {locale} = useStore();
    const {product} = useQuickLook();

    const getEstimatedShippingDate = useMemo(() => {
        const currentDate = new Date();
        currentDate.setDate(
            currentDate.getDate() + (product?.estimatedDeliveryDuration ?? 2)
        );

        const formatter = new Intl.DateTimeFormat(locale, {
            day: 'numeric',
            month: 'long'
        });
        return formatter.format(currentDate);
    }, [locale, product?.estimatedDeliveryDuration]);

    return typeof product?.estimatedDeliveryDuration === 'number' ? (
        <div className="shadow-small rounded-md border">
            <p className="border-b p-4 text-xs font-semibold">
                {t('SHIPPING OPTIONS')}
            </p>

            <div className="space-y-2 px-2 py-3 text-xs">
                {product?.estimatedDeliveryDuration === 1 && (
                    <div className="flex items-center gap-3 rounded-md bg-green-50 p-2">
                        <TruckFastIcon className="h-6 w-6 text-green-600" />
                        <p className="w-full text-green-600">
                            {t('If you order now, we will ship it tomorrow!')}
                        </p>
                    </div>
                )}
                {product?.estimatedDeliveryDuration >= 2 && (
                    <div className="flex items-center gap-3 rounded-md p-2">
                        <TruckFastIcon className="h-6 w-6 text-primary-600" />
                        <p
                            dangerouslySetInnerHTML={{
                                __html: t(
                                    'We will ship it on {getEstimatedShippingDate}!',
                                    {getEstimatedShippingDate}
                                )
                            }}
                        />
                    </div>
                )}
            </div>
        </div>
    ) : null;
});

if (isDev) {
    ShipmentOptions.displayName = 'ShipmentOptions';
}

export default ShipmentOptions;

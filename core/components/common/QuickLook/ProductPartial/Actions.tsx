import {FC, memo, useCallback, useState} from 'react';
import {isDev, pushIntoGTMDataLayer} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {Ui<PERSON>utton, UiSpinner} from '@core/components/ui';
import Quantity from '@components/common/Quantity';
import {HeartIcon, BellIcon} from '@core/icons/regular';
import {
    BagIcon,
    HeartIcon as HeartSolidIcon,
    BellIcon as BellSolidIcon
} from '@core/icons/solid';
import {useQuickLook} from '../context';

const Actions: FC = memo(() => {
    const t = useTrans();
    const {
        currency,
        addToFavorites,
        removeFromFavorites,
        addToCollection,
        removeFromCollection
    } = useStore();

    const {
        selectedProduct,
        setQuantity,
        isAddToCartInProgress,
        addToCart,
        availableQuantity,
        inStock,
        customerProductParams,
        setCustomerProductParams
    } = useQuickLook();

    const [isFavoriteUpdateInProgress, setIsFavoriteUpdateInProgress] =
        useState(false);

    const onAddToFavorites = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        const result = await addToFavorites({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isFavorite: true
            });
        }
        setIsFavoriteUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isFavoriteUpdateInProgress,
        addToFavorites,
        selectedProduct
    ]);
    const onRemoveFromFavorites = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        const result = await removeFromFavorites({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isFavorite: false
            });
        }
        setIsFavoriteUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isFavoriteUpdateInProgress,
        removeFromFavorites,
        selectedProduct
    ]);

    const [
        isAlarmCollectionUpdateInProgress,
        setIsAlarmCollectionUpdateInProgress
    ] = useState(false);

    const onAddToAlarmCollection = useCallback(async () => {
        if (isAlarmCollectionUpdateInProgress) {
            return;
        }

        setIsAlarmCollectionUpdateInProgress(true);

        // ---------- Google Tag Manager ----------
        pushIntoGTMDataLayer({
            event: 'add_to_wishlist',
            data: {
                currency: currency.name === 'TL' ? 'TRY' : currency.name,
                value: selectedProduct.salesPrice - selectedProduct.discount,
                items: [
                    {
                        item_id: selectedProduct.code,
                        item_name: selectedProduct.definition,
                        discount: selectedProduct.discount,
                        item_brand: selectedProduct.brandName,
                        item_category: selectedProduct.categoryName,
                        price: selectedProduct.salesPrice
                    }
                ]
            }
        });
        // ----------------------------------------

        const result = await addToCollection(
            {
                id: 'is-alarm',
                isAlarm: true
            },
            {
                id: selectedProduct.productId,
                name: selectedProduct.name,
                image:
                    (selectedProduct.images ?? []).length > 0
                        ? selectedProduct.images![0]
                        : '/no-image.png',
                price: selectedProduct.salesPrice
            }
        );

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isInAlarmCollection: true
            });
        }
        setIsAlarmCollectionUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isAlarmCollectionUpdateInProgress,
        addToCollection,
        selectedProduct,
        currency.name
    ]);
    const onRemoveFromAlarmCollection = useCallback(async () => {
        if (isAlarmCollectionUpdateInProgress) {
            return;
        }

        setIsAlarmCollectionUpdateInProgress(true);

        const result = await removeFromCollection(
            {
                id: 'is-alarm',
                isAlarm: true
            },
            {
                id: selectedProduct.productId,
                name: selectedProduct.name,
                image:
                    (selectedProduct.images ?? []).length > 0
                        ? selectedProduct.images![0]
                        : '/no-image.png',
                price: selectedProduct.salesPrice
            }
        );

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isInAlarmCollection: false
            });
        }
        setIsAlarmCollectionUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isAlarmCollectionUpdateInProgress,
        removeFromCollection,
        selectedProduct
    ]);

    return (
        <div className="border-b pb-4">
            <div className="flex select-none items-center gap-4">
                <Quantity
                    quantity={selectedProduct.quantity}
                    availableQuantity={availableQuantity}
                    onChange={quantity => setQuantity(quantity)}
                    size="sm"
                />
                <UiButton
                    className="flex-1"
                    variant="solid"
                    color="primary"
                    size="md"
                    leftIcon={<BagIcon className="mr-3 h-5 w-5" />}
                    loading={isAddToCartInProgress}
                    disabled={!inStock}
                    onClick={addToCart}
                >
                    {t('ADD TO CART')}
                </UiButton>
            </div>

            <div className="flex justify-end gap-2 pt-4 text-sm font-medium text-muted">
                {customerProductParams.isFavorite ? (
                    !isFavoriteUpdateInProgress ? (
                        <button
                            className="quick-look-product-action-btn"
                            onClick={onRemoveFromFavorites}
                        >
                            <HeartSolidIcon className="h-4 w-4 text-primary-600" />
                        </button>
                    ) : (
                        <button className="quick-look-product-action-btn cursor-progress">
                            <div className="w-4">
                                <UiSpinner size="sm" />
                            </div>
                        </button>
                    )
                ) : !isFavoriteUpdateInProgress ? (
                    <button
                        className="quick-look-product-action-btn"
                        onClick={onAddToFavorites}
                    >
                        <HeartIcon className="h-4 w-4" />
                    </button>
                ) : (
                    <button className="quick-look-product-action-btn cursor-progress">
                        <div className="w-4">
                            <UiSpinner size="sm" />
                        </div>
                    </button>
                )}

                {customerProductParams.isInAlarmCollection ? (
                    !isAlarmCollectionUpdateInProgress ? (
                        <button
                            className="quick-look-product-action-btn"
                            onClick={onRemoveFromAlarmCollection}
                        >
                            <BellSolidIcon className="h-4 w-4 text-primary-600" />
                        </button>
                    ) : (
                        <button className="quick-look-product-action-btn cursor-progress">
                            <div className="w-4">
                                <UiSpinner size="sm" />
                            </div>
                        </button>
                    )
                ) : !isAlarmCollectionUpdateInProgress ? (
                    <button
                        className="quick-look-product-action-btn"
                        onClick={onAddToAlarmCollection}
                    >
                        <BellIcon className="h-4 w-4" />
                    </button>
                ) : (
                    <button className="quick-look-product-action-btn cursor-progress">
                        <div className="w-4">
                            <UiSpinner size="sm" />
                        </div>
                    </button>
                )}
            </div>
        </div>
    );
});

if (isDev) {
    Actions.displayName = 'Actions';
}

export default Actions;

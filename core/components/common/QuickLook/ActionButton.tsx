import {UiButton} from '@core/components/ui';
import {cls} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {EyeIcon} from '@core/icons/solid';
import {useQuickLook} from './context';

type ActionButtonProps = {
    variant?: 'icon' | 'button';
    icon?: JSX.Element;
};

const ActionButton = ({
    variant = 'icon',
    icon = <EyeIcon className="h-4 w-4" />
}: ActionButtonProps) => {
    const {status, setIsModalActive} = useQuickLook();
    const t = useTrans();

    const isLoading = status === 'pending';

    if (variant === 'icon') {
        return (
            <UiButton
                className={cls(
                    'shadow-small inline-flex h-8 w-8 items-center justify-center rounded-md border bg-white text-primary-600 transition hover:bg-secondary-100 focus:ring-0 focus-visible:ring-2'
                )}
                variant="ghost"
                size="xs"
                onClick={() => setIsModalActive(true)}
                loading={isLoading}
                disabled={isLoading}
            >
                <div>{icon}</div>
            </UiButton>
        );
    } else if (variant === 'button') {
        return (
            <div
                className="relative isolate z-10 xl:opacity-0 xl:transition xl:group-hover:opacity-100"
                onClick={() => setIsModalActive(true)}
            >
                <UiButton
                    className="inline-flex w-full gap-3 focus:!bg-primary-500 focus-visible:ring-2 focus-visible:ring-offset-2"
                    variant="solid"
                    color="primary"
                    size="xs"
                    leftIcon={
                        <div className="hidden sm:inline-block">{icon}</div>
                    }
                    loading={isLoading}
                    disabled={isLoading}
                >
                    {t('SEE OPTIONS')}
                </UiButton>
            </div>
        );
    } else {
        return null;
    }
};

export default ActionButton;

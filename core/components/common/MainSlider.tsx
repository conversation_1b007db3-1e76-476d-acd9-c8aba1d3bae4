import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {useStore} from '@core/hooks';
import {UiImage, UiLink, UiSlider} from '@core/components/ui';
import {Autoplay, EffectFade, Navigation} from '@core/components/ui/Slider';

const MainSlider: FC = memo(() => {
    const {navigation} = useStore();

    const slides = useMemo(() => {
        return navigation
            .filter(
                navigationItem =>
                    navigationItem.type === 'slide' &&
                    Array.isArray(navigationItem.images) &&
                    navigationItem.images.length > 0 &&
                    navigationItem.depth === 0
            )
            .map(navigationItem => ({
                title: navigationItem.name,
                src: (navigationItem.images as string[])[0],
                link: navigationItem.href
            }));
    }, [navigation]);

    return slides.length > 0 ? (
        <div className="home-main-slider container px-0 md:px-4 xl:px-4 2xl:px-0">
            <div className="aspect-h-3 aspect-w-8 w-full select-none px-8">
                <div>
                    <UiSlider
                        className="h-full"
                        loop
                        modules={[Autoplay, EffectFade, Navigation]}
                        autoplay={{
                            delay: 5000
                        }}
                        effect="fade"
                        fadeEffect={{crossFade: true}}
                        spaceBetween={20}
                        navigation={{
                            ...navigation,
                            nextEl: '.swiper-button-next',
                            prevEl: '.swiper-button-prev'
                        }}
                    >
                        {slides.map((slide, index) => (
                            <UiSlider.Slide key={index}>
                                <UiLink
                                    className="relative block h-full w-full"
                                    href={slide.link ?? '#'}
                                >
                                    <UiImage
                                        className="rounded-lg"
                                        src={`${slide.src}?w=1280&q=90`}
                                        alt={slide.title}
                                        fit="cover"
                                        position="center"
                                        priority={index === 0}
                                        fill
                                    />
                                </UiLink>
                            </UiSlider.Slide>
                        ))}
                    </UiSlider>

                    <button className="swiper-button-next"></button>
                    <button className="swiper-button-prev"></button>
                </div>
            </div>
        </div>
    ) : null;
});

if (isDev) {
    MainSlider.displayName = 'MainSlider';
}

export default MainSlider;

import {Fragment} from 'react';
import {Column} from '@tanstack/react-table';
import {Popover, PopoverOverlayProps} from '@headlessui/react';
import {UiTransition, UiCheckbox} from '@core/components/ui';
import {ChevronDownIcon} from '@core/icons/solid';
import {useTrans} from '@core/hooks';
import {cls} from '@core/helpers';

type ColumnToggleProps = PopoverOverlayProps<'div'> & {
    tableColumns: Column<any, unknown>[];
    className?: string;
};

const ColumnToggle = ({
    tableColumns,
    className,
    ...rest
}: ColumnToggleProps) => {
    const t = useTrans();

    return (
        <Popover className={cls('relative', className)} {...rest}>
            {({open}) => (
                <>
                    <Popover.Button
                        className={cls(
                            'button-primary group inline-flex items-center px-3.5 py-1.5 text-sm font-medium transition hover:bg-secondary-100',
                            {
                                'border-primary-600 ring-1 ring-primary-600':
                                    open
                            }
                        )}
                    >
                        <span className="text-gray-700">{t('Filter')}</span>
                        <ChevronDownIcon
                            className={cls(
                                'ml-2.5 h-3.5 w-3.5 stroke-gray-700 stroke-[20px] transition group-hover:text-opacity-80',
                                {'-rotate-180': open}
                            )}
                        />
                    </Popover.Button>
                    <UiTransition
                        show={open}
                        as={Fragment}
                        enter="transition ease-out duration-200"
                        enterFrom="opacity-0 translate-y-1"
                        enterTo="opacity-100 translate-y-0"
                        leave="transition ease-in duration-200"
                        leaveFrom="opacity-100 translate-y-0"
                        leaveTo="opacity-0 translate-y-1"
                    >
                        <Popover.Panel className="absolute right-0 top-8 z-20 mt-3 w-full min-w-[320px] max-w-md">
                            <div className="button-primary overflow-hidden">
                                <div className="scroller grid max-h-96 grid-cols-2 gap-4 overflow-auto p-4">
                                    {tableColumns
                                        .filter(
                                            column =>
                                                typeof column.accessorFn !==
                                                    'undefined' &&
                                                column.getCanHide()
                                        )
                                        .map(column => (
                                            <UiCheckbox
                                                key={column.id}
                                                defaultChecked={column.getIsVisible()}
                                                onChange={e =>
                                                    column.getToggleVisibilityHandler()(
                                                        e
                                                    )
                                                }
                                            >
                                                {
                                                    column.columnDef
                                                        .header as string
                                                }
                                            </UiCheckbox>
                                        ))}
                                </div>
                            </div>
                        </Popover.Panel>
                    </UiTransition>
                </>
            )}
        </Popover>
    );
};

export default ColumnToggle;

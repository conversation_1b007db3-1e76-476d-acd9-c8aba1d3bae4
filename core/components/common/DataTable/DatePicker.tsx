import {useState} from 'react';
import {useRouter} from 'next/router';
import {Column} from '@tanstack/react-table';
import {tr, enUS} from 'date-fns/locale';
import ReactDatePicker from 'react-datepicker';
import {ChevronLeftIcon, ChevronRightIcon, XIcon} from '@core/icons/solid';
import {useTrans} from '@core/hooks';
import {cls} from '@core/helpers';

type DatePickerProps = {
    filteredColumn: Column<any, unknown> | undefined;
    className?: string;
};

const DatePicker = ({filteredColumn, className}: DatePickerProps) => {
    const [startDate, setStartDate] = useState<Date | null>(null);
    const [endDate, setEndDate] = useState<Date | null>(null);

    const router = useRouter();

    const t = useTrans();

    return (
        <div className={cls('relative', className)}>
            <ReactDatePicker
                selected={startDate}
                closeOnScroll
                selectsRange
                locale={router.locale === 'tr' ? tr : enUS}
                onChange={dates => {
                    const [start, end] = dates;
                    setStartDate(start);
                    setEndDate(end);
                    if (start === null || end === null) return;
                    filteredColumn?.setFilterValue([start, end]);
                }}
                startDate={startDate}
                endDate={endDate}
                fixedHeight
                dateFormat="MMM d"
                placeholderText={t('Filter by date')}
                maxDate={new Date()}
                nextMonthButtonLabel=">"
                previousMonthButtonLabel="<"
                popperClassName="react-datepicker-left"
                renderCustomHeader={({
                    date,
                    decreaseMonth,
                    increaseMonth,
                    prevMonthButtonDisabled,
                    nextMonthButtonDisabled
                }) => (
                    <div className="flex items-center justify-between p-2">
                        <span className="text-gray-700">
                            {date.toLocaleDateString(router.locale, {
                                dateStyle: 'long'
                            })}
                        </span>

                        <div className="flex items-center gap-2">
                            <button
                                onClick={decreaseMonth}
                                disabled={prevMonthButtonDisabled}
                                type="button"
                                className="featured-button"
                            >
                                <ChevronLeftIcon className="h-4 w-4 text-gray-700" />
                            </button>

                            <button
                                onClick={increaseMonth}
                                disabled={nextMonthButtonDisabled}
                                type="button"
                                className={cls('featured-button', {
                                    'cursor-not-allowed opacity-50':
                                        nextMonthButtonDisabled
                                })}
                            >
                                <ChevronRightIcon className="h-4 w-4 text-gray-700" />
                            </button>
                        </div>
                    </div>
                )}
            />

            <span className="pointer-events-none absolute left-2 top-1/2 inline-flex -translate-y-1/2 cursor-pointer items-center justify-center">
                <svg
                    viewBox="0 0 15 16"
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 fill-gray-700"
                >
                    <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M4.84766 0.823242C5.11287 0.823242 5.36723 0.928599 5.55476 1.11614C5.7423 1.30367 5.84766 1.55803 5.84766 1.82324V2.82324H9.84766V1.82324C9.84766 1.55803 9.95301 1.30367 10.1405 1.11614C10.3281 0.928599 10.5824 0.823242 10.8477 0.823242C11.1129 0.823242 11.3672 0.928599 11.5548 1.11614C11.7423 1.30367 11.8477 1.55803 11.8477 1.82324V2.82324H12.8477C13.3781 2.82324 13.8868 3.03396 14.2619 3.40903C14.6369 3.7841 14.8477 4.29281 14.8477 4.82324V13.8232C14.8477 14.3537 14.6369 14.8624 14.2619 15.2375C13.8868 15.6125 13.3781 15.8232 12.8477 15.8232H2.84766C2.31722 15.8232 1.80852 15.6125 1.43344 15.2375C1.05837 14.8624 0.847656 14.3537 0.847656 13.8232V4.82324C0.847656 4.29281 1.05837 3.7841 1.43344 3.40903C1.80852 3.03396 2.31722 2.82324 2.84766 2.82324H3.84766V1.82324C3.84766 1.55803 3.95301 1.30367 4.14055 1.11614C4.32809 0.928599 4.58244 0.823242 4.84766 0.823242ZM2.84766 6.82324V13.8232H12.8477V6.82324H2.84766Z"
                    />
                </svg>
            </span>

            {(startDate !== null || endDate !== null) && (
                <button
                    onClick={() => {
                        setStartDate(null);
                        setEndDate(null);
                        filteredColumn?.setFilterValue([]);
                    }}
                    className="absolute right-3 top-1/2 inline-flex h-5 w-5 -translate-y-1/2 items-center justify-center rounded-full border bg-gray-200 p-1 transition hover:bg-secondary-400"
                >
                    <XIcon className="h-2.5 w-2.5 stroke-black stroke-[20px]" />
                </button>
            )}
        </div>
    );
};

export default DatePicker;

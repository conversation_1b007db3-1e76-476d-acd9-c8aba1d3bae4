import {ChevronLeftIcon, ChevronRightIcon} from '@core/icons/solid';
import {cls} from '@core/helpers';

type PaginationButtonsProps = {
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    pageCount?: number;
    pageIndex: number;
    nextPage: () => void;
    previousPage: () => void;
    className?: string;
};

const PaginationButtons = ({
    hasNextPage,
    hasPreviousPage,
    pageIndex,
    nextPage,
    previousPage,
    className
}: PaginationButtonsProps) => {
    return (
        <div className={cls('flex items-center', className)}>
            <button
                className={cls(
                    'featured-button',
                    !hasPreviousPage && 'cursor-not-allowed opacity-50'
                )}
                onClick={() => previousPage()}
                disabled={!hasPreviousPage}
            >
                <ChevronLeftIcon className="h-4 w-4" />
            </button>
            <span className="flex w-12 items-center justify-center text-sm">
                {pageIndex + 1}
            </span>
            <button
                className={cls(
                    'featured-button',
                    !hasNextPage && 'cursor-not-allowed opacity-50'
                )}
                onClick={() => nextPage()}
                disabled={!hasNextPage}
            >
                <ChevronRightIcon className="h-4 w-4" />
            </button>
        </div>
    );
};

export default PaginationButtons;

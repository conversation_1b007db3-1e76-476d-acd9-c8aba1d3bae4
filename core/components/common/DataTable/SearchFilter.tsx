import {useState} from 'react';
import {useTrans} from '@core/hooks';
import {cls} from '@core/helpers';
import {UiInput} from '@core/components/ui';
import {SearchIcon} from '@core/icons/solid';

type SearchFilterProps = {
    value: string | number;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

const SearchFilter = ({value, onChange}: SearchFilterProps) => {
    const [toggleInput, setToggleInput] = useState(false);

    const t = useTrans();

    return (
        <>
            <div className="hidden w-full max-w-md items-center md:flex">
                <input
                    onChange={onChange}
                    value={value}
                    className={cls(
                        'button-primary form-input mr-3 w-full origin-right py-1.5 text-sm text-gray-700 transition duration-300 focus:border-primary-600 focus:!ring-primary-600',
                        toggleInput
                            ? 'scale-x-100 opacity-100'
                            : 'scale-x-0 opacity-0'
                    )}
                    placeholder={t('Search...')}
                />

                <button
                    onClick={() => setToggleInput(prev => !prev)}
                    className="featured-button group"
                >
                    {toggleInput ? (
                        <svg
                            className="h-4 w-8 fill-gray-700 transition group-hover:fill-white"
                            viewBox="0 0 512 512"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path d="M437.5,386.6L306.9,256l130.6-130.6c14.1-14.1,14.1-36.8,0-50.9c-14.1-14.1-36.8-14.1-50.9,0L256,205.1L125.4,74.5  c-14.1-14.1-36.8-14.1-50.9,0c-14.1,14.1-14.1,36.8,0,50.9L205.1,256L74.5,386.6c-14.1,14.1-14.1,36.8,0,50.9  c14.1,14.1,36.8,14.1,50.9,0L256,306.9l130.6,130.6c14.1,14.1,36.8,14.1,50.9,0C451.5,423.4,451.5,400.6,437.5,386.6z" />
                        </svg>
                    ) : (
                        <svg
                            className="h-4 w-8 fill-gray-700 transition group-hover:fill-white"
                            viewBox="0 0 32 16"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                fillRule="evenodd"
                                clipRule="evenodd"
                                d="M6.5 12a5.475 5.475 0 0 0 3.118-.968l3.675 3.675a1 1 0 0 0 1.414-1.414l-3.675-3.675A5.5 5.5 0 1 0 6.5 12Zm0-2a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7Z"
                            ></path>
                            <path d="M21 7a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2h-6ZM22 12a1 1 0 0 1 1-1h2a1 1 0 1 1 0 2h-2a1 1 0 0 1-1-1ZM19 3a1 1 0 1 0 0 2h10a1 1 0 1 0 0-2H19Z"></path>
                        </svg>
                    )}
                </button>
            </div>

            <UiInput.Group size="sm" className="w-full md:hidden">
                <UiInput.LeftElement>
                    <SearchIcon className="h-4 w-4 text-gray-700" />
                </UiInput.LeftElement>

                <UiInput
                    placeholder={t('Search...')}
                    className="button-primary form-input py-1.5 pl-8 text-sm text-gray-700 focus:border-primary-600 focus:!ring-primary-600"
                    value={value}
                    onChange={onChange}
                />
            </UiInput.Group>
        </>
    );
};

export default SearchFilter;

import {UiTableCell, UiTableRow} from '@core/components/ui';

type SkeletonRowsProps = {
    rowSize: number;
};

const SkeletonRows = ({rowSize}: SkeletonRowsProps) => {
    return (
        <>
            {new Array(rowSize).fill(Boolean).map((_, index) => (
                <UiTableRow key={index}>
                    <UiTableCell colSpan={rowSize} className="bg-white !py-2">
                        <div className="skeleton-card h-7"></div>
                    </UiTableCell>
                </UiTableRow>
            ))}
        </>
    );
};

export default SkeletonRows;

import {UiTableCell, UiTableRow} from '@core/components/ui';
import {useTrans} from '@core/hooks';

type EmptyTableRowProps = {
    colSize: number;
    icon: JSX.Element;
    title: string;
    description?: string;
};

const EmptyTableRow = ({
    colSize,
    icon,
    title,
    description
}: EmptyTableRowProps) => {
    const t = useTrans();

    return (
        <UiTableRow>
            <UiTableCell colSpan={colSize} className="bg-white py-36">
                <div className="flex flex-col items-center justify-center text-center">
                    <div className="flex h-24 w-24 items-center justify-center rounded-lg border border-dashed border-gray-500 text-muted">
                        {icon}
                    </div>

                    <h2 className="pt-8 text-center text-2xl font-semibold">
                        {t(title)}
                    </h2>

                    {description && description.length > 0 && (
                        <p className="px-10 pt-2 text-center text-muted">
                            {t(description)}
                        </p>
                    )}
                </div>
            </UiTableCell>
        </UiTableRow>
    );
};

export default EmptyTableRow;

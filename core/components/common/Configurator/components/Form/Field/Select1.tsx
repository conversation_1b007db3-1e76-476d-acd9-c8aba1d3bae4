import {FC, Fragment, memo, useMemo} from 'react';
import {cls, isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiListBox, UiTransition} from '@core/components/ui';
import {ChevronDownIcon, ChevronUpIcon} from '@core/icons/solid';
import {Field} from '../../../types';
import {useConfigurator} from '../../../helpers';

type SelectProps = {
    field: Field;
};

const Select: FC<SelectProps> = memo(({field}) => {
    const t = useTrans();
    const {values, changeFieldValue} = useConfigurator();

    const options = useMemo(() => field.options ?? [], [field.options]);
    const value = useMemo(() => values[field.name], [field.name, values]);
    const label = useMemo(() => {
        if (value) {
            return (
                (options.find(option => option.value === value) ?? {}).label ??
                ''
            );
        }

        return '';
    }, [options, value]);

    return (
        <UiListBox
            value={value}
            onChange={selectedValue =>
                changeFieldValue(field.name, selectedValue)
            }
            disabled={field.isReadOnly}
            as="div"
            className="relative"
        >
            {({open}) => (
                <>
                    <UiListBox.Button
                        className={cls(
                            'relative inline-flex w-full min-w-0 appearance-none items-center focus:outline-none',
                            'h-12 cursor-base rounded-base px-4 py-0 pr-8 text-sm',
                            'border shadow-sm',
                            'bg-white text-gray-900',
                            'hover:border-gray-300',
                            'focus:border-primary-600 focus:ring-1 focus:ring-primary-600',
                            'transition duration-150 ease-in-out',
                            {
                                '!border-primary-600 !bg-white !ring-1 !ring-primary-600':
                                    open
                            }
                        )}
                    >
                        {!value && (
                            <span className="truncate text-sm text-muted">
                                {t('Choose an option.')}
                            </span>
                        )}
                        {!!value && (
                            <span className="truncate text-sm">{label}</span>
                        )}
                        <span className="pointer-events-none absolute right-4 ml-3 flex items-center">
                            {open ? (
                                <ChevronUpIcon
                                    className="h-4 w-4 text-muted"
                                    aria-hidden="true"
                                />
                            ) : (
                                <ChevronDownIcon
                                    className="h-4 w-4 text-muted"
                                    aria-hidden="true"
                                />
                            )}
                        </span>
                    </UiListBox.Button>

                    <UiTransition
                        show={open}
                        as={Fragment}
                        enter="transition duration-150 ease-in-out"
                        enterFrom="transform scale-95 opacity-0"
                        enterTo="transform scale-100 opacity-100"
                        leave="transition duration-150 ease-in-out"
                        leaveFrom="transform scale-100 opacity-100"
                        leaveTo="transform scale-95 opacity-0"
                    >
                        <UiListBox.Options className="scroller absolute left-0 z-40 mt-2 max-h-64 w-full origin-top-left overflow-auto rounded border bg-white p-1.5 shadow-sm outline-none">
                            {options.map(option => (
                                <UiListBox.Option
                                    className="relative"
                                    key={option.value}
                                    value={option.value}
                                >
                                    {({active, selected, disabled}) => (
                                        <button
                                            disabled={disabled}
                                            aria-disabled={disabled}
                                            className={cls(
                                                'flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal focus:outline-none',
                                                active && 'bg-gray-100'
                                            )}
                                        >
                                            <span
                                                className={cls(
                                                    'block flex-1 truncate',
                                                    selected
                                                        ? 'font-medium'
                                                        : 'font-normal'
                                                )}
                                            >
                                                {option.label}
                                            </span>
                                            {selected && (
                                                <span
                                                    className="absolute -left-1 h-6 rounded-full bg-primary-600"
                                                    style={{
                                                        width: 2
                                                    }}
                                                />
                                            )}
                                        </button>
                                    )}
                                </UiListBox.Option>
                            ))}
                        </UiListBox.Options>
                    </UiTransition>
                </>
            )}
        </UiListBox>
    );
});

if (isDev) {
    Select.displayName = 'Select';
}

export default Select;

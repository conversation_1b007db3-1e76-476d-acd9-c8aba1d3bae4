import {FC, memo, useMemo} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiImage, UiRadioGroup} from '@core/components/ui';
import {Field} from '../../../types';
import {useConfigurator} from '../../../helpers';
import storeConfig from '~/store.config';

type SelectImageProps = {
    field: Field;
};

const SelectImage: FC<SelectImageProps> = memo(({field}) => {
    const {values, changeFieldValue} = useConfigurator();
    const options = useMemo(() => field.options ?? [], [field.options]);
    const value = useMemo(() => values[field.name], [field.name, values]);

    return (
        <UiRadioGroup
            value={value}
            onChange={selectedValue =>
                changeFieldValue(field.name, selectedValue)
            }
            disabled={field.isReadOnly}
        >
            <div className="mt-2 grid select-none grid-cols-2 gap-2 xl:grid-cols-3 xl:gap-3">
                {options.map(option => (
                    <UiRadioGroup.Option
                        key={option.value}
                        value={option.value}
                        className={({checked, active}) =>
                            cls(
                                'relative flex cursor-pointer flex-col items-center rounded',
                                'border border-gray-300 bg-white p-2',
                                {
                                    'cursor-default': checked
                                },
                                'transition duration-150 ease-in-out',
                                {
                                    'cursor-default border-primary-600 ring-1 ring-primary-600':
                                        value === option.value
                                }
                            )
                        }
                    >
                        <UiImage
                            className="h-auto w-full rounded object-contain"
                            src={
                                Array.isArray(option.images) &&
                                option.images.length > 0
                                    ? option.images[0]
                                    : '/no-image.png'
                            }
                            alt=""
                            raw
                            width={256}
                            height={256}
                            priority
                        />

                        <div className="mt-1.5 w-full text-center text-sm font-medium">
                            {option.label}
                        </div>
                    </UiRadioGroup.Option>
                ))}
            </div>
        </UiRadioGroup>
    );
});

if (isDev) {
    SelectImage.displayName = 'SelectImage';
}

export default SelectImage;

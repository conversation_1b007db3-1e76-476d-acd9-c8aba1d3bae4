import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiImage} from '@core/components/ui';
import Price from '@components/common/Price';
import Quantity from '@components/common/Quantity';
import {useConfigurator} from '../helpers';

const Content: FC = memo(() => {
    const t = useTrans();
    const {locale} = useStore();
    const {
        payload: {
            images,
            covered,
            price,
            additionalPrice,
            quantity,
            deliveryDate
        },
        availableQuantity,
        changeQuantity
    } = useConfigurator();
    const totalPrice = useMemo(
        () => price + additionalPrice,
        [additionalPrice, price]
    );
    const quantityFormatted = useMemo(() => {
        const formatOptions = {
            style: 'decimal'
        };

        return Intl.NumberFormat(locale, formatOptions).format(quantity);
    }, [locale, quantity]);
    const deliveryDateFormatted = useMemo(() => {
        if (!!deliveryDate) {
            return new Intl.DateTimeFormat(locale, {
                month: 'long',
                day: 'numeric',
                year: 'numeric'
            }).format(deliveryDate as Date);
        }

        return '';
    }, [locale, deliveryDate]);

    return (
        <div className="flex flex-1 flex-col border-t p-6 xl:border-t-0">
            <div className="flex flex-1 items-center justify-center">
                {covered.length > 0 ? (
                    <div className="relative h-full min-h-[450px] w-full">
                        <UiImage
                            src={covered[0]}
                            alt=""
                            fill
                            fit="cover"
                            position="center"
                            priority
                        />
                    </div>
                ) : (
                    images.length > 0 && (
                        <div className="relative flex h-full w-full items-center justify-center">
                            <UiImage
                                className="h-full w-auto max-w-[360px] object-contain xl:max-w-[480px]"
                                src={images[0]}
                                alt=""
                                raw
                                width={1024}
                                height={1024}
                                priority
                            />
                        </div>
                    )
                )}
            </div>

            <div className="mt-8 flex items-center justify-center divide-x">
                <div className="hidden h-14 flex-col px-6 xl:flex">
                    <div className="text-sm text-muted">{t('Price')}</div>
                    <div className="mt-0.5 text-lg font-semibold">
                        {totalPrice > 0 ? <Price price={totalPrice} /> : '---'}
                    </div>
                </div>
                <div className="flex h-14 flex-col px-6">
                    <div className="text-sm text-muted">{t('Quantity')}</div>
                    {typeof availableQuantity === 'number' ? (
                        <Quantity
                            className="mt-0.5"
                            size="sm"
                            quantity={quantity}
                            availableQuantity={availableQuantity}
                            onChange={quantity => changeQuantity(quantity)}
                        />
                    ) : (
                        <div className="mt-0.5 text-lg font-semibold">
                            {quantityFormatted}
                        </div>
                    )}
                </div>
                <div className="flex h-14 flex-col px-6">
                    <div className="text-sm text-muted">
                        {t('Estimated Delivery Date')}
                    </div>
                    <div className="mt-0.5 text-lg font-semibold">
                        {deliveryDateFormatted}
                    </div>
                </div>
            </div>

            <div className="mt-8 flex flex-col items-center xl:hidden">
                <div className="text-sm text-muted">{t('Price')}</div>
                <div className="mt-0.5 text-lg font-semibold">
                    {totalPrice > 0 ? <Price price={totalPrice} /> : '---'}
                </div>
            </div>
        </div>
    );
});

if (isDev) {
    Content.displayName = 'Content';
}

export default Content;

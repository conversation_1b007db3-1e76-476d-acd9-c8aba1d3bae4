import {FC, memo, useCallback, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiButton} from '@core/components/ui';
import {useConfigurator} from '../helpers';

const MobileFooter: FC = memo(() => {
    const t = useTrans();
    const {steps, currentStep, AddToCartButton, changeStep} = useConfigurator();

    const isPrevDisabled = useMemo(() => {
        const stepIndex = steps.findIndex(step => step.id === currentStep?.id);

        return stepIndex === 0;
    }, [currentStep?.id, steps]);
    const isNextDisabled = useMemo(() => {
        const stepIndex = steps.findIndex(step => step.id === currentStep?.id);

        return steps.length - 1 === stepIndex;
    }, [currentStep?.id, steps]);

    const onPrev = useCallback(() => {
        if (isPrevDisabled) return;

        const stepIndex = steps.findIndex(step => step.id === currentStep?.id);

        // noinspection JSIgnoredPromiseFromCall
        changeStep(steps[stepIndex - 1]);
    }, [changeStep, currentStep?.id, isPrevDisabled, steps]);
    const onNext = useCallback(() => {
        if (isNextDisabled || !currentStep!.isCompleted) return;

        const stepIndex = steps.findIndex(step => step.id === currentStep?.id);

        // noinspection JSIgnoredPromiseFromCall
        changeStep(steps[stepIndex + 1]);
    }, [changeStep, currentStep, isNextDisabled, steps]);

    return (
        <div className="fixed bottom-0 left-0 z-[48] block h-mobile-tab-bar w-full select-none border-t bg-white shadow xl:hidden">
            <div className="flex h-full items-center justify-between px-4">
                <UiButton
                    variant="solid"
                    color="primary"
                    size="xs"
                    disabled={isPrevDisabled}
                    onClick={onPrev}
                >
                    {t('Previous')}
                </UiButton>

                {isNextDisabled ? (
                    AddToCartButton
                ) : (
                    <UiButton
                        variant="solid"
                        color="primary"
                        size="xs"
                        disabled={isNextDisabled || !currentStep!.isCompleted}
                        onClick={onNext}
                    >
                        {t('Next')}
                    </UiButton>
                )}
            </div>
        </div>
    );
});

if (isDev) {
    MobileFooter.displayName = 'MobileFooter';
}

export default MobileFooter;

import {FC, memo} from 'react';
import {cls, isDev} from '@core/helpers';
import {useConfigurator} from '../helpers';
import {UiSelect} from '@core/components/ui';

const Steps: FC = memo(() => {
    const {steps, currentStep, changeStep} = useConfigurator();

    return (
        <>
            <div className="hidden w-full items-center justify-center overflow-hidden xl:flex">
                {steps.map((step, index) => (
                    <div className="flex items-center" key={step.id}>
                        <button
                            className={cls(
                                'relative flex h-8 items-center justify-center rounded-full border px-6',
                                'min-w-0 select-none truncate text-sm font-medium leading-3',
                                {
                                    'border-primary-600 bg-primary-600 text-white':
                                        step.isActive,
                                    'border-primary-600 bg-primary-50 text-primary-600':
                                        !step.isActive
                                },
                                step.isSummary && {
                                    'cursor-pointer':
                                        step.canBeShown && !step.isActive,
                                    'cursor-default':
                                        !step.canBeShown || step.isActive
                                },
                                !step.isSummary && {
                                    'cursor-pointer':
                                        step.isCompleted && !step.isActive,
                                    'cursor-default':
                                        !step.isCompleted || step.isActive
                                }
                            )}
                            onClick={() => {
                                if (step.isActive) {
                                    return;
                                }
                                if (!step.isSummary && !step.isCompleted) {
                                    return;
                                }
                                if (step.isSummary && !step.canBeShown) {
                                    return;
                                }

                                // noinspection JSIgnoredPromiseFromCall
                                changeStep(step);
                            }}
                        >
                            {step.name}
                        </button>

                        {index < steps.length - 1 && (
                            <div className="mx-2 w-12 border border-primary-100" />
                        )}
                    </div>
                ))}
            </div>

            <div className="block w-full p-4 xl:hidden">
                <UiSelect
                    value={currentStep!.id}
                    size="xl"
                    onChange={e => {
                        const stepId = e.target.value;
                        const step = steps.find(step => step.id === stepId);

                        if (!step) return;

                        if (step.isActive) {
                            return;
                        }
                        if (!step.isSummary && !step.isCompleted) {
                            return;
                        }
                        if (step.isSummary && !step.canBeShown) {
                            return;
                        }

                        // noinspection JSIgnoredPromiseFromCall
                        changeStep(step);
                    }}
                >
                    {steps.map((step, index) => (
                        <option
                            key={step.id + index}
                            value={step.id}
                            disabled={
                                (step.isSummary && !step.canBeShown) ||
                                (!step.isSummary && !step.isCompleted)
                            }
                        >
                            {step.name}
                        </option>
                    ))}
                </UiSelect>
            </div>
        </>
    );
});

if (isDev) {
    Steps.displayName = 'Steps';
}

export default Steps;

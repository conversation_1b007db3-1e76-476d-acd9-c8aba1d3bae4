import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {useConfigurator} from '../helpers';

const Summary: FC = memo(() => {
    const {payload} = useConfigurator();
    const summary = useMemo(() => payload.summary, [payload.summary]);

    return (
        <div
            className="
            scroller max-h-[480px] w-full overflow-auto bg-gray-100 p-6 xl:max-h-[640px]
            xl:min-h-[480px] xl:w-[480px]
            "
        >
            <div className="text-xl font-semibold">{summary.name}</div>
            {summary.description && (
                <div className="mt-1">{summary.description}</div>
            )}

            <div className="mt-6 divide-y">
                {summary.items.map(item => (
                    <div key={item.code} className="flex py-2.5">
                        <div className="flex-1 font-semibold">{item.label}</div>
                        <div className="">{item.value}</div>
                    </div>
                ))}
            </div>
        </div>
    );
});

if (isDev) {
    Summary.displayName = 'Summary';
}

export default Summary;

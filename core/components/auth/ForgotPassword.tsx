import {FC, memo, useCallback, useRef, useState} from 'react';
import {useRouter} from 'next/router';
import {useForm} from 'react-hook-form';
import {isDev, regexp, jsonRequest} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiAlert, UiButton, UiForm, notification} from '@core/components/ui';
import {EnvelopeIcon} from '@core/icons/solid';

type ForgotPasswordProps = {
    setIsForgotPasswordShown: React.Dispatch<React.SetStateAction<boolean>>;
};

const ForgotPassword: FC<ForgotPasswordProps> = memo(
    ({setIsForgotPasswordShown}) => {
        const {
            register,
            formState: {errors},
            handleSubmit
        } = useForm();
        const router = useRouter();

        const t = useTrans();
        const {locale} = useStore();
        const [isLoading, setIsLoading] = useState(false);
        const [errorMessage, setErrorMessage] = useState('');
        const inProgressRef = useRef(false);

        const onSubmit = useCallback(
            async (data: Record<string, any>) => {
                if (inProgressRef.current) {
                    return;
                }

                inProgressRef.current = true;
                setIsLoading(true);
                setErrorMessage('');

                try {
                    await jsonRequest({
                        url: '/api/auth/send-reset-password-link',
                        method: 'POST',
                        data: {
                            email: data.email,
                            locale
                        }
                    });

                    notification({
                        title: t('Reset Your Password'),
                        description: t(
                            'A password reset link has been sent to your email address.'
                        ),
                        status: 'success'
                    });

                    setIsForgotPasswordShown(false);
                } catch (error: any) {
                    if (error.message === 'Not found') {
                        setErrorMessage(t('E-mail is not found.'));
                    } else {
                        setErrorMessage(error.message);
                    }
                }

                inProgressRef.current = false;
                setIsLoading(false);
            },
            // eslint-disable-next-line react-hooks/exhaustive-deps
            [router]
        );

        return (
            <UiForm onSubmit={handleSubmit(onSubmit)}>
                {!!errorMessage && (
                    <UiAlert className="mb-4" color="danger">
                        {t(errorMessage)}
                    </UiAlert>
                )}

                <UiForm.Field
                    label={t('Email address')}
                    leftElement={
                        <EnvelopeIcon className="h-5 w-5 text-muted" />
                    }
                    error={
                        errors.email && errors.email.type === 'required'
                            ? t('Email address is required')
                            : errors.email && errors.email.type === 'pattern'
                            ? t('Email address is invalid')
                            : undefined
                    }
                    {...register('email', {
                        required: true,
                        pattern: regexp.email
                    })}
                />

                <UiButton
                    className="mt-8 w-full"
                    type="submit"
                    variant="solid"
                    size="lg"
                    color="primary"
                    loading={isLoading}
                >
                    {t('Continue')}
                </UiButton>
            </UiForm>
        );
    }
);

if (isDev) {
    ForgotPassword.displayName = 'ForgotPassword';
}

export default ForgotPassword;

import {FC, memo, useCallback, useRef, useState} from 'react';
import {useRouter} from 'next/router';
import {useForm} from 'react-hook-form';
import {signIn} from 'next-auth/react';
import {isDev, regexp} from '@core/helpers';
import {useCart, useTrans} from '@core/hooks';
import {UiAlert, UiButton, UiForm} from '@core/components/ui';
import {EyeIcon, EnvelopeIcon, KeyIcon, EyeSlashIcon} from '@core/icons/solid';

const SignIn: FC = memo(() => {
    const {
        register,
        formState: {errors},
        handleSubmit,
        watch
    } = useForm();
    const {refreshCart} = useCart();
    const router = useRouter();
    const t = useTrans();
    const password: string = watch('password', '');
    const [isLoading, setIsLoading] = useState(false);
    const [isPasswordShown, setIsPasswordShown] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const inProgressRef = useRef(false);

    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgressRef.current) {
                return;
            }

            inProgressRef.current = true;
            setIsLoading(true);
            setErrorMessage('');

            try {
                const result: any = await signIn('credentials', {
                    redirect: false,
                    email: data.email,
                    password: data.password
                });

                if (!!result?.error) {
                    // noinspection ExceptionCaughtLocallyJS
                    throw new Error(result.error);
                }

                router.push('/');
                refreshCart();
                return;
            } catch (error: any) {
                setErrorMessage(error.message);
            }

            inProgressRef.current = false;
            setIsLoading(false);
        },
        [router]
    );

    return (
        <UiForm onSubmit={handleSubmit(onSubmit)}>
            {!!errorMessage && (
                <UiAlert className="mb-4" color="danger">
                    {t(errorMessage)}
                </UiAlert>
            )}

            <UiForm.Field
                label={t('Email address')}
                leftElement={<EnvelopeIcon className="h-5 w-5 text-muted" />}
                error={
                    errors.email && errors.email.type === 'required'
                        ? t('Email address is required')
                        : errors.email && errors.email.type === 'pattern'
                        ? t('Email address is invalid')
                        : undefined
                }
                {...register('email', {
                    required: true,
                    pattern: regexp.email
                })}
            />

            <UiForm.Field
                className="mt-4"
                label={t('Password')}
                leftElement={<KeyIcon className="h-5 w-5 text-muted" />}
                rightElement={
                    !!password &&
                    password.length > 0 && (
                        <div
                            className="cursor-pointer"
                            onClick={() => setIsPasswordShown(!isPasswordShown)}
                        >
                            {isPasswordShown && (
                                <EyeSlashIcon className="h-5 w-5 text-muted" />
                            )}
                            {!isPasswordShown && (
                                <EyeIcon className="h-5 w-5 text-muted" />
                            )}
                        </div>
                    )
                }
                type={isPasswordShown ? 'text' : 'password'}
                error={
                    errors.password && errors.password.type === 'required'
                        ? t('Password is required')
                        : undefined
                }
                {...register('password', {required: true})}
            />

            <UiButton
                className="mt-8 w-full"
                type="submit"
                variant="solid"
                size="lg"
                color="primary"
                loading={isLoading}
            >
                {t('Sign In')}
            </UiButton>
        </UiForm>
    );
});

if (isDev) {
    SignIn.displayName = 'SignIn';
}

export default SignIn;

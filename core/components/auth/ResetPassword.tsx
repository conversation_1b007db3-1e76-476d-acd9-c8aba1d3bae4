import {FC, memo, useCallback, useRef, useState} from 'react';
import {useRouter} from 'next/router';
import {useForm} from 'react-hook-form';
import {isDev, jsonRequest} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiAlert, UiButton, UiForm, notification} from '@core/components/ui';
import {EyeIcon, EyeSlashIcon} from '@core/icons/solid';

type ResetPasswordProps = {
    token: string;
};

const ResetPassword: FC<ResetPasswordProps> = memo(({token}) => {
    const {
        register,
        formState: {errors},
        handleSubmit,
        watch
    } = useForm();
    const router = useRouter();
    const t = useTrans();
    const {locale} = useStore();
    const [isLoading, setIsLoading] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const inProgressRef = useRef(false);
    const password: string = watch('password', '');
    const [isPasswordShown, setIsPasswordShown] = useState(false);
    const confirmation: string = watch('confirmation', '');
    const [isConfirmationShown, setIsConfirmationShown] = useState(false);

    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgressRef.current) {
                return;
            }

            inProgressRef.current = true;
            setIsLoading(true);
            setErrorMessage('');

            try {
                if (data.password !== data.confirmation) {
                    // noinspection ExceptionCaughtLocallyJS
                    throw new Error(
                        t(
                            'Password and password confirmation must be the same!'
                        )
                    );
                }

                const result = await jsonRequest({
                    url: '/api/auth/reset-password',
                    method: 'POST',
                    data: {
                        token,
                        password: data.password,
                        locale
                    }
                });

                if (!!result?.error) {
                    // noinspection ExceptionCaughtLocallyJS
                    throw new Error(result.error);
                }

                notification({
                    title: t('Password Reset'),
                    description: t(
                        'Your password has been successfully reset.'
                    ),
                    status: 'success'
                });

                await router.push('/');
                return;
            } catch (error: any) {
                setErrorMessage(error.message);
            }

            inProgressRef.current = false;
            setIsLoading(false);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [router]
    );

    return (
        <UiForm onSubmit={handleSubmit(onSubmit)}>
            {!!errorMessage && (
                <UiAlert className="mb-4" color="danger">
                    {t(errorMessage)}
                </UiAlert>
            )}

            <UiForm.Field
                label={t('Password')}
                rightElement={
                    !!password &&
                    password.length > 0 && (
                        <div
                            className="cursor-pointer"
                            onClick={() => setIsPasswordShown(!isPasswordShown)}
                        >
                            {isPasswordShown && (
                                <EyeSlashIcon className="h-5 w-5 text-muted" />
                            )}
                            {!isPasswordShown && (
                                <EyeIcon className="h-5 w-5 text-muted" />
                            )}
                        </div>
                    )
                }
                type={isPasswordShown ? 'text' : 'password'}
                error={
                    errors.password && errors.password.type === 'required'
                        ? t('Password is required')
                        : undefined
                }
                {...register('password', {required: true})}
            />

            <UiForm.Field
                className="mt-4"
                label={t('Password confirmation')}
                rightElement={
                    !!confirmation &&
                    confirmation.length > 0 && (
                        <div
                            className="cursor-pointer"
                            onClick={() =>
                                setIsConfirmationShown(!isConfirmationShown)
                            }
                        >
                            {isConfirmationShown && (
                                <EyeSlashIcon className="h-5 w-5 text-muted" />
                            )}
                            {!isConfirmationShown && (
                                <EyeIcon className="h-5 w-5 text-muted" />
                            )}
                        </div>
                    )
                }
                type={isConfirmationShown ? 'text' : 'password'}
                error={
                    errors.confirmation &&
                    errors.confirmation.type === 'required'
                        ? t('Password confirmation is required')
                        : undefined
                }
                {...register('confirmation', {required: true})}
            />

            <UiButton
                className="mt-8 w-full"
                type="submit"
                variant="solid"
                size="lg"
                color="primary"
                loading={isLoading}
            >
                {t('Reset Password')}
            </UiButton>
        </UiForm>
    );
});

if (isDev) {
    ResetPassword.displayName = 'ResetPassword';
}

export default ResetPassword;

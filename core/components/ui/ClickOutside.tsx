import {cloneElement, FC, useEffect, useRef} from 'react';
import {isDev} from '@core/helpers';

export type UiClickOutsideProps = {
    active: boolean;
    onClick: (e?: MouseEvent) => void;
    children: any;
};

function hasParent(element: HTMLElement, root?: HTMLElement) {
    return root && root.contains(element) && Boolean(element.closest('body'));
}

const UiClickOutside: FC<UiClickOutsideProps> = ({
    active = true,
    onClick,
    children
}) => {
    const innerRef = useRef();

    const handleClick = (event: any) => {
        if (!hasParent(event.target, innerRef?.current)) {
            if (typeof onClick === 'function') {
                onClick(event);
            }
        }
    };

    useEffect(() => {
        if (active) {
            document.addEventListener('mousedown', handleClick);
            document.addEventListener('touchstart', handleClick);
        }

        return () => {
            if (active) {
                document.removeEventListener('mousedown', handleClick);
                document.removeEventListener('touchstart', handleClick);
            }
        };
    });

    return cloneElement(children, {ref: innerRef});
};

if (isDev) {
    UiClickOutside.displayName = 'UiClickOutside';
}

export default UiClickOutside;

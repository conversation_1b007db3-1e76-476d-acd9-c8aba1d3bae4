import {
    ForwardedRef,
    forwardRef,
    memo,
    ReactNode,
    useCallback,
    useEffect,
    useId,
    useMemo,
    useRef,
    useState
} from 'react';
import {useFormContext, useWatch} from 'react-hook-form';
import dynamic from 'next/dynamic';
import type {NumberFormatValues} from 'react-number-format';
import {cls, isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {default as UiFormControl} from '../FormControl';
import {default as UiFormLabel} from '../FormLabel';
import {default as UiFormErrorMessage} from '../FormErrorMessage';
import {UiInput, UiInputProps} from '../..';
import storeConfig from '~/store.config';

const CountryFlag = dynamic(() => import('react-country-flag'));
const NumberFormat = dynamic(() => import('react-number-format'));

type Phone = {
    countryCode: string;
    code: string;
    number: string;
};

type UiPhoneFieldProps = Omit<UiInputProps, 'placeholder'> & {
    name: string;
    value?: Phone;
    label?: string;
    error?: string;
    disabled?: boolean;
    readOnly?: boolean;
    countries: Record<string, any>[];
    leftElement?: ReactNode;
    rightElement?: ReactNode;
    leftAddon?: ReactNode;
    rightAddon?: ReactNode;
};

const UiPhoneField = memo(
    forwardRef<HTMLElement, UiPhoneFieldProps>((props, ref) => {
        const {
            name,
            label,
            value: initialValue,
            error,
            // countries,
            leftElement,
            rightElement,
            leftAddon,
            rightAddon,
            className,
            onChange,
            onFocus,
            onBlur,
            children,
            ...rest
        } = props;
        const countries = useMemo(
            () => (Array.isArray(props.countries) ? props.countries : []),
            [props.countries]
        );
        const id = useId();
        const defaultCountry = storeConfig.defaultCountry;
        const {setValue: setFieldValue} = useFormContext();
        const formLabelRef = useRef<any>(null);
        const [value, setValue] = useState(initialValue);
        const t = useTrans();
        const [inputValue, setInputValue] = useState<string>(
            value?.number ?? ''
        );
        const [isInvalid, setIsInvalid] = useState(false);
        const [allowEmptyFormatting, setAllowEmptyFormatting] = useState(false);
        const [country, setCountry] = useState(() => {
            if (typeof value === 'object' && !!value.countryCode) {
                return countries.find(
                    country =>
                        (country || defaultCountry).code === value.countryCode
                ) as Record<string, any>;
            }

            return countries.find(country => !!country.isDefault) as Record<
                string,
                any
            >;
        });
        const isCountryFlagShown = useMemo(
            () => allowEmptyFormatting || inputValue.length > 0,
            [allowEmptyFormatting, inputValue]
        );
        const countrySelectRef = useRef<any>(null);

        const [isInitialized, setIsInitialized] = useState(false);
        useEffect(() => {
            const idx = setTimeout(() => {
                setIsInitialized(true);
            }, 500);

            return () => {
                clearTimeout(idx);
            };
        }, []);

        const phone = useWatch({
            name
        });

        useEffect(() => {
            setValue(phone);
            setInputValue(phone?.number ?? '');
            setCountry(() => {
                if (typeof phone === 'object' && !!phone.countryCode) {
                    return countries.find(
                        country =>
                            (country || defaultCountry).code ===
                            phone.countryCode
                    ) as Record<string, any>;
                }

                return countries.find(country => !!country.isDefault) as Record<
                    string,
                    any
                >;
            });
        }, [defaultCountry, phone, countries]);

        const onCountryChange = useCallback(
            (e: any) => {
                const newCountry = countries.find(
                    country =>
                        (country || defaultCountry).code === e.target.value
                ) as Record<string, any>;

                setCountry(newCountry);

                // @ts-ignore
                setValue({
                    countryCode: (newCountry || defaultCountry).code,
                    code: newCountry.phoneCode,
                    number: ''
                });
                setAllowEmptyFormatting(true);
                setInputValue('');

                formLabelRef.current.focus();
            },
            [defaultCountry, formLabelRef, countries]
        );
        const onValueChange = useCallback(
            (values: NumberFormatValues) => {
                const number = values.formattedValue.replace(
                    `${(country || defaultCountry).phoneCode} `,
                    ''
                );

                const realNumber = (number ?? '')
                    .replaceAll('_', '')
                    .replaceAll('-', '')
                    .replaceAll('(', '')
                    .replaceAll(')', '')
                    .replaceAll(' ', '')
                    .trim();

                if (!realNumber) {
                    setInputValue('');
                    setIsInvalid(true);

                    if (typeof onChange === 'function') {
                        setFieldValue(name, undefined);
                    }
                } else {
                    setInputValue(number);
                    setIsInvalid(false);

                    if (typeof onChange === 'function') {
                        setFieldValue(name, {
                            countryCode: (country || defaultCountry).code,
                            code: (country || defaultCountry).phoneCode,
                            number
                        });
                    }
                }
            },
            // eslint-disable-next-line react-hooks/exhaustive-deps
            [name, country, onChange]
        );
        const onInputFocus = useCallback(
            (e: any) => {
                setAllowEmptyFormatting(true);

                if (typeof onFocus === 'function') {
                    onFocus(e);
                }
            },
            [onFocus]
        );
        const onInputBlur = useCallback(
            (e: any) => {
                if (inputValue.length < 10 && inputValue.length > 0) {
                    setIsInvalid(true);
                } else {
                    setIsInvalid(false);
                }

                setAllowEmptyFormatting(false);

                if (typeof onBlur === 'function') {
                    onBlur(e);
                }
            },
            [inputValue, onBlur]
        );

        return (
            <UiFormControl
                className={className}
                invalid={
                    (typeof error === 'string' && error.length > 0) || isInvalid
                }
            >
                <UiInput.Group className="relative" size="xl">
                    {leftAddon && (
                        <UiInput.LeftAddon>{leftAddon}</UiInput.LeftAddon>
                    )}

                    <UiInput.LeftElement>
                        <div
                            className={cls(
                                'relative cursor-pointer select-none pt-4',
                                {
                                    'opacity-0': !isCountryFlagShown,
                                    'opacity-1': isCountryFlagShown,
                                    'transition-all': isInitialized
                                }
                            )}
                            onClick={() => countrySelectRef.current?.focus()}
                        >
                            {/* @ts-ignore */}
                            <CountryFlag
                                className="-mt-1"
                                countryCode={(country || defaultCountry).code}
                                svg
                                style={{
                                    width: '1rem',
                                    height: '1rem'
                                }}
                                title={(country || defaultCountry).name}
                            />

                            <div className="absolute top-4 z-base opacity-0">
                                <select
                                    ref={countrySelectRef}
                                    className="w-4 cursor-pointer p-0"
                                    tabIndex={-1}
                                    defaultValue={
                                        (country || defaultCountry).code
                                    }
                                    onChange={onCountryChange}
                                    onFocus={() =>
                                        setAllowEmptyFormatting(true)
                                    }
                                >
                                    {countries.map(country => (
                                        <option
                                            key={
                                                (country || defaultCountry).code
                                            }
                                            value={
                                                (country || defaultCountry).code
                                            }
                                        >
                                            {(country || defaultCountry).name}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>
                    </UiInput.LeftElement>

                    {/* @ts-ignore */}
                    <NumberFormat
                        {...rest}
                        id={id}
                        name={name}
                        value={inputValue}
                        className={cls(
                            'peer pl-10 pr-4 pt-4 text-sm font-medium leading-4',
                            'placeholder-transparent peer-placeholder-shown:pt-0',
                            {
                                'pr-12': !!rightElement
                            }
                        )}
                        customInput={UiInput as any}
                        getInputRef={(el: ForwardedRef<HTMLElement>) =>
                            (ref = el)
                        }
                        placeholder={label}
                        format={
                            !!(country || defaultCountry).phoneCode
                                ? `${
                                      (country || defaultCountry).phoneCode
                                  } (###) ###-##-##`
                                : '(###) ###-##-##'
                        }
                        mask="_"
                        allowEmptyFormatting={allowEmptyFormatting}
                        onFocus={onInputFocus}
                        onBlur={onInputBlur}
                        onValueChange={onValueChange}
                    />

                    <UiFormLabel
                        htmlFor={id}
                        ref={formLabelRef}
                        className={cls(
                            'absolute top-1.5 select-none text-xs text-muted',
                            'peer-placeholder-shown:text-base peer-placeholder-shown:text-muted',
                            'peer-placeholder-shown:top-3 peer-focus:top-1.5 peer-focus:text-muted',
                            'cursor-text peer-focus:text-xs',
                            {
                                'left-4': !leftElement,
                                'left-12': !!leftElement,
                                'transition-all': isInitialized
                            }
                        )}
                    >
                        {label}
                    </UiFormLabel>

                    {rightElement && (
                        <UiInput.RightElement>
                            {rightElement}
                        </UiInput.RightElement>
                    )}
                    {rightAddon && (
                        <UiInput.RightAddon>{rightAddon}</UiInput.RightAddon>
                    )}
                </UiInput.Group>

                {typeof error === 'string' && error.length > 0 && (
                    <UiFormErrorMessage>{error}</UiFormErrorMessage>
                )}
                {!error && isInvalid && (
                    <UiFormErrorMessage>
                        {t('Phone number is invalid')}
                    </UiFormErrorMessage>
                )}
            </UiFormControl>
        );
    })
);

if (isDev) {
    UiPhoneField.displayName = 'UiPhoneField';
}

export default UiPhoneField;

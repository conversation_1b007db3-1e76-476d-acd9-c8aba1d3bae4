import {forwardRef, ReactNode} from 'react';
import {isDev} from '@core/helpers';
import {default as UiFormControl} from '../FormControl';
import {default as UiFormErrorMessage} from '../FormErrorMessage';
import {UiCheckbox, UiCheckboxProps} from '../..';

type UiCheckboxFieldProps = Omit<UiCheckboxProps, 'placeholder'> & {
    name: string;
    label?: string;
    error?: string;
    disabled?: boolean;
    readOnly?: boolean;
    leftElement?: ReactNode;
    rightElement?: ReactNode;
    leftAddon?: ReactNode;
    rightAddon?: ReactNode;
};

const UiCheckboxField = forwardRef<HTMLInputElement, UiCheckboxFieldProps>(
    (props, ref) => {
        const {
            name,
            label,
            error,
            leftElement,
            rightElement,
            leftAddon,
            rightAddon,
            className,
            children,
            ...rest
        } = props;

        return (
            <UiFormControl
                className={className}
                invalid={typeof error === 'string' && error.length > 0}
            >
                <UiCheckbox {...rest} name={name} ref={ref}>
                    {label}
                </UiCheckbox>

                {typeof error === 'string' && error.length > 0 && (
                    <UiFormErrorMessage>{error}</UiFormErrorMessage>
                )}
            </UiFormControl>
        );
    }
);

if (isDev) {
    UiCheckboxField.displayName = 'UiCheckboxField';
}

export default UiCheckboxField;

import {
    ChangeEvent,
    forwardRef,
    ReactNode,
    useEffect,
    useId,
    useRef,
    useState
} from 'react';
import {mergeRefs} from 'react-merge-refs';
import {cls, isDev} from '@core/helpers';
import {default as UiFormControl} from '../FormControl';
import {default as UiFormLabel} from '../FormLabel';
import {default as UiFormErrorMessage} from '../FormErrorMessage';
import {UiInput, UiInputProps} from '../..';

type UiSelectFieldProps = Omit<UiInputProps, 'placeholder'> & {
    name: string;
    label?: string;
    error?: string;
    disabled?: boolean;
    readOnly?: boolean;
    leftElement?: ReactNode;
    rightElement?: ReactNode;
    leftAddon?: ReactNode;
    rightAddon?: ReactNode;
    hiddenValue?: boolean;
};

const UiSelectField = forwardRef<HTMLSelectElement, UiSelectFieldProps>(
    (props, ref) => {
        const {
            name,
            hiddenValue = true,
            label,
            error,
            leftElement,
            rightElement,
            leftAddon,
            rightAddon,
            className,
            children,
            ...rest
        } = props;
        const id = useId();
        const selectRef = useRef<HTMLSelectElement>(null);
        const initializedTimoutIdx = useRef<any>();
        const selectedTimoutIdx = useRef<any>();
        const [isSelected, setIsSelected] = useState(!!props.defaultValue);

        const [isInitialized, setIsInitialized] = useState(false);
        useEffect(() => {
            initializedTimoutIdx.current = setTimeout(() => {
                setIsInitialized(true);
            }, 500);

            return () => {
                try {
                    clearTimeout(initializedTimoutIdx.current);
                } catch (error: any) {}
            };
        }, []);

        useEffect(() => {
            let onSelect: any = null;

            selectedTimoutIdx.current = setTimeout(() => {
                setIsSelected(!!selectRef.current?.value);

                onSelect = (e: ChangeEvent<HTMLSelectElement>) => {
                    setIsSelected(!!e.target.value.toString());
                };

                if (!!(selectRef.current as HTMLSelectElement)) {
                    (selectRef.current as HTMLSelectElement).addEventListener(
                        'change',
                        onSelect
                    );
                }
            }, 50);

            return () => {
                if (!!(selectRef.current as HTMLSelectElement) && onSelect) {
                    // @ts-ignore
                    // eslint-disable-next-line react-hooks/exhaustive-deps
                    selectRef.current.removeEventListener('change', onSelect);
                }

                try {
                    clearTimeout(selectedTimoutIdx.current);
                } catch (error: any) {}
            };
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, []);

        useEffect(() => {
            if (!!(selectRef.current as HTMLSelectElement)) {
                setIsSelected(!!(selectRef.current as HTMLSelectElement).value);
            }
        }, [children]);

        return (
            <UiFormControl
                className={className}
                invalid={typeof error === 'string' && error.length > 0}
            >
                <UiInput.Group className="relative" size="xl">
                    {leftAddon && (
                        <UiInput.LeftAddon>{leftAddon}</UiInput.LeftAddon>
                    )}
                    {leftElement && (
                        <UiInput.LeftElement>{leftElement}</UiInput.LeftElement>
                    )}

                    <UiInput
                        {...rest}
                        id={id}
                        name={name}
                        as="select"
                        className={cls('peer form-select px-4', {
                            'text-muted': !isSelected,
                            'pt-4 text-sm font-medium leading-4': isSelected,
                            'pl-12': !!leftElement,
                            'pr-12': !!rightElement,
                            'transition-all': isInitialized
                        })}
                        ref={mergeRefs([selectRef, ref])}
                    >
                        {label && hiddenValue && (
                            <option key={`__hidden`} value="" hidden>
                                {label}
                            </option>
                        )}
                        {children}
                    </UiInput>

                    <UiFormLabel
                        htmlFor={id}
                        className={cls(
                            'absolute top-3 cursor-text select-none',
                            {
                                'text-md hidden leading-6 text-muted opacity-0':
                                    !isSelected,
                                'opacity-1 !top-1.5 text-xs leading-4 text-muted':
                                    isSelected,
                                'left-4': !leftElement,
                                'left-12': !!leftElement,
                                'transition-all': isInitialized
                            }
                        )}
                    >
                        {label}
                    </UiFormLabel>

                    {rightElement && (
                        <UiInput.RightElement>
                            {rightElement}
                        </UiInput.RightElement>
                    )}
                    {rightAddon && (
                        <UiInput.RightAddon>{rightAddon}</UiInput.RightAddon>
                    )}
                </UiInput.Group>

                {typeof error === 'string' && error.length > 0 && (
                    <UiFormErrorMessage>{error}</UiFormErrorMessage>
                )}
            </UiFormControl>
        );
    }
);

if (isDev) {
    UiSelectField.displayName = 'UiSelectField';
}

export default UiSelectField;

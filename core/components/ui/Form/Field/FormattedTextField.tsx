import {forwardRef, useId, ReactNode, ForwardedRef} from 'react';
import dynamic from 'next/dynamic';
import {cls, isDev} from '@core/helpers';
import {default as UiFormControl} from '../FormControl';
import {default as UiFormLabel} from '../FormLabel';
import {default as UiFormErrorMessage} from '../FormErrorMessage';
import {UiInput, UiInputProps} from '../..';

const NumberFormat = dynamic(() => import('react-number-format'));

type UiFormattedTextFieldProps = Omit<UiInputProps, 'placeholder'> & {
    name: string;
    label?: string;
    error?: string;
    disabled?: boolean;
    readOnly?: boolean;
    format: string;
    mask?: any;
    leftElement?: ReactNode;
    rightElement?: ReactNode;
    leftAddon?: ReactNode;
    rightAddon?: ReactNode;
};

const UiFormattedTextField = forwardRef<
    HTMLInputElement,
    UiFormattedTextFieldProps
>((props, ref) => {
    const {
        name,
        label,
        error,
        format,
        mask,
        leftElement,
        rightElement,
        leftAddon,
        rightAddon,
        className,
        children,
        ...rest
    } = props;
    const id = useId();

    return (
        <UiFormControl
            className={className}
            invalid={typeof error === 'string' && error.length > 0}
        >
            <UiInput.Group className="relative" size="xl">
                {leftAddon && (
                    <UiInput.LeftAddon>{leftAddon}</UiInput.LeftAddon>
                )}
                {leftElement && (
                    <UiInput.LeftElement>{leftElement}</UiInput.LeftElement>
                )}

                {/* @ts-ignore */}
                <NumberFormat
                    {...rest}
                    id={id}
                    name={name}
                    className={cls(
                        'peer px-4 pt-4 text-sm font-medium leading-4',
                        'placeholder-transparent peer-placeholder-shown:pt-0',
                        {
                            'pl-12': !!leftElement,
                            'pr-12': !!rightElement
                        }
                    )}
                    placeholder={label}
                    customInput={UiInput as any}
                    // @ts-ignore
                    getInputRef={(el: ForwardedRef<HTMLElement>) => (ref = el)}
                    format={format}
                    mask={mask}
                />

                <UiFormLabel
                    htmlFor={id}
                    className={cls(
                        'absolute top-1.5 select-none text-xs text-muted transition-all',
                        'peer-placeholder-shown:text-base peer-placeholder-shown:text-muted',
                        'peer-placeholder-shown:top-3 peer-focus:top-1.5 peer-focus:text-muted',
                        'cursor-text peer-focus:text-xs',
                        {
                            'left-4': !leftElement,
                            'left-12': !!leftElement
                        }
                    )}
                >
                    {label}
                </UiFormLabel>

                {rightElement && (
                    <UiInput.RightElement>{rightElement}</UiInput.RightElement>
                )}
                {rightAddon && (
                    <UiInput.RightAddon>{rightAddon}</UiInput.RightAddon>
                )}
            </UiInput.Group>

            {typeof error === 'string' && error.length > 0 && (
                <UiFormErrorMessage>{error}</UiFormErrorMessage>
            )}
        </UiFormControl>
    );
});

if (isDev) {
    UiFormattedTextField.displayName = 'UiFormattedTextField';
}

export default UiFormattedTextField;

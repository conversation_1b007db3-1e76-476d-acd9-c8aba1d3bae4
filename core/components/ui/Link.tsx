import {CSSProperties, forwardRef} from 'react';
import NextLink, {LinkProps as NextLinkProps} from 'next/link';
import {isDev} from '@core/helpers';

export type UiLinkProps = NextLinkProps & {
    className?: string;
    style?: CSSProperties;
    children: any;
};

const UiLink = forwardRef<HTMLAnchorElement, UiLinkProps>(
    ({href, className, style, children, prefetch = false, ...props}, ref) => {
        return (
            <NextLink
                ref={ref}
                className={className}
                style={style}
                {...props}
                href={href}
                prefetch={prefetch}
            >
                {children}
            </NextLink>
        );
    }
);

if (isDev) {
    UiLink.displayName = 'UiLink';
}

export default UiLink;

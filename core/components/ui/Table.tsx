import React from 'react';
import {cls} from '@core/helpers';

const Table = React.forwardRef<
    HTMLTableElement,
    React.HTMLAttributes<HTMLTableElement>
>(({className, ...props}, ref) => (
    <div className="w-full overflow-auto">
        <table
            ref={ref}
            className={cls('w-full caption-bottom text-sm', className)}
            {...props}
        />
    </div>
));
Table.displayName = 'Table';

const TableHeader = ({
    className,
    ...props
}: React.HTMLAttributes<HTMLTableSectionElement>) => (
    <thead
        className={cls('font-medium [&_tr]:border-b', className)}
        {...props}
    />
);

const TableBody = ({
    className,
    ...props
}: React.HTMLAttributes<HTMLTableSectionElement>) => (
    <tbody
        className={cls('[&_tr:last-child]:border-0', className)}
        {...props}
    />
);

const TableFooter = ({
    className,
    ...props
}: React.HTMLAttributes<HTMLTableSectionElement>) => (
    <tfoot className={className} {...props} />
);

const TableRow = ({
    className,
    ...props
}: React.HTMLAttributes<HTMLTableRowElement>) => (
    <tr className={cls('border-b', className)} {...props} />
);

const TableHead = ({
    className,
    ...props
}: React.ThHTMLAttributes<HTMLTableCellElement>) => (
    <th
        className={cls(
            'h-12 px-4 text-left align-middle font-medium',
            className
        )}
        {...props}
    />
);

const TableCell = ({
    className,
    ...props
}: React.TdHTMLAttributes<HTMLTableCellElement>) => (
    <td className={cls('p-4 align-middle', className)} {...props} />
);

const TableCaption = ({
    className,
    ...props
}: React.HTMLAttributes<HTMLTableCaptionElement>) => (
    <caption className={cls('mt-4 text-sm', className)} {...props} />
);

export {
    Table,
    TableHeader,
    TableBody,
    TableFooter,
    TableHead,
    TableRow,
    TableCell,
    TableCaption
};

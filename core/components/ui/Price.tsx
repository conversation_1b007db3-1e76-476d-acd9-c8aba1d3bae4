import {ComponentType, FC, HTMLAttributes} from 'react';
import {cls, isDev} from '@core/helpers';

function format(
    price: number,
    options: {
        locale: string;
        decimals: number;
        name: string;
        symbol: string;
        template: string;
    }
) {
    const {locale, decimals, name, symbol, template} = options;
    const formatOptions = {
        style: 'decimal',
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    };
    let formatted = Intl.NumberFormat(locale, formatOptions).format(price);

    return template
        .replace('{{price}}', formatted)
        .replace('{{name}}', name)
        .replace('{{symbol}}', symbol);
}

export type UiPriceProps = {
    locale: string;
    currency: {
        name: string;
        symbol: string;
        template: string;
    };
    price: number;
    discountedPrice?: number | null;
    className?: string;
    dontWrapDiscountedPrice?: boolean;
    as?: string | HTMLElement;
};

const UiPrice: FC<UiPriceProps> = props => {
    const {
        locale,
        currency,
        price,
        discountedPrice,
        className,
        dontWrapDiscountedPrice = false,
        as = 'span'
    } = props;
    const rootClassName = cls('product-price', className);
    const Component: ComponentType<HTMLAttributes<HTMLDivElement>> = as as any;
    const formattedPrice = format(price, {
        locale,
        decimals: 2,
        name: currency.name,
        symbol: currency.symbol,
        template: currency.template
    });
    const formattedDiscountedPrice =
        typeof discountedPrice === 'number'
            ? format(discountedPrice, {
                  locale,
                  decimals: 2,
                  name: currency.name,
                  symbol: currency.symbol,
                  template: currency.template
              })
            : null;

    return (
        // @ts-ignore
        <Component className={rootClassName}>
            {formattedDiscountedPrice !== null ? (
                <span
                    className={cls('inline-flex', {
                        'flex-col space-x-0 xl:flex-row xl:items-center xl:space-x-2.5':
                            !dontWrapDiscountedPrice,
                        'flex-row items-center space-x-2.5':
                            dontWrapDiscountedPrice
                    })}
                >
                    <span className="un-discounted-price overflow-hidden truncate text-sm text-muted line-through">
                        {formattedPrice}
                    </span>
                    <span className="discounted-price overflow-hidden truncate">
                        {formattedDiscountedPrice}
                    </span>
                </span>
            ) : (
                <span className="whitespace-nowrap">{formattedPrice}</span>
            )}
        </Component>
    );
};

if (isDev) {
    UiPrice.displayName = 'UiPrice';
}

export default UiPrice;

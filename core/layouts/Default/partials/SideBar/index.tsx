import {FC, memo, useMemo} from 'react';
import {cls, isDev} from '@core/helpers';
import {
    BagIcon,
    StoreIcon,
    InvoicesIcon,
    OrdersIcon,
    TagsIcon,
    ReportsIcon,
    InboxIcon,
    PaymentIcon,
    BarsIcon,
    BoxOpenIcon
} from '@core/icons/solid';
import storeConfig from '~/store.config';
import SideBarLink from './SideBarLink';
import {useCustomer, useLayout, useStore, useTrans} from '@core/hooks';
import {UiAvatar, UiLink} from '@core/components/ui';
import {useRouter} from 'next/router';

const SideBarPartial: FC = memo(() => {
    const {navigation, paymentOnly} = useStore();
    const customer = useCustomer();
    const router = useRouter();
    const t = useTrans();
    const {isSideMenuOpen, setIsSideMenuOpen, isSideMenuBlocked} = useLayout();

    const firstProductCatalogLink = useMemo(() => {
        return (
            navigation?.find(item => item.type === 'product-catalog')?.href ??
            ''
        );
        // eslint-disable-next-line
    }, []);

    return (
        <aside
            className={cls(
                'fixed left-0 hidden border-r py-3 transition-[width] duration-200 xl:flex xl:flex-col xl:justify-between',
                isSideMenuOpen ? 'w-64' : 'w-20'
            )}
            style={{
                height: `calc(100vh - ${storeConfig.theme.headerHeight})`,
                top: storeConfig.theme.headerHeight
            }}
        >
            <div className="grid gap-3">
                <div className="grid gap-2">
                    <p
                        className={cls(
                            'h-5 origin-left whitespace-nowrap pl-8 text-sm font-medium text-muted transition-transform duration-200',
                            isSideMenuOpen
                                ? 'w-full scale-x-100'
                                : 'w-0 scale-x-0'
                        )}
                    >
                        {t('Main Menu')}
                    </p>
                    <ul className="grid gap-1">
                        <SideBarLink
                            href="/home"
                            icon={<StoreIcon className="h-4 w-4" />}
                            title={t('Home') as any}
                        />
                        {!paymentOnly && (
                            <>
                                <SideBarLink
                                    href={firstProductCatalogLink}
                                    icon={<TagsIcon className="h-4 w-4" />}
                                    title={t('Products') as any}
                                />
                                <SideBarLink
                                    href={
                                        isDev
                                            ? '/cart'
                                            : `/cart?t=${Date.now()}`
                                    }
                                    icon={<BagIcon className="h-4 w-4" />}
                                    title={t('My Cart') as any}
                                />
                                <SideBarLink
                                    href="/quick-order"
                                    icon={<InboxIcon className="h-4 w-4" />}
                                    title={t('Quick Order') as any}
                                />
                            </>
                        )}

                        <SideBarLink
                            href="/payment"
                            icon={<PaymentIcon className="h-4 w-4" />}
                            title={t('Payment') as any}
                        />
                        {!paymentOnly && (
                            <SideBarLink
                                href="/previous-orders"
                                icon={<BoxOpenIcon className="h-4 w-4" />}
                                title={t('Previous Orders') as any}
                            />
                        )}
                    </ul>
                </div>

                <div className="grid gap-2">
                    <p className="inline-flex h-5 origin-left items-center whitespace-nowrap pl-8 text-sm font-medium text-muted transition-transform duration-200">
                        {isSideMenuOpen ? (
                            t('Reports')
                        ) : (
                            <span className="-ml-4 inline-block h-[1px] w-12 bg-secondary-400"></span>
                        )}
                    </p>
                    <ul className="grid gap-1">
                        <SideBarLink
                            href="/my-orders"
                            icon={<OrdersIcon className="h-4 w-4" />}
                            title={t('My Orders') as any}
                        />
                        <SideBarLink
                            href="/my-invoices"
                            icon={<InvoicesIcon className="h-4 w-4" />}
                            title={t('My Invoices') as any}
                        />
                        <SideBarLink
                            href="/my-ledger"
                            icon={<ReportsIcon className="h-4 w-4" />}
                            title={t('My Ledger') as any}
                        />
                    </ul>
                </div>
            </div>

            <div
                className={cls(
                    isSideMenuOpen ? 'flex' : 'grid place-content-center'
                )}
            >
                {isSideMenuOpen && (
                    <UiLink
                        href="/account"
                        className={cls(
                            'group relative mx-3 flex flex-1 origin-left cursor-pointer items-center rounded-lg p-2.5 outline-none transition duration-200 hover:bg-secondary-100 focus:outline-none active:outline-none',
                            {
                                'bg-secondary-100':
                                    router.asPath.startsWith('/account')
                            }
                        )}
                    >
                        <UiAvatar
                            className="cursor-pointer bg-gray-700 text-white"
                            name={customer?.name}
                            size="sm"
                        />
                        <div className="ml-2">{t('My Account')}</div>
                    </UiLink>
                )}

                <button
                    onClick={() => setIsSideMenuOpen(prev => !prev)}
                    className={cls(
                        'flex w-fit justify-center rounded-md p-3 transition hover:bg-secondary-100',
                        isSideMenuOpen && 'mr-3'
                    )}
                >
                    <BarsIcon className="h-6 w-6 text-gray-700" />
                </button>
            </div>
        </aside>
    );
});

if (isDev) {
    SideBarPartial.displayName = 'MainSideBarPartial';
}

export default SideBarPartial;

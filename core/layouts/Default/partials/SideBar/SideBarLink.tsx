import {UiLink} from '@core/components/ui';
import {cls} from '@core/helpers';
import {useCart, useLayout, useTrans} from '@core/hooks';
import {useRouter} from 'next/router';
import {FC} from 'react';

interface SideBarLinkProps {
    href: string;
    title: string;
    icon: JSX.Element;
}

const SideBarLink: FC<SideBarLinkProps> = ({href, title, icon}) => {
    const router = useRouter();
    const t = useTrans();
    const {isSideMenuOpen} = useLayout();
    const {itemCount} = useCart();
    const isActiveTab =
        (router.pathname === '/[[...slug]]' && title === t('Products')) ||
        ((router.asPath.startsWith('/cart') ||
            router.asPath.startsWith('/checkout')) &&
            title === t('My Cart')) ||
        router.asPath.startsWith(href);

    return (
        <li className="group relative">
            {isActiveTab && (
                <div className="absolute bottom-0 left-0 top-0 h-full w-[5px] rounded-r-lg bg-primary-600"></div>
            )}
            <UiLink
                className={cls(
                    'ml-3 mr-2 flex items-center justify-between rounded-md py-0.5 pl-2.5 transition-[background-color,width] duration-200 hover:bg-secondary-100',
                    isActiveTab
                        ? 'bg-secondary-200 text-primary-600'
                        : 'text-gray-700'
                )}
                href={href}
            >
                <div
                    className={cls('ml-2.5 py-2', {
                        'bg-transparent [&>div>svg]:fill-primary-600':
                            isActiveTab
                    })}
                >
                    {icon}
                </div>
                <p
                    className={cls(
                        'flex-1 origin-left whitespace-nowrap text-left text-sm transition-[margin,transform,opacity] duration-200',
                        isSideMenuOpen
                            ? 'ml-3 scale-x-100 opacity-100'
                            : 'ml-0 w-0 scale-x-0 opacity-0'
                    )}
                >
                    {title}
                </p>

                {!isSideMenuOpen && (
                    <span className="invisible absolute -right-2.5 z-50 inline-flex h-9 min-w-[8rem] translate-x-full items-center justify-center rounded-lg border bg-white text-xs font-semibold text-primary-600 opacity-0 transition delay-700 duration-200 before:absolute before:right-[100%] before:top-1/2 before:-translate-y-1/2 before:border-8 before:border-y-transparent before:border-l-transparent before:border-r-white before:content-[''] group-hover:visible group-hover:opacity-100">
                        {title}
                    </span>
                )}

                {title === t('My Cart') && itemCount > 0 && (
                    <span
                        className={cls(
                            'mr-2 inline-flex h-5 w-5 items-center justify-center rounded-full bg-primary-600 text-xs text-white transition-[margin,transform,opacity] duration-200',
                            isSideMenuOpen
                                ? 'scale-x-100 opacity-100'
                                : 'w-0 scale-x-0 opacity-0'
                        )}
                    >
                        {itemCount}
                    </span>
                )}
            </UiLink>
        </li>
    );
};

export default SideBarLink;

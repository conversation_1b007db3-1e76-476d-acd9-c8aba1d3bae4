import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useStore} from '@core/hooks';
import HeaderLogoPartial from './Logo';
import HeaderUserNavPartial from './UserNav';
import SearchBar from './SearchBar';

const HeaderPartial: FC = memo(() => {
    const store = useStore();

    return (
        <header className="shadow-small sticky top-0 z-50 hidden h-header w-full border-b bg-white xl:block">
            <div className="flex h-full items-stretch justify-between px-8">
                <HeaderLogoPartial />
                {!store.paymentOnly && <SearchBar />}
                <HeaderUserNavPartial />
            </div>
        </header>
    );
});

if (isDev) {
    HeaderPartial.displayName = 'HeaderPartial';
}

export default HeaderPartial;

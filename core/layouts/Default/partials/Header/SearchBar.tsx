import {
    ChangeEventHandler,
    FC,
    FocusEventHandler,
    Fragment,
    KeyboardEventHandler,
    memo,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {useRouter} from 'next/router';
import {cls, isDev, debounce, jsonRequest, toUpper, trim} from '@core/helpers';
import {useElementSize, useTrans} from '@core/hooks';
import {ProductSearchResultItem} from '@core/types';
import {
    UiButton,
    UiClickOutside,
    UiImage,
    UiInput,
    UiLink,
    UiSpinner,
    UiTransition
} from '@core/components/ui';
import {SearchIcon} from '@core/icons/regular';
import storeConfig from '~/store.config';
import EmptyResult from '@components/common/EmptyResult';
import {QrCodeIcon} from '@core/icons/solid';

const SearchResultItem: FC<{
    item: ProductSearchResultItem;
    onClick: () => void;
}> = memo(({item, onClick}) => {
    const t = useTrans();
    const description = useMemo(() => {
        if (item.type === 'product') {
            return t('Product');
        } else if (item.type === 'brand') {
            return t('Brand');
        } else if (item.type === 'navigation') {
            return t('Category');
        }

        return '';
    }, [t, item.type]);

    return (
        <UiLink
            className="hover:shadow-small group flex flex-shrink-0 cursor-pointer rounded-md border-0 px-2 py-2 transition hover:bg-secondary-100 focus:outline-none"
            href={`/${item.slug}`}
            onClick={onClick}
        >
            {typeof item.image === 'string' && item.image.length > 0 ? (
                <div
                    className={cls(
                        'h-12 flex-shrink-0 overflow-hidden rounded-md',
                        {
                            'w-9':
                                storeConfig.catalog.productImageShape ===
                                'rectangle',
                            'w-12':
                                storeConfig.catalog.productImageShape !==
                                'rectangle'
                        }
                    )}
                >
                    <UiImage
                        className="h-full w-full rounded-md"
                        src={item.image}
                        alt={item.name}
                        width={
                            storeConfig.catalog.productImageShape ===
                            'rectangle'
                                ? 36
                                : 48
                        }
                        height={48}
                        fit="cover"
                        position="center"
                    />
                </div>
            ) : (
                <div className="flex h-12 w-12 items-center justify-center bg-primary-50 text-xl font-semibold text-primary-600 transition group-hover:bg-primary-100">
                    {item.name
                        .split(' ')
                        .slice(0, 2)
                        .map(s => toUpper(s)[0])
                        .join('')}
                </div>
            )}

            <div className="ml-3 flex flex-1 flex-col justify-center">
                <div className="mb-1 text-sm font-medium">{item.name}</div>
                <div className="text-sm text-muted">{description}</div>
            </div>
        </UiLink>
    );
});

if (isDev) {
    SearchResultItem.displayName = 'SearchResultItem';
}

const SearchBar: FC = memo(() => {
    const router = useRouter();
    const t = useTrans();
    const [searchResult, setSearchResult] = useState<ProductSearchResultItem[]>(
        []
    );
    const [searchQuery, setSearchQuery] = useState('');
    const [isResultShown, setIsResultShown] = useState(false);
    const [isBlurInProgress, setIsBlurInProgress] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isSearchPageLoading, setIsSearchPageLoading] = useState(false);
    const {ref: containerRef, width: containerWidth} = useElementSize();
    const blurTimeoutIdx = useRef<any>();
    const inputRef = useRef<HTMLInputElement>(null);

    const searchDebounced = useRef(
        debounce(
            async (query: string) => {
                query = trim(query);

                try {
                    if (!(query === '' || query.length < 2)) {
                        const result = await jsonRequest({
                            url: '/api/catalog/search',
                            method: 'POST',
                            data: {searchQuery: query}
                        });

                        setSearchResult(() =>
                            result.map((item: ProductSearchResultItem) => ({
                                ...item,
                                name: !!item.brandName
                                    ? `${item.brandName} ${item.name}`
                                    : item.name
                            }))
                        );
                    } else {
                        setSearchResult([]);
                    }
                } catch (error: any) {}

                setIsLoading(false);
            },
            250,
            {leading: false, trailing: true}
        )
    );
    const onSearch: ChangeEventHandler<HTMLInputElement> = useCallback(e => {
        const query = e.target.value;

        setIsLoading(true);
        searchDebounced.current(query);
        setSearchQuery(query);
    }, []);
    const onFocus: FocusEventHandler<HTMLInputElement> = useCallback(e => {
        setIsBlurInProgress(false);
        setIsResultShown(true);

        try {
            clearTimeout(blurTimeoutIdx.current);
        } catch (error: any) {}
    }, []);
    const onBlur: FocusEventHandler<HTMLInputElement> = useCallback(e => {
        setIsBlurInProgress(true);
    }, []);
    useEffect(() => {
        if (isBlurInProgress) {
            blurTimeoutIdx.current = setTimeout(() => {
                setIsBlurInProgress(false);
            }, 200);
        }

        return () => {
            try {
                clearTimeout(blurTimeoutIdx.current);
            } catch (error: any) {}
        };
    }, [isBlurInProgress]);

    const onPopularSearchClick = useCallback((query: string) => {
        setIsLoading(true);

        query = trim(query);

        (async () => {
            try {
                const result = await jsonRequest({
                    url: '/api/catalog/search',
                    method: 'POST',
                    data: {searchQuery: query}
                });

                setSearchResult(() =>
                    result.map((item: ProductSearchResultItem) => ({
                        ...item,
                        name: !!item.brandName
                            ? `${item.brandName} ${item.name}`
                            : item.name
                    }))
                );
            } catch (error: any) {}

            setIsLoading(false);
            setSearchQuery(query);
        })();
    }, []);
    const onPopularCategoryClick = useCallback(() => {
        setIsResultShown(false);
        setSearchQuery('');
    }, []);
    const onSearchItemClick = useCallback(() => {
        if (trim(searchQuery).length > 2) {
            jsonRequest({
                url: '/api/catalog/save-popular-search',
                method: 'POST',
                data: {searchQuery}
            });
        }

        setIsResultShown(false);
        setSearchResult([]);
        setIsLoading(false);
        setSearchQuery('');
        setIsBlurInProgress(false);
        try {
            clearTimeout(blurTimeoutIdx.current);
        } catch (error: any) {}
        if (!!inputRef.current) {
            inputRef.current.blur();
        }
    }, [searchQuery]);

    const onGoToSearchDetail = useCallback(async () => {
        if (trim(searchQuery).length < 2) return;

        setIsSearchPageLoading(true);
        setIsResultShown(false);
        setSearchResult([]);
        setIsLoading(false);
        setSearchQuery('');
        setIsBlurInProgress(false);
        try {
            clearTimeout(blurTimeoutIdx.current);
        } catch (error: any) {}

        await router.push(
            `/search?${new URLSearchParams({query: searchQuery}).toString()}`
        );

        if (!!inputRef.current) {
            inputRef.current.blur();
        }
        setIsSearchPageLoading(false);
    }, [router, searchQuery]);
    const onKeyDown: KeyboardEventHandler<HTMLInputElement> = useCallback(
        e => {
            if (e.code === 'Enter' || e.code === 'NumpadAdd') {
                onGoToSearchDetail();
            }
        },
        [onGoToSearchDetail]
    );

    return (
        <>
            <div className="flex w-full max-w-xl">
                <div
                    ref={containerRef}
                    className="flex w-full items-center bg-white"
                >
                    <UiClickOutside
                        active={isResultShown}
                        onClick={() => setIsResultShown(false)}
                    >
                        <div
                            className={cls(
                                'relative w-full rounded-lg bg-white px-6 py-2',
                                {
                                    'z-dropdown rounded-b-none':
                                        isResultShown || isBlurInProgress
                                }
                            )}
                        >
                            <UiInput.Group size="lg" className="group w-full">
                                <UiInput.LeftElement>
                                    <SearchIcon className="h-4 w-4 stroke-gray-700 stroke-[24px] text-gray-700" />
                                </UiInput.LeftElement>

                                <UiInput
                                    placeholder={t(
                                        'Search product, category or brand..'
                                    )}
                                    className="shadow-small rounded-lg bg-secondary-100 pl-12 text-sm transition placeholder:text-sm focus:!border-primary-600 focus:bg-secondary-100"
                                    value={searchQuery}
                                    onChange={onSearch}
                                    onFocus={onFocus}
                                    onBlur={onBlur}
                                    onKeyDown={onKeyDown}
                                />

                                {isLoading ? (
                                    <UiInput.RightElement>
                                        <UiSpinner size="sm" />
                                    </UiInput.RightElement>
                                ) : (
                                    <UiInput.RightElement>
                                        <QrCodeIcon className="h-5 w-5 fill-primary-600" />
                                    </UiInput.RightElement>
                                )}
                            </UiInput.Group>

                            <UiTransition
                                as={Fragment}
                                enter="transition duration-200 ease-in-out"
                                enterFrom="opacity-0"
                                enterTo="opacity-100"
                                leave="transition duration-200 ease-in-out"
                                leaveFrom="opacity-100"
                                leaveTo="opacity-0"
                                show={isResultShown}
                            >
                                <div
                                    className="absolute left-0 right-0 top-full z-dropdown flex origin-top flex-col rounded-b-lg bg-white shadow-[0_31px_41px_0_#202a3533]"
                                    style={{
                                        width: `${containerWidth}px`
                                    }}
                                >
                                    {searchResult.length > 0 ? (
                                        <>
                                            <div className="scroller max-h-96 w-full overflow-y-auto p-2">
                                                {searchResult.map(item => (
                                                    <SearchResultItem
                                                        key={item.id}
                                                        item={item}
                                                        onClick={
                                                            onSearchItemClick
                                                        }
                                                    />
                                                ))}
                                            </div>
                                            <div className="border-t p-3">
                                                <UiButton
                                                    variant="outline"
                                                    color="primary"
                                                    size="sm"
                                                    className="w-full border-2"
                                                    onClick={onGoToSearchDetail}
                                                >
                                                    {t(
                                                        'SHOW ALL THE SEARCH RESULTS'
                                                    )}
                                                </UiButton>
                                            </div>
                                        </>
                                    ) : !isLoading &&
                                      searchQuery.length >= 2 ? (
                                        <div className="flex w-full flex-col items-center justify-center p-10">
                                            <div className="flex h-12 w-12 items-center justify-center rounded-lg border border-dashed border-gray-500 text-muted">
                                                <SearchIcon className="h-4 w-4" />
                                            </div>

                                            <h2 className="pt-5 text-center font-semibold text-base">
                                                {t('No result found!')}
                                            </h2>

                                            <p className="pt-2.5 text-center text-muted">
                                                {t(
                                                    'Please change your search criteria and try again.'
                                                )}
                                            </p>
                                        </div>
                                    ) : (
                                        <EmptyResult
                                            onPopularSearchClick={
                                                onPopularSearchClick
                                            }
                                            onPopularCategoryClick={
                                                onPopularCategoryClick
                                            }
                                        />
                                    )}
                                </div>
                            </UiTransition>
                        </div>
                    </UiClickOutside>
                </div>
            </div>

            <UiTransition
                show={isResultShown}
                as={Fragment}
                enter="ease-in-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in-out duration-200"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
            >
                <div className="fixed inset-0 z-20 bg-gray-900 bg-opacity-20 transition-opacity" />
            </UiTransition>

            <UiTransition
                show={isSearchPageLoading}
                as={Fragment}
                enter="transition ease-in-out duration-75"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="transition ease-in-out duration-75"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
            >
                <div className="fixed inset-0 z-dropdown bg-white bg-opacity-40 backdrop-blur-sm" />
            </UiTransition>
        </>
    );
});

if (isDev) {
    SearchBar.displayName = 'SearchBar';
}

export default SearchBar;

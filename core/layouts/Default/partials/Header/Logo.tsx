import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import storeConfig from '~/store.config';
import {UiImage, UiLink} from '@core/components/ui';

const HeaderLogoPartial: FC = memo(() => {
    return (
        <div className="flex items-center">
            <UiLink
                href="/"
                className="h-logo cursor-pointer"
                aria-label="Logo"
            >
                <UiImage
                    src="/site-logo.png"
                    alt={storeConfig.title}
                    width={parseFloat(
                        storeConfig.theme.logoWidth.replace('px', '')
                    )}
                    height={parseFloat(
                        storeConfig.theme.logoHeight.replace('px', '')
                    )}
                    priority
                />
            </UiLink>
        </div>
    );
});

if (isDev) {
    HeaderLogoPartial.displayName = 'HeaderLogoPartial';
}

export default HeaderLogoPartial;

import {FC, memo} from 'react';
import {cls, isDev} from '@core/helpers';
import {useLayout, useTrans} from '@core/hooks';
import {UiImage} from '@core/components/ui';

const FooterPartial: FC = memo(() => {
    const t = useTrans();
    const {isSideMenuOpen} = useLayout();

    return (
        <footer
            className={cls(
                'bg-secondary-100 transition-[margin] duration-200 xl:px-4',
                isSideMenuOpen ? 'xl:ml-64' : 'xl:ml-20'
            )}
        >
            <div className="container">
                <div className="flex select-none flex-col items-center justify-center border-t py-4 text-sm text-muted xl:flex-row xl:items-center xl:justify-between xl:pb-4">
                    <div className="mb-3 whitespace-nowrap text-left xl:mb-0">
                        &copy; {new Date().getFullYear()}{' '}
                        {t('All Rights Reserved')}
                    </div>

                    <div className="flex flex-col items-center gap-4 md:flex-row">
                        <a
                            href="https://entererp.com/"
                            rel="noopener noreferrer"
                            target="_blank"
                            title="EnterERP Logo"
                        >
                            <UiImage
                                className="h-auto w-24"
                                src={require('@assets/images/footer/entererp.svg')}
                                alt="EnterERP Logo"
                            />
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    );
});

if (isDev) {
    FooterPartial.displayName = 'FooterPartial';
}

export default FooterPartial;

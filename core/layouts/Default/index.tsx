import {FC, memo, PropsWithChildren} from 'react';
import {cls, isDev} from '@core/helpers';
import {useLayout, useMobileViewportDifference} from '@core/hooks';

import HeaderPartial from './partials/Header';
import FooterPartial from './partials/Footer';
import SideBarPartial from './partials/SideBar';
import {useRouter} from 'next/router';

const DefaultLayout: FC<PropsWithChildren> = memo(({children}) => {
    const {isSideMenuOpen} = useLayout();
    const viewportDifference = useMobileViewportDifference();
    const router = useRouter();
    return (
        <div className="relative w-full bg-white xl:flex xl:min-h-screen xl:flex-col">
            <HeaderPartial />

            <SideBarPartial />

            <main
                className={cls(
                    'bg-secondary-100 py-4 transition-[margin] duration-200 xl:!min-h-full xl:flex-1 xl:py-0',
                    isSideMenuOpen ? 'xl:ml-64' : 'xl:ml-20'
                )}
                style={{
                    minHeight: `calc(100vh - ${viewportDifference}px)`
                }}
            >
                <div className="container xl:p-4">{children}</div>
            </main>

            {router.pathname === '/checkout' ? null : <FooterPartial />}
        </div>
    );
});

if (isDev) {
    DefaultLayout.displayName = 'DefaultLayout';
}

export default DefaultLayout;

import {FC, memo, PropsWithChildren} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiImage, UiLink} from '@core/components/ui';
import AuthBg from '@assets/images/common/AuthBackground.png';

const AuthLayout: FC<PropsWithChildren> = memo(({children}) => {
    const t = useTrans();

    return (
        <div
            style={{
                backgroundImage: `url(${AuthBg.src})`,
                backgroundPosition: 'top center',
                backgroundSize: 'cover',
                backgroundRepeat: 'no-repeat'
            }}
            className="flex h-full w-full flex-col items-center xl:h-screen"
        >
            <main className="auth-page w-full flex-1">{children}</main>

            <footer className="fixed bottom-0 flex w-full flex-col items-center justify-between gap-2.5 p-4 xl:flex-row">
                <UiImage
                    className="h-6 w-auto md:h-10"
                    src={require('@assets/images/common/pay-logos.png')}
                    alt="EnterERP Logo"
                />
                <div className="flex flex-wrap items-center justify-center gap-4 text-sm">
                    <UiLink href="/preliminary-information-form">
                        {t('Preliminary Information Form')}
                    </UiLink>
                    <UiLink href="/service-agreement">
                        {t('Service Agreement')}
                    </UiLink>
                    <UiLink href="/contact">{t('Contact')}</UiLink>
                    <a
                        href="https://entererp.com/"
                        rel="noopener noreferrer"
                        target="_blank"
                        title="EnterERP Logo"
                    >
                        <UiImage
                            className="h-6 w-auto"
                            src={require('@assets/images/footer/entererp.svg')}
                            alt="EnterERP Logo"
                        />
                    </a>
                </div>
            </footer>
        </div>
    );
});

if (isDev) {
    AuthLayout.displayName = 'AuthLayout';
}

export default AuthLayout;
